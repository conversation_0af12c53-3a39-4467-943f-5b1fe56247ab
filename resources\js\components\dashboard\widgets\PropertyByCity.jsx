import React from "react";
const PropertyByCity = ({ widget, handleConfigUpdate,handleSubmit }) => {
    return (
        <form
          onSubmit={handleSubmit}
          className="space-y-4"
        >
            <div className="flex gap-4">
                <div className="flex-1">
                    <label>
                     Title
                    </label>
                    <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                    value={widget.config?.title || ""}
                    onChange={(e) =>
                      handleConfigUpdate(widget.id, "title", e.target.value)
                    }
                    placeholder="Properties By Cities"
                    />
                 </div>
                 <div className="flex-1 gap-4">
                    <label>
                     Sub Title
                    </label>
                    <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                    value={widget.config?.subTitle || ""}
                    onChange={(e) =>
                      handleConfigUpdate(widget.id, "subTitle", e.target.value)
                    }
                    placeholder="Here are some of the featured Apartment in different categories"
                    />
                 </div>
            </div>
            <div className="flex gap-4">
                <div className="flex-1 gap-4">
                      <label>
                        Order By
                      </label>
                      <select
                        className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                        value={widget.config?.orderBy || ""}
                        onChange={(e) =>
                          handleConfigUpdate(widget.id, "orderBy", e.target.value)
                        }
                      >
                        <option value="Descending">Newest</option>
                        <option value="Ascending">Oldest</option>
                      </select>
                </div>
                <div className="flex-1 gap-4">
                      <label>
                        On Each View
                      </label>
                      <input
                      type="number"
                      className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                      value={widget.config?.onEachView || ""}
                      onChange={(e) =>
                        handleConfigUpdate(widget.id, "onEachView", e.target.value)
                      }
                      />
                </div>
            </div>
            
        </form>
    );
};
export default PropertyByCity;
