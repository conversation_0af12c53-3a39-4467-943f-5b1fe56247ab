import React,{useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Plus, Eye, Edit, Trash2 } from "lucide-react";
import widgetsAPI from '@/services/widgetsAPI';



export default function Widgets() {
  const [widgets, setWidgets] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const fetchWidgets = async () => {
    try {
      const response = await widgetsAPI.getAll();
      setWidgets(response.data);
      setLoading(false);
    } catch (err) {
      setError(err.message);
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchWidgets();
  }, []);

  if (loading) {
    return (
      <div className="mx-auto p-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Widgets</h1>
            <p className="text-gray-600">Loading widgets...</p>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(3)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse">
                  <div className="h-4 bg-gray-200 rounded w-3/4 mb-4"></div>
                  <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                  <div className="flex gap-2">
                    <div className="h-8 bg-gray-200 rounded w-16"></div>
                    <div className="h-8 bg-gray-200 rounded w-16"></div>
                    <div className="h-8 bg-gray-200 rounded w-16"></div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mx-auto p-6 space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Widgets</h1>
            <p className="text-gray-600">Error loading widgets</p>
          </div>
        </div>
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-red-600 mb-4">Failed to load widgets: {error}</p>
            <Button onClick={fetchWidgets} variant="outline">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="mx-auto p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Widgets</h1>
          <p className="text-gray-600">Manage your widgets</p>
        </div>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Widget
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {widgets && widgets.length > 0 ? (
          widgets.map((widget) => (
            <Card key={widget.id}>
              <CardContent className="p-6">
                <div className="flex justify-between items-start mb-4">
                   <img
                      src={widget.image}
                      alt={`${widget.name} icon`}
                      className="h-8 w-8 object-contain mr-4 flex-shrink-0"
                    />
                      <br />
                  <h3 className="text-lg font-semibold">{widget.name}</h3>
                  <Badge variant={widget.status === "active" ? "default" : "secondary"}>
                    {widget.status}
                  </Badge>
                </div>
                <p className="text-2xl font-bold mb-4">{widget.value}</p>
                
              </CardContent>
            </Card>
          ))
        ) : (
          <Card className="col-span-full">
            <CardContent className="p-6 text-center">
              <p className="text-gray-600 mb-4">No widgets found</p>
              <Button>
                <Plus className="h-4 w-4 mr-2" />
                Create Your First Widget
              </Button>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
