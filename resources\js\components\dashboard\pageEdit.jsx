import React, { useEffect, useState, useRef } from "react";
import { useParams, useNavigate } from "react-router-dom";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { ArrowLeft, Save, Loader2, Plus } from "lucide-react";
import { showAlertMethods as showAlert } from "@/utils/sweetAlert";
import menuAPI from "@/services/menuAPI";
import widgetsAPI from "@/services/widgetsAPI";
import pageContentAPI from "@/services/pageContentAPI";
import PageContent from "./pageContent";

const ITEM_TYPE = "WIDGET";

export default function EditPage() {
  const { id } = useParams();
  const navigate = useNavigate();  

  const [widgets, setWidgets] = useState([]);
  const [droppedWidgets, setDroppedWidgets] = useState([]);
  const [pageData, setPageData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [openAccordion, setOpenAccordion] = useState(null);

  const [formData, setFormData] = useState({
      name: "",
      slug: "",
      description: "",
      status: "active",
  });

  // ===============================
  // Draggable Widget Component
  // ===============================
  const DraggableWidgetCard = ({ widget, index }) => {
    const ref = useRef(null);
    const [{ isDragging }, drag] = useDrag({
      type: ITEM_TYPE,
      item: { id: widget.id }, // Remove index for sidebar widgets to distinguish from content area widgets
      collect: (monitor) => ({
        isDragging: monitor.isDragging()
      }),
    });

    drag(ref);

    return (
      <Card
        ref={ref}
        style={{ opacity: isDragging ? 0.5 : 1, cursor: "grab", marginBottom: "8px" }}
      >
        <CardContent className="pt-[5px] pb-[5px] pl-[5px] pr-[5px] flex flex-col items-center justify-center text-center">
          <img src={widget.image} alt={widget.name} className="mb-2" />
          <h3 className="!font-extralight text-base">{widget.name}</h3>
          <p className="text-2xl font-bold">{widget.value}</p>
        </CardContent>
      </Card>
    );
  };

  // ===============================
  // Drop Widget Handler (Fallback for general area)
  // ===============================
  const [{ isOver }, drop] = useDrop({
    accept: ITEM_TYPE,
    drop: (item, monitor) => {
      // Only handle drop if it wasn't handled by a specific drop zone
      const didDrop = monitor.didDrop();
      if (!didDrop && item?.id && !droppedWidgets.find(w => w.id === item.id)) {
        // Add to end as fallback
        onNewWidgetDrop(item, droppedWidgets.length);
      }
    },
    collect: (monitor) => ({ isOver: monitor.isOver() }),
  });

  // ===============================
  // CRUD Handlers
  // ===============================
    const removeWidget = (widgetId) => {
    setDroppedWidgets(prev => prev.filter(widget => widget.id !== widgetId));
  };

  const onNewWidgetDrop = (draggedItem, dropIndex) => {
    console.log('onNewWidgetDrop called:', draggedItem, 'dropIndex:', dropIndex); // Debug log
    
    // Check if widget already exists
    if (!droppedWidgets.find(w => w.id === draggedItem.id)) {
      const widget = widgets.find(w => w.id === draggedItem.id);
      if (widget) {
        console.log('Found widget:', widget, 'inserting at index:', dropIndex); // Debug log
        
        const newWidget = { 
          ...widget, 
          config: { 
            title: widget.name, 
            subtitle: "", 
            details: "" 
          },
          tempId: Date.now() // Temporary ID for new widgets
        };

        // Insert the widget at the specified position
        setDroppedWidgets(prev => {
          const updated = [...prev];
          updated.splice(dropIndex, 0, newWidget);
          console.log('Updated widgets array:', updated); // Debug log
          return updated;
        });

        // Open the newly added widget
        setOpenAccordion(`item-${dropIndex}`);
      }
    } else {
      console.log('Widget already exists, not adding duplicate'); // Debug log
    }
  };

  const reorderWidget = async (dragIndex, dropIndex) => {
    // Update local state immediately for smooth UI
    const updatedWidgets = [...droppedWidgets];
    const draggedWidget = updatedWidgets[dragIndex];
    updatedWidgets.splice(dragIndex, 1);
    updatedWidgets.splice(dropIndex, 0, draggedWidget);
    
    setDroppedWidgets(updatedWidgets);

    // Update the database with the new order
    try {
      const contentIds = updatedWidgets.map(widget => widget.pageContentId).filter(Boolean);
      if (contentIds.length > 0) {
        await pageContentAPI.reorder(id, contentIds);
      }
    } catch (error) {
      console.error('Failed to update widget order:', error);
      // Revert the local state change on error
      setDroppedWidgets(droppedWidgets);
      showAlert.error("Error", "Failed to update widget order");
    }
  };

  const updateWidgetConfig = (widgetId, field, value) => {
    setDroppedWidgets(prev =>
      prev.map(w => w.id === widgetId ? { ...w, config: { ...w.config, [field]: value } } : w)
    );
  };

  const handleImageChange = async (widgetId, field, event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      // Create a preview URL for immediate display
      const previewUrl = URL.createObjectURL(file);
      updateWidgetConfig(widgetId, field, previewUrl);

      // Upload the file to server using multipart
      const formData = new FormData();
      formData.append('image', file);
      formData.append('widget_id', widgetId);
      formData.append('field_name', field);

      showAlert.loading('Uploading image...', 'Please wait while we upload your image');

      const response = await pageContentAPI.uploadImage(formData);

      if (response.success) {
        // Update with the server URL
        updateWidgetConfig(widgetId, field, response.data.image_url);
        showAlert.close();
        showAlert.success('Success', 'Image uploaded successfully');
      } else {
        showAlert.close();
        showAlert.error('Error', response.message || 'Failed to upload image');
        // Revert to no image on error
        updateWidgetConfig(widgetId, field, '');
      }
    } catch (error) {
      console.error('Image upload error:', error);
      showAlert.close();
      showAlert.error('Error', 'Failed to upload image');
      // Revert to no image on error
      updateWidgetConfig(widgetId, field, '');
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // Add widget to dropped widgets with better handling
  const addWidgetToPage = (widget) => {
    const newWidget = {
      ...widget,
      config: {
        title: widget.name,
        subtitle: "",
        details: ""
      },
      tempId: Date.now() // Temporary ID for new widgets
    };
    setDroppedWidgets(prev => [...prev, newWidget]);
    setOpenAccordion(`item-${droppedWidgets.length}`);
  };

  const handleSave = async () => {
    if (!formData.name.trim()) {
      showAlert.error("Validation Error", "Page name is required");
      return;
    }

    try {
      setSaving(true);

      // Fallback: use existing slug if formData.slug is empty
      const payload = {
        ...formData,
        slug: formData.slug || (pageData && pageData.slug) || ""
      };

      // Update page/menu data first
      await menuAPI.update(id, payload);

      // Prepare page content data with proper structure
      const contentsToSave = droppedWidgets.map((widget, index) => ({
        widget_id: widget.id,
        pageContent: widget.config || {},
        order_index: index
      }));

      // Save page content data
      if (contentsToSave.length > 0) {
        await pageContentAPI.save(id, contentsToSave);
      } else {
        // If no widgets, clear all page contents
        await pageContentAPI.deleteByPageId(id);
      }

      showAlert.success("Success", "Page updated successfully");

      // Optionally navigate back or stay on page
      // navigate("/menu"); // Uncomment if you want to navigate back

    } catch (err) {
      console.error('Save error:', err);
      const errorMessage = err.message || err.response?.data?.message || "Failed to update page";
      showAlert.error("Error", errorMessage);
    } finally {
      setSaving(false);
    }
  };

  // Save individual widget changes (auto-save functionality)
  const handleAutoSave = async (widgetId, field, value) => {
    try {
      updateWidgetConfig(widgetId, field, value);

      // Find the widget
      const widget = droppedWidgets.find(w => w.id === widgetId);
      if (!widget) return;

      // If widget has pageContentId, update it; otherwise create new
      if (widget.pageContentId) {
        await pageContentAPI.update(widget.pageContentId, {
          pageContent: { ...widget.config, [field]: value }
        });
      }
      // Note: For new widgets, we'll save them when the main save is called

    } catch (error) {
      console.error('Auto-save error:', error);
      // Don't show error alert for auto-save failures to avoid annoying the user
    }
  };

  const handleBack = () => navigate("/admin/pages");

  // ===============================
  // API Calls
  // ===============================
  const fetchWidgets = async () => {
    try {
      const response = await widgetsAPI.getAll();
      setWidgets(response.data);
    } catch {
      showAlert.error("Error", "Failed to load widgets");
    } finally {
      setLoading(false);
    }
  };

  const fetchPageContents = async () => {
    try {
      const response = await pageContentAPI.getByPageId(id);

      // Handle both old and new response formats
      const contents = response.data || response;

      if (!Array.isArray(contents)) {
        console.warn('Page contents response is not an array:', contents);
        setDroppedWidgets([]);
        return;
      }

      const dropped = contents.map(content => {
        const widget = widgets.find(w => w.id === content.widget_id);
        if (!widget) {
          console.warn(`Widget with ID ${content.widget_id} not found`);
          return null;
        }

        return {
          ...widget,
          config: content.pageContent || {},
          pageContentId: content.id, // Store the page content ID for updates/deletes
          orderIndex: content.order_index || 0
        };
      })
      .filter(Boolean)
      .sort((a, b) => (a.orderIndex || 0) - (b.orderIndex || 0)); // Sort by order_index

      setDroppedWidgets(dropped);
    } catch (err) {
      console.error("Failed to load page contents:", err);
      // Don't show error alert as page contents might not exist yet
      setDroppedWidgets([]);
    }
  };

  const fetchPage = async () => {
    try {
      setLoading(true);
      const response = await menuAPI.getById(id);
      const data = response.data;
      setPageData(data);
      setFormData({
        name: data.name || "",
          slug: data.slug || "",
        description: data.description || "",
        status: data.status || "active",
        content: data.content || ""
      });
    } catch (err) {
      showAlert.error("Error", "Failed to load page data");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => { if (id) fetchPage(); }, [id]);
  useEffect(() => { fetchWidgets(); }, []);
  useEffect(() => {
    if (id && widgets.length > 0) {
      fetchPageContents();
    }
  }, [id, widgets]);

 

  // ===============================
  // Render Loading / Error States
  // ===============================
  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading page...</span>
      </div>
    );
  }

  if (!pageData) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen">
        <h2 className="text-2xl font-bold text-gray-900 mb-4">Page not found</h2>
        <Button onClick={handleBack}>
          <ArrowLeft className="h-4 w-4 mr-2" /> Back to Pages
        </Button>
      </div>
    );
  }

  // ===============================
  // Main Render
  // ===============================
  return (
    <DndProvider backend={HTML5Backend}>
      <div className="mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBack}>
              <ArrowLeft className="h-4 w-4 mr-2" /> Back
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Edit Page</h1>
              <p className="text-gray-600">Modify page details and content</p>
            </div>
          </div>
          <Button onClick={handleSave} disabled={saving}>
            {saving ? <Loader2 className="h-4 w-4 mr-2 animate-spin" /> : <Save className="h-4 w-4 mr-2" />}
            {saving ? "Saving..." : "Save Changes"}
          </Button>
        </div>

        {/* Page Layout */}
        <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
          {/* Widgets List */}
          <div className="md:col-span-4">
            <Card>
              <CardHeader>
                <CardTitle>Widgets</CardTitle>
              </CardHeader>
              <CardContent className="max-h-[600px] overflow-auto grid grid-cols-2 gap-4">
                {widgets.length > 0 ? (
                  widgets.map((widget, index) => (
                    <DraggableWidgetCard key={widget.id} widget={widget} index={index} />
                  ))
                ) : (
                  <Card>
                    <CardContent className="p-6 text-center">
                      <p className="text-gray-600 mb-4">No widgets found</p>
                      <Button>
                        <Plus className="h-4 w-4 mr-2" /> Create Your First Widget
                      </Button>
                    </CardContent>
                  </Card>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Page Content */}
          <div className="md:col-span-8">
            
                <PageContent
                  droppedWidgets={droppedWidgets}
                  updateWidgetConfig={updateWidgetConfig}
                  removeWidget={removeWidget}
                  isOver={isOver}
                  drop={drop}
                  handleImageChange={handleImageChange}
                  reorderWidget={reorderWidget}
                  onNewWidgetDrop={onNewWidgetDrop}
                />
             
          </div>
        </div>
      </div>
    </DndProvider>
  );
}
