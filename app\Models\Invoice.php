<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Invoice extends Model
{
    protected $fillable = [
        'invoice_type',
        'invoice_number',
        'invoice_date',
        'customer_id',
        'tenant_id',
        'property_id',
        'grand_total',
        'discount',
        'is_percentage',
        'amount',
  
        
        'terms_and_conditions',
        'tax_amount',
        'total_amount',
        'payment_method_id',
        'payment_Type_id',
        'payment_amount',
        'due_amount',
        'payment_status_id',
        'due_date',
        'notes',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'invoice_date' => 'date',
        'due_date' => 'date',
        'billing_date' => 'date',
        'billing_end' => 'date',
        'payment_date' => 'date',
        'amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'discount_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    // Accessor to provide ammount field for frontend compatibility
    public function getAmmountAttribute()
    {
        return $this->amount;
    }

    // Mutator to handle ammount field from frontend
    public function setAmmountAttribute($value)
    {
        $this->attributes['amount'] = $value;
    }

    // Relationships
    public function tenant()
    {
        return $this->belongsTo(Tanants::class);
    }
    
   public function paymentStatus()
    {
        return $this->belongsTo(PaymentStatus::class, 'payment_status_id');
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }

  
    public function property()
    {
        return $this->belongsTo(Project::class);
    }

    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    

    public function items()
    {
        return $this->hasMany(InvoiceItem::class);
    }

    public function propertyItems()
    {
        return $this->hasMany(PropertyItem::class);
    }

    // Scopes
    public function scopePaid($query)
    {
        return $query->whereHas('paymentStatus', function($q) {
            $q->where('name', 'Paid');
        });
    }

    public function scopePending($query)
    {
        return $query->whereHas('paymentStatus', function($q) {
            $q->where('name', 'Pending');
        });
    }

    public function scopeUnpaid($query)
    {
        // Alias for pending for backward compatibility
        return $this->scopePending($query);
    }

    public function scopeOverdue($query)
    {
        return $query->whereHas('paymentStatus', function($q) {
            $q->where('name', 'Pending');
        })->where('due_date', '<', now());
    }

    public function scopeFailed($query)
    {
        return $query->whereHas('paymentStatus', function($q) {
            $q->where('name', 'Failed');
        });
    }

    public function scopeRefunded($query)
    {
        return $query->whereHas('paymentStatus', function($q) {
            $q->where('name', 'Refunded');
        });
    }

    public function scopeCancelled($query)
    {
        return $query->whereHas('paymentStatus', function($q) {
            $q->where('name', 'Cancelled');
        });
    }
}
