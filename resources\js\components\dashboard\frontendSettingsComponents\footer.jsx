import React from "react";
import { TabsContent } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const Footer = ({ formData, handleInputChange, handleSubmit, loading, handleFooterLogoFileChange, footerLogoPreview }) => {
    const handleFooterLogoFileChangeLocal = (event) => {
        const file = event.target.files[0];
        if (file) {
            // Pass the file to parent component
            handleFooterLogoFileChange(file);
        }
    };

    return (
        <TabsContent value="footer" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
                <div>
                    <Label htmlFor="footerLogo">Footer Logo</Label>
                    <div className="space-y-2">
                        <Input
                            id="footerLogo"
                            type="file"
                            accept="image/*"
                            onChange={handleFooterLogoFileChangeLocal}
                            className=""
                        />

                        {footerLogoPreview && (
                            <div className="mt-4">
                                <Label className="text-sm font-medium">Preview:</Label>
                                <div className="mt-2 border rounded-lg p-2 bg-gray-50">
                                    <img
                                        src={footerLogoPreview}
                                        alt="Footer Logo preview"
                                        className="max-w-full max-h-32 mx-auto rounded"
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                <div>
                    <Label htmlFor="footerPhone">Footer Phone</Label>
                    <Input
                        id="footerPhone"
                        value={formData.footerPhone || ""}
                        onChange={(e) => handleInputChange('footerPhone', e.target.value)}
                        placeholder="+990-737 621 432"
                    />
                </div>
                <div>
                    <Label htmlFor="footerEmail">Email</Label>
                    <Input
                        id="footerEmail"
                        value={formData.footerEmail || ""}
                        onChange={(e) => handleInputChange('footerEmail', e.target.value)}
                        placeholder="<EMAIL>"
                    />
                </div>
                <div>
                    <Label htmlFor="copyRightText">Copyright Text</Label>
                    <Input
                        id="copyRightText"
                        value={formData.copyRightText || ""}
                        onChange={(e) => handleInputChange('copyRightText', e.target.value)}
                        placeholder="© 2024 Your Company. All rights reserved."
                    />
                </div>
            </div>

             <div className="flex justify-end">
                <button
                onClick={handleSubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
                disabled={loading}
                >
                {loading ? "Saving..." : "Save Settings"}
                </button>
            </div>
        </TabsContent>
    );
};

export default Footer;
