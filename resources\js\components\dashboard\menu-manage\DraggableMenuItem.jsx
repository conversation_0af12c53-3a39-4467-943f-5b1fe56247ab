import React from 'react';
import { useDrag, useDrop } from 'react-dnd';
import { Button } from "@/components/ui/button";
import { Trash, GripVertical, ArrowRight } from "lucide-react";

const DraggableMenuItem = ({ item, index, moveItem, onDelete, level = 0, allItems = [], onDragStart, onDragEnd }) => {
  const [{ isDragging }, drag, preview] = useDrag(() => ({
    type: 'MENU_ITEM',
    item: () => {
      if (onDragStart) onDragStart();
      return { 
        id: item.id, 
        index, 
        title: item.title,
        type: item.type,
        parent_id: item.parent_id,
        order: item.order
      };
    },
    end: () => {
      if (onDragEnd) onDragEnd();
    },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  }), [item, index, onDragStart, onDragEnd]);

  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'MENU_ITEM',
    drop: (draggedItem) => {
      if (draggedItem.index !== index) {
        moveItem(draggedItem.index, index);
      }
    },
    hover: (draggedItem) => {
      if (draggedItem.index !== index) {
        moveItem(draggedItem.index, index);
        draggedItem.index = index;
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }), [index, moveItem]);

  const opacity = isDragging ? 0.5 : 1;
  const backgroundColor = isOver && canDrop ? 'bg-blue-100' : '';
  
  // Calculate indentation based on level
  const indentClass = level > 0 ? `ml-${level * 8}` : '';
  const borderLeft = level > 0 ? 'border-l-4 border-gray-300' : '';
  
  // Check if this item has children
  const hasChildren = allItems.some(childItem => childItem.parent_id === item.id);

  return (
    <div className="relative">
      {/* Drop indicator - shows above current item when hovering */}
      {isOver && canDrop && (
        <div className={`absolute -top-1 left-0 right-0 h-0.5 bg-blue-500 z-10 rounded-full ${indentClass}`}>
          <div className="absolute -top-1 -left-1 w-2 h-2 bg-blue-500 rounded-full"></div>
          <div className="absolute -top-1 -right-1 w-2 h-2 bg-blue-500 rounded-full"></div>
        </div>
      )}
      
      <div
        ref={(node) => drag(preview(drop(node)))}
        style={{ opacity }}
        className={`flex items-center justify-between p-3 border rounded-lg transition-all duration-200 cursor-move ${backgroundColor} ${indentClass} ${borderLeft} ${
          isDragging 
            ? 'bg-blue-50 border-blue-300 shadow-lg transform rotate-1 scale-105' 
            : 'hover:bg-muted/50 hover:shadow-sm hover:border-blue-200'
        } ${isOver && canDrop ? 'border-blue-300 shadow-md' : ''} ${
          level > 0 ? 'bg-gray-50' : ''
        }`}
        title="Click and drag to reorder this menu item or drag right to nest under another item"
      >
        <div className="flex items-center mr-3 text-gray-400 transition-colors pointer-events-none">
          <GripVertical className="h-5 w-5" />
          {level > 0 && (
            <ArrowRight className="h-4 w-4 ml-1 text-gray-500" />
          )}
        </div>
        
        <div className="flex-1 pointer-events-none">
          <div className={`font-medium ${level > 0 ? 'text-sm' : ''}`}>
            {level > 0 && '└─ '}
            {item.title}
          </div>
          <div className="text-sm text-muted-foreground">
            Type: {item.type} • Order: {item.order}
            {level > 0 && ' • Child Item'}
            {hasChildren && ' • Has Children'}
            {item.slug && ` • Slug: ${item.slug}`}
          </div>
        </div>
        
        <div className="flex gap-2">
          {level > 0 && (
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                if (typeof moveItem === 'function') {
                  // Unnest: set parent_id to null and move to top
                  moveItem(index, 0, { unnest: true });
                }
              }}
              className="text-blue-600 hover:text-blue-700 pointer-events-auto relative z-10"
            >
              Unnest
            </Button>
          )}
          <Button
            variant="outline"
            size="sm"
            onClick={(e) => {
              e.stopPropagation();
              onDelete(item.id);
            }}
            className="text-red-600 hover:text-red-700 pointer-events-auto relative z-10"
          >
            <Trash className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DraggableMenuItem;