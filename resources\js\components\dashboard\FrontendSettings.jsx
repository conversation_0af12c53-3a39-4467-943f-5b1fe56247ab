import React, { useState, useEffect } from "react";
import { Ta<PERSON>,  TabsList, TabsTrigger } from "@/components/ui/tabs";
import GoogleAnalytics from "./frontendSettingsComponents/GoogleAnalytics";
import frontendSettingsAPI from "../../services/frontendSettingsAPI";
import GeneralHeaderSettings from "./frontendSettingsComponents/generalHeaderSettings";
import ThemeColor from "./frontendSettingsComponents/themeColor";
import Header from "./frontendSettingsComponents/header";
import Footer from "./frontendSettingsComponents/footer";
import BreadCums from "./frontendSettingsComponents/breadCums";
import SEO from "./frontendSettingsComponents/seo";
import GoogleLogin from "./frontendSettingsComponents/googleLogin";

const FrontendSettings = () => {
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState({
    logo: "",
    phone: "",
    addPropertyButton: 0,
    showPreloader: 0,
    showTopBar: 0,
    btnUrl: "",
    primaryColor: "",
    secondaryColor: "",
    footerLogo: "",
    footerEmail: "",
    footerPhone: "",
    copyRightText: "",
    facebook: "",
    twitter: "",
    linkedin: "",
    youtube: "",
    instagram: "",
    MetaTitle: "",
    meta_key_word: [],
    metaDescription: ""
  });
  const [logoPreview, setLogoPreview] = useState(null);
  const [selectedLogoFile, setSelectedLogoFile] = useState(null);
  const [footerLogoPreview, setFooterLogoPreview] = useState(null);
  const [selectedFooterLogoFile, setSelectedFooterLogoFile] = useState(null);
  const [breadcumImagePreview, setBreadcumImagePreview] = useState(null);
  const [selectedBreadCumImageFile, setSelectedBreadCumImageFile] = useState(null);
  const [selectedMetaImageFile, setSelectedMetaImageFile] = useState(null);
  const [metaImagePreview, setMetaImagePreview] = useState(null);

  // Load data on component mount
  useEffect(() => {
    const loadSettings = async () => {
      try {
        const response = await frontendSettingsAPI.getAll();
        if (response && response.data) {
          setFormData({
            logo: response.data.logo || "",
            phone: response.data.phone || "",
            addPropertyButton: response.data.addPropertyButton || 0,
            showTopBar: response.data.showTopBar || 0,
            btnUrl: response.data.btnUrl || "",
            primaryColor: response.data.primaryColor || "",
            secondaryColor: response.data.secondaryColor || "",
            showPreloader: response.data.showPreloader || 0,
            footerLogo: response.data.footerLogo || "",
            footerEmail: response.data.footerEmail || "",
            footerPhone: response.data.footerPhone || "",
            copyRightText: response.data.copyRightText || "",
            facebook: response.data.facebook || "",
            twitter: response.data.twitter || "",
            linkedin: response.data.linkedin || "",
            youtube: response.data.youtube || "",
            instagram: response.data.instagram || "",
            breadcumImage: response.data.BreadcumImage || "",
            breadcumColor: response.data.breadcumColor || "",
            MetaImage: response.data.metaImage || "",
            MetaTitle: response.data.MetaTitle || "",
            meta_key_word: response.data.meta_key_word ? response.data.meta_key_word.split(',') : [],
            metaDescription: response.data.metaDescription || "",
            googleAnalytics: response.data.googleAnalytics || "",
            googleClientID: response.data.googleClientID || "",
            googleClientSecret: response.data.googleClientSecret || "",
            googleRedirectionUrl: response.data.googleRedirectionUrl || "",
            googleLoginClientId: response.data.googleLoginClientId || ""
          });
          // Set logo preview if logo URL exists
          if (response.data.logo) {
            setLogoPreview(response.data.logo);
          }
          // Set footer logo preview if footerLogo URL exists
          if (response.data.footerLogo) {
            setFooterLogoPreview(response.data.footerLogo);
          }
          // Set breadcum image preview if breadcumImage URL exists
          if (response.data.BreadcumImage) {
            setBreadcumImagePreview(response.data.BreadcumImage);
          }

          // Set meta image preview if metaImage URL exists
          if (response.data.metaImage) {
            setMetaImagePreview(response.data.metaImage);
          }
        }
      } catch (error) {
        console.error('Error loading frontend settings:', error);
        // Keep default values if no data exists
      }
    };

    loadSettings();
  }, []);

  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleLogoFileChange = (file) => {
    setSelectedLogoFile(file);
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => setLogoPreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  const handleFooterLogoFileChange = (file) => {
    setSelectedFooterLogoFile(file);
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => setFooterLogoPreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

  const handleBreadcumImageFileChange = (file) => {
    setSelectedBreadCumImageFile(file);
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => setBreadcumImagePreview(e.target.result);
      reader.readAsDataURL(file);
    }
  };

      const handleMetaImageFileChange = (event) => {
          const file = event.target.files[0];
          if (file) {
              setSelectedMetaImageFile(file);
              // Create preview URL
              const reader = new FileReader();
              reader.onload = (e) => {
                  setMetaImagePreview(e.target.result);
              };
              reader.readAsDataURL(file);
          }
      };

  // Global handleSubmit function that can access all state
  const handleSubmit = async () => {
    setLoading(true);
    try {
      const formDataToSend = new FormData();

      // Append logo if selected
      if (selectedLogoFile) {
        formDataToSend.append("logo", selectedLogoFile);
      }

      // Append footer logo if selected
      if (selectedFooterLogoFile) {
        formDataToSend.append("footerLogo", selectedFooterLogoFile);
      }

      // Append breadcum image if selected
      if (selectedBreadCumImageFile) {
        formDataToSend.append("BreadcumImage", selectedBreadCumImageFile);
      }

        // Append meta image if selected
      if (selectedMetaImageFile) {
        formDataToSend.append("metaImage", selectedMetaImageFile);
      }

      // Append other fields
      formDataToSend.append("phone", formData.phone || "");
      formDataToSend.append("addPropertyButton", formData.addPropertyButton || 0);
      formDataToSend.append("showTopBar", formData.showTopBar || 0);
      formDataToSend.append("showPreloader", formData.showPreloader || 0);
      formDataToSend.append("btnUrl", formData.btnUrl || "");
      formDataToSend.append("primaryColor", formData.primaryColor || "");
      formDataToSend.append("secondaryColor", formData.secondaryColor || "");
      formDataToSend.append("facebook", formData.facebook || "");
      formDataToSend.append("twitter", formData.twitter || "");
      formDataToSend.append("youtube", formData.youtube || "");
      formDataToSend.append("instagram", formData.instagram || "");
      formDataToSend.append("linkedin", formData.linkedin || "");
      formDataToSend.append("footerLogo", formData.footerLogo || "");
      formDataToSend.append("footerPhone", formData.footerPhone || "");
      formDataToSend.append("footerEmail", formData.footerEmail || "");
      formDataToSend.append("copyRightText", formData.copyRightText || "");
      formDataToSend.append("breadcumColor", formData.breadcumColor || "");
      formDataToSend.append("MetaTitle", formData.MetaTitle || "");
      formDataToSend.append("meta_key_word", formData.meta_key_word ? formData.meta_key_word.join(',') : "");
      formDataToSend.append("metaDescription", formData.metaDescription || "");
      formDataToSend.append("googleAnalytics", formData.googleAnalytics || "");
      formDataToSend.append("googleClientID", formData.googleClientID || "");
      formDataToSend.append("googleClientSecret", formData.googleClientSecret || "");
      formDataToSend.append("googleRedirectionUrl", formData.googleRedirectionUrl || "");
      formDataToSend.append("googleLoginClientId", formData.googleLoginClientId || "");
      formDataToSend.append("googleLoginClientSecret", formData.googleLoginClientSecret || "");
      formDataToSend.append("googleLoginRedirectionUrl", formData.googleLoginRedirectionUrl || "");

      // Send to backend (use create or update endpoint)
      const response = await frontendSettingsAPI.create(formDataToSend);

      if (response.success) {
        alert("Settings saved successfully!");
        // Clear the selected logo files after successful save
        setSelectedLogoFile(null);
        setSelectedFooterLogoFile(null);
        setSelectedBreadCumImageFile(null);
        setSelectedMetaImageFile(null);
      } else {
        alert("Failed to save settings.");
      }
    } catch (error) {
      console.error("Error saving settings:", error);
      alert("Something went wrong. Check console for details.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 w-full max-w-6xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between pb-4 border-b mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Frontend Settings</h3>
      </div>

      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-8 mb-6">
          <TabsTrigger value="general" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">General</TabsTrigger>
          <TabsTrigger value="theme" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">Theme Color</TabsTrigger>
          <TabsTrigger value="header" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">Header</TabsTrigger>
          <TabsTrigger value="footer" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">Footer</TabsTrigger>
          <TabsTrigger value="breadcums" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">BreadCums</TabsTrigger>
          <TabsTrigger value="seo" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">SEO</TabsTrigger>
          <TabsTrigger value="google-analytics" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">Google Analytics</TabsTrigger>
          <TabsTrigger value="google-login" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">Google Login</TabsTrigger>
        </TabsList>

        {/* General Tab */}
        <GeneralHeaderSettings
          formData={formData}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          logoPreview={logoPreview}
          handleLogoFileChange={handleLogoFileChange}
          loading={loading}
        />

        {/* Theme Color */}
        <ThemeColor
          formData={formData}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          loading={loading}
        />

        {/* Header */}
        <Header
          formData={formData}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          loading={loading}
        />

        {/* Footer */}
        <Footer
          formData={formData}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          loading={loading}
          handleFooterLogoFileChange={handleFooterLogoFileChange}
          footerLogoPreview={footerLogoPreview}
        />

        {/* BreadCums */}
        <BreadCums
          formData={formData}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          loading={loading}
          handleBreadcumImageFileChange={handleBreadcumImageFileChange}
          breadcumImagePreview={breadcumImagePreview}
        />


        {/* SEO */}
        <SEO
          formData={formData}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          handleMetaImageFileChange={handleMetaImageFileChange}
        
          loading={loading}
        />

        {/* Google Analytics */}
        <GoogleAnalytics
          formData={formData}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          loading={loading}
        />

        {/* Google Login */}
        <GoogleLogin
          formData={formData}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          loading={loading}
        />

      </Tabs>
    </div>
  );
};

export default FrontendSettings;
