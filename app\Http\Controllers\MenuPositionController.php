<?php

namespace App\Http\Controllers;
use Illuminate\Http\JsonResponse;
use App\Models\MenuPosition;
use Illuminate\Http\Request;

class MenuPositionController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        //
        try {
            $menuPositions = MenuPosition::all();
            return response()->json([
                'success' => true,
                'data' => $menuPositions
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch menu positions: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:menu_positions,name',
                'slug' => 'nullable|string|max:255|unique:menu_positions,slug',
            ]);
            $menuPosition = MenuPosition::create($validated);
            return response()->json([
                'success' => true,
                'data' => $menuPosition,
                'message' => 'Menu position created successfully'
            ], 201);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to create menu position: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(MenuPosition $menuPosition):jsonResponse
    {
        //
        try {
            return response()->json([
                'success' => true,
                'data' => $menuPosition
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch menu position: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MenuPosition $menuPosition):JsonResponse
    {
        //
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:menu_positions,name,' . $menuPosition->id,
                'slug' => 'nullable|string|max:255|unique:menu_positions,slug,' . $menuPosition->id,
            ]);
            $menuPosition->update($validated);
            return response()->json([
                'success' => true,
                'data' => $menuPosition->fresh(),
                'message' => 'Menu position updated successfully'
            ]);
        }
        catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update menu position: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MenuPosition $menuPosition):JsonResponse
    {
        //
        try {
            $menuPosition->delete();
            return response()->json([
                'success' => true,
                'message' => 'Menu position deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete menu position: ' . $e->getMessage()
            ], 500);
        }
    }
}
