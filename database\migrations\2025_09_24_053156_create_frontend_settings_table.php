<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('frontend_settings', function (Blueprint $table) {
            $table->id();
            $table->string('logo')->nullable();
            $table->text('phone')->nullable();
            $table->integer('addPropertyButton')->nullable();
            $table->string('btnUrl')->nullable();
            $table->string('showPreloader')->nullable();
            $table->integer('showTopBar')->nullable();
            $table->string('primaryColor')->nullable();
            $table->string('secondaryColor')->nullable();
            $table->string('facebook')->nullable();
            $table->string('twitter')->nullable();
            $table->string('youtube')->nullable();
            $table->string('instagram')->nullable();
            $table->string('linkedin')->nullable();
            $table->string('footerLogo')->nullable();
            $table->text('footerPhone')->nullable();
            $table->string('footerEmail')->nullable();
            $table->text('copyRightText')->nullable();
            $table->string('breadcumImage')->nullable();
            $table->string('breadcumColor')->nullable();
            $table->string('metaImage')->nullable();
            $table->string('MetaTitle')->nullable();
            $table->text('meta_key_word')->nullable();
            $table->text('metaDescription')->nullable();
            $table->string('googleAnalytics')->nullable();
            $table->string('googleClientID')->nullable();
            $table->string('googleClientSecret')->nullable();
            $table->string('googleRedirectionUrl')->nullable();
            $table->string('googleLoginClientId')->nullable();
            $table->string('googleLoginClientSecret')->nullable();
            $table->string('googleLoginRedirectionUrl')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('frontend_settings');
    }
};
