import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { FileText } from "lucide-react";

const MenuPositionSelector = ({
  menuPositions,
  selectedMenuPosition,
  onMenuPositionChange,
  loadingMenuPositions,
  onRefresh
}) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Select Menu Position
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
          <div className="space-y-2">
            <Label>Choose Menu Position</Label>
            <div className="flex flex-wrap gap-4">
              {menuPositions.length === 0 && !loadingMenuPositions && (
                <p className="text-sm text-muted-foreground">
                  No menu positions found. Please create menu positions first.
                </p>
              )}
              {menuPositions.map((position) => (
                <label
                  key={position.id}
                  className="inline-flex items-center cursor-pointer select-none"
                >
                  <input
                    type="checkbox"
                    checked={selectedMenuPosition === position.id.toString()}
                    onChange={(e) => {
                      console.log('Menu position selected:', position.id, position.name);
                      if (e.target.checked) {
                        // Select this position (exclusive selection)
                        onMenuPositionChange(position.id.toString());
                      } else {
                        // Uncheck - clear selection
                        onMenuPositionChange("");
                      }
                    }}
                    className="h-5 w-5 text-black focus:ring-black border-gray-300 rounded-sm checked:bg-black checked:border-black focus:ring-offset-0"
                  />
                  <span className="ml-2">{position.name}</span>
                </label>
              ))}
            </div>
          </div>
          <div>
            <Button 
              variant="outline" 
              onClick={onRefresh}
              disabled={loadingMenuPositions}
            >
              {loadingMenuPositions ? "Loading..." : "Refresh Positions"}
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default MenuPositionSelector;