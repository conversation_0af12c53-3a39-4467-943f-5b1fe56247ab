import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { X, FileText, Upload } from 'lucide-react';
import manageTypesAPI from '../../services/manageTypesAPI';
import blogAPI from '../../services/blogAPI';

const BlogModal = ({ isOpen, onClose, onSave, editData = null }) => {
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    excerpt: '',
    category_id: 'no-category',
    featured_image: null,
    status: 'draft',
    is_featured: false,
    author_name: '',
    author_email: '',
    tags: [],
    meta_title: '',
    meta_description: '',
    seo_keywords: []
  });

  const [currentImage, setCurrentImage] = useState(null);
  const [imagePreview, setImagePreview] = useState(null);

  const [formErrors, setFormErrors] = useState({});
  const [loading, setLoading] = useState(false);
  const [propertyTypes, setPropertyTypes] = useState([]);
  const [loadingPropertyTypes, setLoadingPropertyTypes] = useState(false);
  const [propertyTypesError, setPropertyTypesError] = useState('');
  const [tagInput, setTagInput] = useState('');
  const [keywordInput, setKeywordInput] = useState('');

  useEffect(() => {
    const fetchPropertyTypes = async () => {
      setLoadingPropertyTypes(true);
      setPropertyTypesError('');
      try {
        const data = await manageTypesAPI.propertyTypeDropDown();
        if (data && data.success && Array.isArray(data.data)) {
          setPropertyTypes(data.data);
        } else {
          setPropertyTypesError('Failed to load property types');
        }
      } catch (error) {
        setPropertyTypesError('Error fetching property types');
      } finally {
        setLoadingPropertyTypes(false);
      }
    };

    if (isOpen) {
      fetchPropertyTypes();

      // Load edit data if provided
      if (editData) {
        setFormData({
          title: editData.title || '',
          content: editData.content || '',
          excerpt: editData.excerpt || '',
          category_id: editData.category_id?.toString() || 'no-category',
          featured_image: null, // File input will be empty for edit
          status: editData.status || 'draft',
          is_featured: editData.is_featured || false,
          author_name: editData.author_name || '',
          author_email: editData.author_email || '',
          tags: Array.isArray(editData.tags) ? editData.tags : (editData.tags ? JSON.parse(editData.tags) : []),
          meta_title: editData.meta_title || '',
          meta_description: editData.meta_description || '',
          seo_keywords: Array.isArray(editData.seo_keywords) ? editData.seo_keywords : (editData.seo_keywords ? JSON.parse(editData.seo_keywords) : [])
        });
        setCurrentImage(editData.featured_image || null);
        setImagePreview(null);
      } else {
        // Reset form for new blog
        setFormData({
          title: '',
          content: '',
          excerpt: '',
          category_id: 'no-category',
          featured_image: null,
          status: 'draft',
          is_featured: false,
          author_name: '',
          author_email: '',
          tags: [],
          meta_title: '',
          meta_description: '',
          seo_keywords: []
        });
        setCurrentImage(null);
        setImagePreview(null);
      }
      
      // Disable body scroll when modal is open
      document.body.style.overflow = 'hidden';
    } else {
      // Re-enable body scroll when modal is closed
      document.body.style.overflow = 'unset';
    }

    // Cleanup function to ensure scroll is re-enabled
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen, editData]);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error for this field when user starts typing
    if (formErrors[field]) {
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors[field];
        return newErrors;
      });
    }
  };

  const handleFileChange = (e) => {
    const file = e.target.files[0];
    if (file) {
      // Validate file type and size
      const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
      const maxSize = 2048 * 1024; // 2MB

      if (!validTypes.includes(file.type)) {
        setFormErrors(prev => ({ ...prev, featured_image: ['Please select a valid image file (JPEG, PNG, JPG, GIF, WEBP)'] }));
        return;
      }

      if (file.size > maxSize) {
        setFormErrors(prev => ({ ...prev, featured_image: ['Image file size must be less than 2MB'] }));
        return;
      }

      setFormData(prev => ({ ...prev, featured_image: file }));
      setImagePreview(URL.createObjectURL(file));
      setFormErrors(prev => {
        const newErrors = { ...prev };
        delete newErrors.featured_image;
        return newErrors;
      });
    }
  };

  const addTag = () => {
    if (tagInput.trim() && !formData.tags.includes(tagInput.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, tagInput.trim()]
      }));
      setTagInput('');
    }
  };

  const removeTag = (tagToRemove) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }));
  };

  const addKeyword = () => {
    if (keywordInput.trim() && !formData.seo_keywords.includes(keywordInput.trim())) {
      setFormData(prev => ({
        ...prev,
        seo_keywords: [...prev.seo_keywords, keywordInput.trim()]
      }));
      setKeywordInput('');
    }
  };

  const removeKeyword = (keywordToRemove) => {
    setFormData(prev => ({
      ...prev,
      seo_keywords: prev.seo_keywords.filter(keyword => keyword !== keywordToRemove)
    }));
  };

  const handleSave = async () => {
    // Basic validation
    const errors = {};
    if (!formData.title.trim()) errors.title = ['Title is required'];
    if (!formData.content.trim()) errors.content = ['Content is required'];
    if (!formData.author_name.trim()) errors.author_name = ['Author name is required'];
    if (formData.author_email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.author_email)) {
      errors.author_email = ['Please enter a valid email address'];
    }

    if (Object.keys(errors).length > 0) {
      setFormErrors(errors);
      return;
    }

    setLoading(true);
    setFormErrors({});

    try {
      let response;
      
      // Process form data before sending
      const processedFormData = {
        ...formData,
        category_id: formData.category_id === 'no-category' ? null : formData.category_id
      };
      
      if (editData) {
        // Update existing blog
        response = await blogAPI.updateBlog(editData.id, processedFormData);
      } else {
        // Create new blog
        response = await blogAPI.createBlog(processedFormData);
      }

      if (response.data.success) {
        onSave(response.data.data);
        handleCancel(); // Reset form and close modal
      } else {
        setFormErrors({ general: [response.data.message || 'Failed to save blog'] });
      }
    } catch (error) {
      console.error('Error saving blog:', error);
      if (error.response?.data?.errors) {
        setFormErrors(error.response.data.errors);
      } else {
        setFormErrors({ general: ['An error occurred while saving the blog'] });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      title: '',
      content: '',
      excerpt: '',
      category_id: 'no-category',
      featured_image: null,
      status: 'draft',
      is_featured: false,
      author_name: '',
      author_email: '',
      tags: [],
      meta_title: '',
      meta_description: '',
      seo_keywords: []
    });
    setFormErrors({});
    setTagInput('');
    setKeywordInput('');
    setCurrentImage(null);
    setImagePreview(null);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-6xl max-h-[90vh] flex flex-col overflow-hidden">
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <FileText className="h-6 w-6" />
            <div>
              <h3 className="text-lg font-semibold">{editData ? 'Edit Blog' : 'Add New Blog'}</h3>
              <p className="text-blue-100 text-sm">
                {editData ? 'Update the blog post details.' : 'Fill in the details to create a new blog post.'}
              </p>
            </div>
          </div>
          <button
            onClick={handleCancel}
            className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
            disabled={loading}
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {formErrors.general && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-sm text-red-600">{formErrors.general[0]}</p>
            </div>
          )}

          <form className="space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h4 className="text-lg font-medium text-gray-900">Basic Information</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="no-focus-outline">
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleChange('title', e.target.value)}
                    placeholder="Enter blog title"
                    className={formErrors.title ? 'border-destructive' : ''}
                    disabled={loading}
                  />
                  {formErrors.title && (
                    <p className="text-sm text-destructive mt-1">{formErrors.title[0]}</p>
                  )}
                </div>

                <div className="no-focus-outline">
                  <Label htmlFor="category_id">Category</Label>
                  <Select
                    value={formData.category_id}
                    onValueChange={(value) => handleChange('category_id', value)}
                    disabled={loading || loadingPropertyTypes}
                  >
                    <SelectTrigger className={formErrors.category_id ? 'border-destructive' : ''}>
                      <SelectValue placeholder={loadingPropertyTypes ? "Loading categories..." : "Select category"} />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="no-category">No Category</SelectItem>
                      {loadingPropertyTypes && (
                        <SelectItem value="loading" disabled>Loading...</SelectItem>
                      )}
                      {propertyTypesError && (
                        <SelectItem value="error" disabled>{propertyTypesError}</SelectItem>
                      )}
                      {!loadingPropertyTypes && !propertyTypesError && propertyTypes.length === 0 && (
                        <SelectItem value="no-categories" disabled>No categories available</SelectItem>
                      )}
                      {!loadingPropertyTypes && !propertyTypesError && propertyTypes.map(pt => (
                        <SelectItem key={pt.id} value={pt.id.toString()}>{pt.name}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {formErrors.category_id && (
                    <p className="text-sm text-destructive mt-1">{formErrors.category_id[0]}</p>
                  )}
                </div>

                <div className="no-focus-outline">
                  <Label htmlFor="status">Status *</Label>
                  <Select
                    value={formData.status}
                    onValueChange={(value) => handleChange('status', value)}
                    disabled={loading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="draft">Draft</SelectItem>
                      <SelectItem value="published">Published</SelectItem>
                      <SelectItem value="scheduled">Scheduled</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="no-focus-outline">
                  <Label htmlFor="is_featured" className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="is_featured"
                      checked={formData.is_featured}
                      onChange={(e) => handleChange('is_featured', e.target.checked)}
                      className="rounded border-gray-300"
                      disabled={loading}
                    />
                    <span>Featured Blog</span>
                  </Label>
                </div>
              </div>

              <div className="no-focus-outline">
                <Label htmlFor="excerpt">Excerpt</Label>
                <Textarea
                  id="excerpt"
                  value={formData.excerpt}
                  onChange={(e) => handleChange('excerpt', e.target.value)}
                  placeholder="Brief description of the blog post (optional - will be auto-generated if empty)"
                  rows={2}
                  className={formErrors.excerpt ? 'border-destructive' : ''}
                  disabled={loading}
                />
                {formErrors.excerpt && (
                  <p className="text-sm text-destructive mt-1">{formErrors.excerpt[0]}</p>
                )}
              </div>

              <div className="no-focus-outline">
                <Label htmlFor="content">Content *</Label>
                <Textarea
                  id="content"
                  value={formData.content}
                  onChange={(e) => handleChange('content', e.target.value)}
                  placeholder="Enter blog content (HTML supported)"
                  rows={8}
                  className={formErrors.content ? 'border-destructive' : ''}
                  disabled={loading}
                />
                {formErrors.content && (
                  <p className="text-sm text-destructive mt-1">{formErrors.content[0]}</p>
                )}
              </div>
            </div>

            {/* Author Information */}
            <div className="space-y-4 border-t pt-6">
              <h4 className="text-lg font-medium text-gray-900">Author Information</h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="no-focus-outline">
                  <Label htmlFor="author_name">Author Name *</Label>
                  <Input
                    id="author_name"
                    value={formData.author_name}
                    onChange={(e) => handleChange('author_name', e.target.value)}
                    placeholder="Enter author name"
                    className={formErrors.author_name ? 'border-destructive' : ''}
                    disabled={loading}
                  />
                  {formErrors.author_name && (
                    <p className="text-sm text-destructive mt-1">{formErrors.author_name[0]}</p>
                  )}
                </div>

                <div className="no-focus-outline">
                  <Label htmlFor="author_email">Author Email</Label>
                  <Input
                    id="author_email"
                    type="email"
                    value={formData.author_email}
                    onChange={(e) => handleChange('author_email', e.target.value)}
                    placeholder="Enter author email"
                    className={formErrors.author_email ? 'border-destructive' : ''}
                    disabled={loading}
                  />
                  {formErrors.author_email && (
                    <p className="text-sm text-destructive mt-1">{formErrors.author_email[0]}</p>
                  )}
                </div>
              </div>
            </div>

            {/* Featured Image */}
            <div className="space-y-4 border-t pt-6">
              <h4 className="text-lg font-medium text-gray-900">Featured Image</h4>

              <div className="no-focus-outline">
                <Label htmlFor="featured_image">Featured Image</Label>
                <div className="mt-1 space-y-4">
                  {/* Image Preview */}
                  {(currentImage || imagePreview) && (
                    <div className="relative inline-block">
                      <img
                        src={imagePreview || currentImage}
                        alt="Featured image preview"
                        className="max-w-xs max-h-48 object-cover rounded-md border border-gray-300"
                      />
                      {imagePreview && (
                        <span className="absolute top-2 right-2 bg-green-500 text-white text-xs px-2 py-1 rounded">
                          New
                        </span>
                      )}
                    </div>
                  )}

                  {/* File Input */}
                  <div className="flex items-center space-x-4">
                    <label className="cursor-pointer bg-white border border-gray-300 rounded-md py-2 px-4 flex items-center space-x-2 hover:bg-gray-50 transition-colors">
                      <Upload className="h-4 w-4" />
                      <span>Choose Image</span>
                      <input
                        type="file"
                        id="featured_image"
                        accept="image/jpeg,image/png,image/jpg,image/gif,image/webp"
                        onChange={handleFileChange}
                        className="sr-only"
                        disabled={loading}
                      />
                    </label>
                    {formData.featured_image && (
                      <span className="text-sm text-green-600">
                        {formData.featured_image.name}
                      </span>
                    )}
                  </div>
                </div>
                <p className="text-xs text-gray-500 mt-1">
                  Supported formats: JPEG, PNG, JPG, GIF, WEBP. Max size: 2MB
                </p>
                {formErrors.featured_image && (
                  <p className="text-sm text-destructive mt-1">{formErrors.featured_image[0]}</p>
                )}
              </div>
            </div>

            {/* Tags */}
            <div className="space-y-4 border-t pt-6">
              <h4 className="text-lg font-medium text-gray-900">Tags</h4>
              
              <div className="no-focus-outline">
                <Label htmlFor="tags">Tags</Label>
                <div className="flex space-x-2 mb-2">
                  <Input
                    value={tagInput}
                    onChange={(e) => setTagInput(e.target.value)}
                    placeholder="Enter a tag"
                    onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                    disabled={loading}
                  />
                  <Button
                    type="button"
                    onClick={addTag}
                    variant="outline"
                    size="sm"
                    disabled={loading}
                  >
                    Add
                  </Button>
                </div>
                {formData.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2">
                    {formData.tags.map((tag, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeTag(tag)}
                          className="ml-1 text-blue-600 hover:text-blue-800"
                          disabled={loading}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </span>
                    ))}
                  </div>
                )}
              </div>
            </div>

            {/* SEO */}
            <div className="space-y-4 border-t pt-6">
              <h4 className="text-lg font-medium text-gray-900">SEO Settings</h4>
              
              <div className="grid grid-cols-1 gap-4">
                <div className="no-focus-outline">
                  <Label htmlFor="meta_title">Meta Title</Label>
                  <Input
                    id="meta_title"
                    value={formData.meta_title}
                    onChange={(e) => handleChange('meta_title', e.target.value)}
                    placeholder="SEO title (leave empty to use blog title)"
                    className={formErrors.meta_title ? 'border-destructive' : ''}
                    disabled={loading}
                  />
                  {formErrors.meta_title && (
                    <p className="text-sm text-destructive mt-1">{formErrors.meta_title[0]}</p>
                  )}
                </div>

                <div className="no-focus-outline">
                  <Label htmlFor="meta_description">Meta Description</Label>
                  <Textarea
                    id="meta_description"
                    value={formData.meta_description}
                    onChange={(e) => handleChange('meta_description', e.target.value)}
                    placeholder="SEO description (leave empty to use excerpt)"
                    rows={3}
                    className={formErrors.meta_description ? 'border-destructive' : ''}
                    disabled={loading}
                  />
                  {formErrors.meta_description && (
                    <p className="text-sm text-destructive mt-1">{formErrors.meta_description[0]}</p>
                  )}
                </div>

                <div className="no-focus-outline">
                  <Label htmlFor="seo_keywords">SEO Keywords</Label>
                  <div className="flex space-x-2 mb-2">
                    <Input
                      value={keywordInput}
                      onChange={(e) => setKeywordInput(e.target.value)}
                      placeholder="Enter an SEO keyword"
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addKeyword())}
                      disabled={loading}
                    />
                    <Button
                      type="button"
                      onClick={addKeyword}
                      variant="outline"
                      size="sm"
                      disabled={loading}
                    >
                      Add
                    </Button>
                  </div>
                  {formData.seo_keywords.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {formData.seo_keywords.map((keyword, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-800"
                        >
                          {keyword}
                          <button
                            type="button"
                            onClick={() => removeKeyword(keyword)}
                            className="ml-1 text-green-600 hover:text-green-800"
                            disabled={loading}
                          >
                            <X className="h-3 w-3" />
                          </button>
                        </span>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* Modal Footer */}
        <div className="flex justify-end space-x-2 pt-4 border-t px-6 pb-6">
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="hover:bg-gray-50 transition-colors"
            disabled={loading}
          >
            Cancel
          </Button>
          <Button
            type="button"
            onClick={handleSave}
            className="bg-blue-600 hover:bg-blue-700 transition-colors"
            disabled={loading}
          >
            {loading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {editData ? 'Updating...' : 'Creating...'}
              </>
            ) : (
              editData ? 'Update Blog' : 'Create Blog'
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default BlogModal;
