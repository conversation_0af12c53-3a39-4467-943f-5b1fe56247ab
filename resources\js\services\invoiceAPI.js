import axios from 'axios';

const API_URL = '/api/invoices';

// Create axios instance with default config
const api = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            localStorage.removeItem('auth_token');
            // Redirect to login if needed
        }
        return Promise.reject(error);
    }
);

// Invoice API functions
const invoiceAPI = {
    // Get all invoices with pagination and filters
    getAll: async (params = {}) => {
        try {
            const response = await api.get(API_URL, { params });
            // Handle paginated response
            if (response.data.success && response.data.data && response.data.data.data) {
                return response.data; // Return full response for pagination info
            }
            return response.data;
        } catch (error) {
            console.error('Error fetching invoices:', error);
            throw error.response?.data || error;
        }
    },

    // Get invoice by ID
    getById: async (id) => {
        try {
            const response = await api.get(`${API_URL}/${id}`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching invoice:', error);
            throw error.response?.data || error;
        }
    },

    // Create new invoice
    create: async (data) => {
        try {
            const response = await api.post(API_URL, data);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error creating invoice:', error);
            throw error.response?.data || error;
        }
    },

    // Update invoice
    update: async (id, data) => {
        try {
            const response = await api.put(`${API_URL}/${id}`, data);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error updating invoice:', error);
            throw error.response?.data || error;
        }
    },

    // Delete invoice
    delete: async (id) => {
        try {
            const response = await api.delete(`${API_URL}/${id}`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error deleting invoice:', error);
            throw error.response?.data || error;
        }
    },

    // Get invoice statistics
    getStatistics: async () => {
        try {
            const response = await api.get(`${API_URL}-statistics`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching invoice statistics:', error);
            throw error.response?.data || error;
        }
    },

    // Get dropdown options
    getDropdown: async () => {
        try {
            const response = await api.get(`${API_URL}/dropdown`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error fetching invoice dropdown:', error);
            throw error.response?.data || error;
        }
    },

    // Toggle invoice status (paid/unpaid/overdue)
    toggleStatus: async (id) => {
        try {
            const response = await api.patch(`${API_URL}/${id}/toggle-status`);
            return response.data.data || response.data;
        } catch (error) {
            console.error('Error toggling invoice status:', error);
            throw error.response?.data || error;
        }
    }
};

export default invoiceAPI;
    