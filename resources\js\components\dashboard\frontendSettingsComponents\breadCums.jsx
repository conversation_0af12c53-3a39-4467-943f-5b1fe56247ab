import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { TabsContent } from "@/components/ui/tabs";

const BreadCums = ({ formData, handleInputChange, handleSubmit, loading, handleBreadcumImageFileChange, breadcumImagePreview }) => {
    const handleBreadcumFileChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            handleBreadcumImageFileChange(file);
        }
    };

    return (
        <TabsContent value="breadcums" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <Label htmlFor="breadCumImage">BreadCum Image</Label>
                    <div className="space-y-2">
                        <Input
                            id="breadCumImage"
                            type="file"
                            accept="image/*"
                            onChange={handleBreadcumFileChange}
                            className=""
                        />

                        {breadcumImagePreview && (
                            <div className="mt-4">
                                <Label className="text-sm font-medium">Preview:</Label>
                                <div className="mt-2 border rounded-lg p-2 bg-gray-50">
                                    <img
                                        src={breadcumImagePreview}
                                        alt="BreadCum preview"
                                        className="max-w-full max-h-32 mx-auto rounded"
                                    />
                                </div>
                            </div>
                        )}
                    </div>
                </div>
                <div>
                    <Label htmlFor="breadcumColor">BreadCum Color</Label>
                    <Input
                    id="breadcumColor"
                    type="color"
                    value={formData.breadcumColor || "#2563eb"}
                    onChange={(e) => handleInputChange("breadcumColor", e.target.value)}
                    className="w-20 h-10"
                    />
                </div>
            </div>
             <div className="flex justify-end">
                <button
                onClick={handleSubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
                disabled={loading}
                >
                {loading ? "Saving..." : "Save Settings"}
                </button>
            </div>
        </TabsContent>
    )};
export default BreadCums;
