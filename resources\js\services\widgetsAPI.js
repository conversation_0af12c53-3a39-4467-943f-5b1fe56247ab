import axios from 'axios';

const API_URL = '/api/widgets';

// Create axios instance with default config
const api = axios.create({
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor to include auth token 
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Widgets API functions
const widgetsAPI = {
  // Get all widgets with pagination and filters
  getAll: async (params = {}) => {
    try {
      const response = await api.get(API_URL, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching widgets:', error);
      throw error.response?.data || error;
    }
  },

  // Get widget by ID
  getById: async (id) => {
    try {
      const response = await api.get(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching widget:', error);
      throw error.response?.data || error;
    }
  },
  // Create new widget
  create: async (data) => {
    try {
      const response = await api.post(API_URL, data);
      return response.data;
    } catch (error) {
      console.error('Error creating widget:', error);
      throw error.response?.data || error;
    }
  },
  // Update widget
  update: async (id, data) => {
    try {
      const response = await api.put(`${API_URL}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating widget:', error);
      throw error.response?.data || error;
    }
  },
  // Delete widget
  delete: async (id) => {
    try {
      const response = await api.delete(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting widget:', error);
      throw error.response?.data || error;
    }
  },
};

export default widgetsAPI;
