import React, { useState, useEffect } from "react";

const HowItWorks = ({ widget, handleConfigUpdate }) => {
  const items = widget.config?.items || [{ title: "", text: "", image: "" }];

  const addItem = () => {
    const newItems = [...items, { title: "", text: "", image: "" }];
    handleConfigUpdate(widget.id, "items", newItems);
  };

  const removeItem = (index) => {
    const newItems = items.filter((_, i) => i !== index);
    handleConfigUpdate(widget.id, "items", newItems);
  };

  const updateItem = (index, field, value) => {
    const newItems = [...items];
    newItems[index][field] = value;
    handleConfigUpdate(widget.id, "items", newItems);
  };
  
  const handleImageChange = async (index, e) => {
    const file = e.target.files[0];
    if (file) {
      // Create preview for UI
      const preview = URL.createObjectURL(file);
      updateItem(index, "imagePreview", preview);

      // Upload file to server
      const formData = new FormData();
      formData.append('image', file);

      try {
        const response = await fetch('/api/page-contents/upload-image', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          updateItem(index, "image", data.data.url); // Store image URL
        } else {
          alert('Image upload failed: ' + data.message);
        }
      } catch (error) {
        alert('Error uploading image: ' + error.message);
      }
    }
  };

  return (
    <div className="space-y-4">
      {/* Header fields */}
      <div className="flex gap-4">
        <div className="flex-1">
          <label>Shoulder</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={widget.config?.shoulder || ""}
            onChange={(e) =>
              handleConfigUpdate(widget.id, "shoulder", e.target.value)
            }
            placeholder="How IT Works"
          />
        </div>

        <div className="flex-1">
          <label>Under Text</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={widget.config?.underText || ""}
            onChange={(e) =>
              handleConfigUpdate(widget.id, "underText", e.target.value)
            }
            placeholder="Sub Title Here"
          />
        </div>

        <div className="flex-1">
          <label>Button Name</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={widget.config?.buttonName || ""}
            onChange={(e) =>
              handleConfigUpdate(widget.id, "buttonName", e.target.value)
            }
            placeholder="Button Name"
          />
        </div>

        <div className="flex-1">
          <label>Button Url</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={widget.config?.buttonUrl || ""}
            onChange={(e) =>
              handleConfigUpdate(widget.id, "buttonUrl", e.target.value)
            }
            placeholder="Button URL"
          />
        </div>
      </div>

      {/* Items */}
      <div className="space-y-4">
        {items.map((item, index) => (
          <div
            key={index}
            className="flex gap-4 items-start border p-4 rounded-md"
          >
            <div className="flex-1">
              <label>Image</label>
              <input
                type="file"
                accept="image/*"
                className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                onChange={(e) => handleImageChange(index, e)}
              />
              {(item.image || item.imagePreview) && (
                <div>
                  <img
                    src={item.image || item.imagePreview}
                    alt="Preview"
                    className="w-24 h-24 object-cover rounded-md border"
                  />
                </div>
              )}
            </div>

            <div className="flex-1">
              <label>Title</label>
              <input
                type="text"
                className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                value={item.title}
                onChange={(e) => updateItem(index, "title", e.target.value)}
              />
            </div>

            <div className="flex-1">
              <label>Text</label>
              <input
                type="text"
                className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                value={item.text}
                onChange={(e) => updateItem(index, "text", e.target.value)}
              />
            </div>

            <div className="flex-shrink-0">
              <button
                type="button"
                className="mt-6 px-3 py-2 bg-red-500 text-white rounded"
                onClick={() => removeItem(index)}
              >
                Remove
              </button>
            </div>
          </div>
        ))}

        <button
          type="button"
          className="px-4 py-2 bg-blue-500 text-white rounded"
          onClick={addItem}
        >
          Add Item
        </button>
      </div>
    </div>
  );
};

export default HowItWorks;
