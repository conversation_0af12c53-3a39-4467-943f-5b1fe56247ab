<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class FrontendSettings extends Model
{
    protected $fillable = [
        'logo',
        'phone',
        'addPropertyButton',
        'btnUrl',
        'showPreloader',
        'showTopBar',
        'primaryColor',
        'secondaryColor',
        'facebook',
        'twitter',
        'youtube',
        'instagram',
        'linkedin',
        'footerLogo',
        'footerPhone',
        'footerEmail',
        'copyRightText',
        'BreadcumImage',
        'breadcumColor',
        'metaImage',
        'MetaTitle',
        'meta_key_word',
        'metaDescription',
        'googleAnalytics',
        'googleClientID',
        'googleClientSecret',
        'googleRedirectionUrl',
        'googleLoginClientId',
        'googleLoginClientSecret',
        'googleLoginRedirectionUrl',
    ];

    protected $casts = [
        'addPropertyButton' => 'integer',
        'showPreloader' => 'integer',
        'showTopBar' => 'integer',
    ];
}
