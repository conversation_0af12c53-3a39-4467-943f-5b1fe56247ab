import React, { useState, useEffect } from 'react';
import { Plus, Search, Filter, Download, MoreVertical, Edit, Trash2, Eye, Users, Building, Calendar } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { useAuth } from '../../contexts/AuthContext';
import { useTranslation } from '../TranslationProvider';
import { showAlertMethods as showAlert } from '@/utils/sweetAlert';
import projectEmployeeAPI from '@/services/projectEmployeeAPI';

const AssignEmployeePage = () => {
    const { canAccessModule } = useAuth();
    const { t } = useTranslation();

    // State management
    const [assignments, setAssignments] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [projectFilter, setProjectFilter] = useState('all');
    const [employeeFilter, setEmployeeFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [perPage, setPerPage] = useState(15);
    const [totalPages, setTotalPages] = useState(0);
    const [statistics, setStatistics] = useState({});

    // Form state
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [isEditing, setIsEditing] = useState(false);
    const [editingId, setEditingId] = useState(null);
    const [projects, setProjects] = useState([]);
    const [employees, setEmployees] = useState([]);
    const [formData, setFormData] = useState({
        project_id: '',
        employee_id: '',
        role: '',
        status: 'active',
        responsibilities: '',
        notes: ''
    });

    // Check permissions
    const canCreate = canAccessModule('assign-employee', 'create');
    const canUpdate = canAccessModule('assign-employee', 'update');
    const canDelete = canAccessModule('assign-employee', 'delete');
    const canExport = canAccessModule('assign-employee', 'export');

    // Load data on component mount
    useEffect(() => {
        loadAssignments();
        loadStatistics();
        loadProjects();
        loadEmployees();
    }, [currentPage, perPage, searchTerm, statusFilter, projectFilter, employeeFilter]);

    const loadAssignments = async () => {
        try {
            setLoading(true);
            const params = {
                page: currentPage,
                per_page: perPage,
                search: searchTerm,
                status: statusFilter !== 'all' ? statusFilter : '',
                project_id: projectFilter !== 'all' ? projectFilter : '',
                employee_id: employeeFilter !== 'all' ? employeeFilter : ''
            };

            const response = await projectEmployeeAPI.getAll(params);
            if (response.success) {
                setAssignments(response.data.data);
                setTotalPages(Math.ceil(response.data.total / perPage));
            }
        } catch (error) {
            showAlert.error('Error loading employee assignments');
        } finally {
            setLoading(false);
        }
    };

    const loadStatistics = async () => {
        try {
            const response = await projectEmployeeAPI.getStatistics();
            if (response.success) {
                setStatistics(response.data);
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    };

    const loadProjects = async () => {
        try {
            const response = await projectEmployeeAPI.getProjects();
            if (response.success) {
                setProjects(response.data);
            }
        } catch (error) {
            console.error('Error loading projects:', error);
        }
    };

    const loadEmployees = async () => {
        try {
            const response = await projectEmployeeAPI.getEmployees();
            if (response.success) {
                setEmployees(response.data);
            }
        } catch (error) {
            console.error('Error loading employees:', error);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        
        try {
            let response;
            if (isEditing) {
                response = await projectEmployeeAPI.update(editingId, formData);
            } else {
                response = await projectEmployeeAPI.create(formData);
            }

            if (response.success) {
                showAlert.success(response.message);
                setIsModalOpen(false);
                resetForm();
                loadAssignments();
                loadStatistics();
            }
        } catch (error) {
            const message = error.response?.data?.message || 'An error occurred';
            showAlert.error(message);
        }
    };

    const handleEdit = async (assignment) => {
        setIsEditing(true);
        setEditingId(assignment.id);
        setFormData({
            project_id: assignment.project_id.toString(),
            employee_id: assignment.employee_id.toString(),
            role: assignment.role || '',
            status: assignment.status,
            responsibilities: assignment.responsibilities || '',
            notes: assignment.notes || ''
        });
        setIsModalOpen(true);
    };

    const handleDelete = async (assignment) => {
        const result = await showAlert.confirm(
            'Are you sure?',
            `Do you want to delete the assignment of ${assignment.employee_name} to ${assignment.project_name}?`,
            'Yes, delete it!'
        );

        if (result.isConfirmed) {
            try {
                const response = await projectEmployeeAPI.delete(assignment.id);
                if (response.success) {
                    showAlert.success(response.message);
                    loadAssignments();
                    loadStatistics();
                }
            } catch (error) {
                showAlert.error('Error deleting assignment');
            }
        }
    };

    const resetForm = () => {
        setFormData({
            project_id: '',
            employee_id: '',
            role: '',
            status: 'active',
            responsibilities: '',
            notes: ''
        });
        setIsEditing(false);
        setEditingId(null);
    };

    const getStatusBadge = (status) => {
        const statusConfig = {
            active: { variant: 'success', label: 'Active' },
            inactive: { variant: 'secondary', label: 'Inactive' },
            completed: { variant: 'default', label: 'Completed' },
            suspended: { variant: 'destructive', label: 'Suspended' }
        };
        
        const config = statusConfig[status] || { variant: 'secondary', label: status };
        return <Badge variant={config.variant}>{config.label}</Badge>;
    };

    const handleExport = async () => {
        try {
            const response = await projectEmployeeAPI.export({
                search: searchTerm,
                status: statusFilter !== 'all' ? statusFilter : '',
                project_id: projectFilter !== 'all' ? projectFilter : '',
                employee_id: employeeFilter !== 'all' ? employeeFilter : ''
            });
            
            const blob = new Blob([response], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `employee-assignments-${new Date().toISOString().split('T')[0]}.xlsx`;
            link.click();
            window.URL.revokeObjectURL(url);
        } catch (error) {
            showAlert.error('Error exporting data');
        }
    };

    return (
        <div className="mx-auto p-6 space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <div>
                    <h1 className="text-3xl font-bold">Assign Employee</h1>
                    <p className="text-muted-foreground">
                        Manage employee assignments to projects
                    </p>
                </div>
                {canCreate && (
                    <Button onClick={() => setIsModalOpen(true)}>
                        <Plus className="h-4 w-4 mr-2" />
                        Assign Employee
                    </Button>
                )}
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Total Assignments</CardTitle>
                        <Users className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{statistics.total_assignments || 0}</div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">Active Assignments</CardTitle>
                        <Building className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{statistics.active_assignments || 0}</div>
                    </CardContent>
                </Card>

                <Card>
                    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                        <CardTitle className="text-sm font-medium">This Month</CardTitle>
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{statistics.assignments_this_month || 0}</div>
                    </CardContent>
                </Card>
            </div>

            {/* Filters */}
            <Card>
                <CardHeader>
                    <CardTitle>Filter Employee Assignments</CardTitle>
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <div className="space-y-2">
                            <Label htmlFor="search">Search</Label>
                            <div className="relative">
                                <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="search"
                                    placeholder="Search assignments..."
                                    value={searchTerm}
                                    onChange={(e) => setSearchTerm(e.target.value)}
                                    className="pl-8"
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="status-filter">Status</Label>
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Statuses</SelectItem>
                                    <SelectItem value="active">Active</SelectItem>
                                    <SelectItem value="inactive">Inactive</SelectItem>
                                    <SelectItem value="completed">Completed</SelectItem>
                                    <SelectItem value="suspended">Suspended</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="project-filter">Project</Label>
                            <Select value={projectFilter} onValueChange={setProjectFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select project" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Projects</SelectItem>
                                    {projects.map((project) => (
                                        <SelectItem key={project.id} value={project.id.toString()}>
                                            {project.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="employee-filter">Employee</Label>
                            <Select value={employeeFilter} onValueChange={setEmployeeFilter}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Select employee" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Employees</SelectItem>
                                    {employees.map((employee) => (
                                        <SelectItem key={employee.id} value={employee.id.toString()}>
                                            {employee.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="flex justify-between items-center mt-4">
                        <div className="flex items-center space-x-2">
                            <Label htmlFor="per-page">Show:</Label>
                            <Select value={perPage.toString()} onValueChange={(value) => setPerPage(Number(value))}>
                                <SelectTrigger className="w-20">
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="10">10</SelectItem>
                                    <SelectItem value="15">15</SelectItem>
                                    <SelectItem value="25">25</SelectItem>
                                    <SelectItem value="50">50</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        {canExport && (
                            <Button variant="outline" onClick={handleExport}>
                                <Download className="h-4 w-4 mr-2" />
                                Export
                            </Button>
                        )}
                    </div>
                </CardContent>
            </Card>

            {/* Employee Assignments Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Employee Assignments</CardTitle>
                    <CardDescription>
                        List of all employee assignments to projects
                    </CardDescription>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className="flex justify-center py-4">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Employee</TableHead>
                                        <TableHead>Project</TableHead>
                                        <TableHead>Role</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Assigned By</TableHead>
                                        <TableHead className="text-right">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {assignments.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={6} className="text-center py-4">
                                                No employee assignments found
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        assignments.map((assignment) => (
                                            <TableRow key={assignment.id}>
                                                <TableCell className="font-medium">
                                                    {assignment.employee_name}
                                                </TableCell>
                                                <TableCell>{assignment.project_name}</TableCell>
                                                <TableCell>{assignment.role || '-'}</TableCell>
                                                <TableCell>{getStatusBadge(assignment.status)}</TableCell>
                                                <TableCell>{assignment.assigned_by_name}</TableCell>
                                                <TableCell className="text-right">
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button variant="ghost" className="h-8 w-8 p-0">
                                                                <MoreVertical className="h-4 w-4" />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent align="end">
                                                            <DropdownMenuItem onClick={() => handleEdit(assignment)}>
                                                                <Eye className="mr-2 h-4 w-4" />
                                                                View
                                                            </DropdownMenuItem>
                                                            {canUpdate && (
                                                                <DropdownMenuItem onClick={() => handleEdit(assignment)}>
                                                                    <Edit className="mr-2 h-4 w-4" />
                                                                    Edit
                                                                </DropdownMenuItem>
                                                            )}
                                                            {canDelete && (
                                                                <DropdownMenuItem
                                                                    onClick={() => handleDelete(assignment)}
                                                                    className="text-destructive"
                                                                >
                                                                    <Trash2 className="mr-2 h-4 w-4" />
                                                                    Delete
                                                                </DropdownMenuItem>
                                                            )}
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>
                    )}

                    {/* Pagination */}
                    {totalPages > 1 && (
                        <div className="flex justify-center mt-4">
                            <div className="flex space-x-2">
                                <Button
                                    variant="outline"
                                    onClick={() => setCurrentPage(Math.max(1, currentPage - 1))}
                                    disabled={currentPage === 1}
                                >
                                    Previous
                                </Button>
                                <span className="flex items-center px-4">
                                    Page {currentPage} of {totalPages}
                                </span>
                                <Button
                                    variant="outline"
                                    onClick={() => setCurrentPage(Math.min(totalPages, currentPage + 1))}
                                    disabled={currentPage === totalPages}
                                >
                                    Next
                                </Button>
                            </div>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Create/Edit Modal */}
            <Dialog open={isModalOpen} onOpenChange={setIsModalOpen}>
                <DialogContent className="sm:max-w-[600px]">
                    <DialogHeader>
                        <DialogTitle>
                            {isEditing ? 'Edit Employee Assignment' : 'Assign Employee to Project'}
                        </DialogTitle>
                        <DialogDescription>
                            {isEditing 
                                ? 'Update the employee assignment details below.'
                                : 'Fill in the details to assign an employee to a project.'
                            }
                        </DialogDescription>
                    </DialogHeader>

                    <form onSubmit={handleSubmit} className="space-y-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="project_id">Project *</Label>
                                <Select
                                    value={formData.project_id}
                                    onValueChange={(value) => setFormData({ ...formData, project_id: value })}
                                    required
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select project" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {projects.map((project) => (
                                            <SelectItem key={project.id} value={project.id.toString()}>
                                                {project.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="employee_id">Employee *</Label>
                                <Select
                                    value={formData.employee_id}
                                    onValueChange={(value) => setFormData({ ...formData, employee_id: value })}
                                    required
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select employee" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {employees.map((employee) => (
                                            <SelectItem key={employee.id} value={employee.id.toString()}>
                                                {employee.name} - {employee.designation}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="role">Role</Label>
                                <Input
                                    id="role"
                                    value={formData.role}
                                    onChange={(e) => setFormData({ ...formData, role: e.target.value })}
                                    placeholder="Enter role in project"
                                />
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="status">Status *</Label>
                            <Select
                                value={formData.status}
                                onValueChange={(value) => setFormData({ ...formData, status: value })}
                                required
                            >
                                <SelectTrigger>
                                    <SelectValue placeholder="Select status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="active">Active</SelectItem>
                                    <SelectItem value="inactive">Inactive</SelectItem>
                                    <SelectItem value="completed">Completed</SelectItem>
                                    <SelectItem value="suspended">Suspended</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="responsibilities">Responsibilities</Label>
                            <Textarea
                                id="responsibilities"
                                value={formData.responsibilities}
                                onChange={(e) => setFormData({ ...formData, responsibilities: e.target.value })}
                                placeholder="Enter employee responsibilities"
                                rows={3}
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="notes">Notes</Label>
                            <Textarea
                                id="notes"
                                value={formData.notes}
                                onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                                placeholder="Enter additional notes"
                                rows={2}
                            />
                        </div>

                        <div className="flex justify-end space-x-2">
                            <Button type="button" variant="outline" onClick={() => setIsModalOpen(false)}>
                                Cancel
                            </Button>
                            <Button type="submit">
                                {isEditing ? 'Update Assignment' : 'Assign Employee'}
                            </Button>
                        </div>
                    </form>
                </DialogContent>
            </Dialog>
        </div>
    );
};

export default AssignEmployeePage;
