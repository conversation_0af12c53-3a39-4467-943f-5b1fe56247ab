import React from "react";
import { TabsContent } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import Select from 'react-select';
import Timezone from './timezone';
import dateFormats from './dateFormat';

const General = ({ formData, handleInputChange, handleadminLogoFileChange, adminLogoPreview, handleinvoiceLogoFileChange, invoiceLogoPreview,handleSubmit,loading }) => {
  return (
    <TabsContent value="general" className="space-y-6">
         <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
             <div>
                <Label htmlFor="applicationName">Application Name</Label>
                    <Input
                        id="applicationName"
                        value={formData.applicationName || ""}
                        onChange={(e) => handleInputChange("applicationName", e.target.value)}
                        placeholder="Oikko"
                    />
                </div>

             <div>
                <Label htmlFor="vatTaxRateForCustomers">Vat/Tax Rate For Customers</Label>
                    <Input
                        id="vatTaxRateForCustomers"
                        value={formData.vatTaxRateForCustomers || ""}
                        onChange={(e) => handleInputChange("vatTaxRateForCustomers", e.target.value)}
                        placeholder="5"
                    />
                </div>

               <div>
                  <Label htmlFor="systemTimeZone">System TimeZone</Label>
                    <Select
                        value={formData.systemTimeZone ? { value: formData.systemTimeZone, label: formData.systemTimeZone } : null}
                        onChange={(selectedOption) =>
                        handleInputChange(
                            "systemTimeZone",
                            selectedOption ? selectedOption.value : ""
                        )
                        }
                        options={Timezone}
                        placeholder="Select timezone"
                    />
              </div>

              
               <div>
                  <Label htmlFor="dateFormat">Date Format</Label>
                    <Select
                        value={formData.dateFormat ? { value: formData.dateFormat, label: formData.dateFormat } : null}
                        onChange={(selectedOption) =>
                        handleInputChange(
                            "dateFormat",
                            selectedOption ? selectedOption.value : ""
                        )
                        }
                        options={dateFormats}
                        placeholder="Select Date Format"
                    />
              </div>

              <div>
                <Label htmlFor="commissionFromMerchant">Comission From Marchent(%)</Label>
                    <Input
                        id="commissionFromMerchant"
                        value={formData.commissionFromMerchant || ""}
                        onChange={(e) => handleInputChange("commissionFromMerchant", e.target.value)}
                        placeholder="5"
                    />
                </div>

                <div>
                    <Label htmlFor="adminLogo">Admin Logo</Label>
                    <div className="space-y-2">
                    <Input
                        id="adminLogo"
                        type="file"
                        accept="image/*"
                        onChange={handleadminLogoFileChange} 
                    />
        
                    {adminLogoPreview && (
                        <div className="mt-4">
                        <Label className="text-sm font-medium">Preview:</Label>
                        <div className="mt-2 border rounded-lg p-2 bg-gray-50">
                            <img
                            src={adminLogoPreview}
                            alt="Admin Logo preview"
                            className="max-w-full max-h-32 mx-auto rounded"
                            />
                        </div>
                        </div>
                    )}
                    </div>
                </div>

                  <div>
                    <Label htmlFor="invoiceLogo">Invoice Logo</Label>
                    <div className="space-y-2">
                    <Input
                        id="invoiceLogo"
                        type="file"
                        accept="image/*"
                        onChange={handleinvoiceLogoFileChange} 
                    />
        
                    {invoiceLogoPreview && (
                        <div className="mt-4">
                        <Label className="text-sm font-medium">Preview:</Label>
                        <div className="mt-2 border rounded-lg p-2 bg-gray-50">
                            <img
                            src={invoiceLogoPreview}
                            alt="Admin Logo preview"
                            className="max-w-full max-h-32 mx-auto rounded"
                            />
                        </div>
                        </div>
                    )}
                    </div>
                </div>
         </div>
          {/* Action Buttons */}
        <div className="flex justify-end">
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
            disabled={loading}
          >
            {loading ? "Saving..." : "Save Settings"}
          </button>
        </div>
    </TabsContent>
  );
};
export default General;
