import React from 'react';
import { AccordionContent } from "@/components/ui/accordion";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Plus } from "lucide-react";

const CustomLinkForm = ({
  customLink,
  onCustomLinkChange,
  onAddCustomLink,
  selectedMenuPosition
}) => {
  return (
    <AccordionContent>
      <div className="space-y-4">
        <div className="text-sm text-muted-foreground">
          Create a custom link for your menu
        </div>
        
        <div className="space-y-4">
          <div className="grid grid-cols-1 gap-4">
            <div>
              <Label htmlFor="link-title">Link Title *</Label>
              <Input
                id="link-title"
                type="text"
                placeholder="Enter link title"
                value={customLink.title}
                onChange={(e) => onCustomLinkChange("title", e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="link-url">URL *</Label>
              <Input
                id="link-url"
                type="url"
                placeholder="https://example.com"
                value={customLink.url}
                onChange={(e) => onCustomLinkChange("url", e.target.value)}
              />
            </div>
            
            <div>
              <Label htmlFor="link-description">Description</Label>
              <Textarea
                id="link-description"
                placeholder="Optional description for the link"
                value={customLink.description}
                onChange={(e) => onCustomLinkChange("description", e.target.value)}
                rows={3}
              />
            </div>
            
            <div>
              <Label htmlFor="link-target">Link Target</Label>
              <select
                id="link-target"
                value={customLink.target}
                onChange={(e) => onCustomLinkChange("target", e.target.value)}
                className="w-full p-2 border border-gray-300 rounded-md"
              >
                <option value="_self">Same Window</option>
                <option value="_blank">New Window</option>
              </select>
            </div>
          </div>
          
          <Button
            onClick={onAddCustomLink}
            disabled={!selectedMenuPosition}
            size="sm"
            className="ml-auto"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Custom Link
          </Button>
        </div>
      </div>
    </AccordionContent>
  );
};

export default CustomLinkForm;