import React, { useState, useEffect } from 'react';

const Advertise = () => {
  const [advertiseData, setAdvertiseData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchAdvertiseData = async () => {
      try {
        setLoading(true);
        console.log('Fetching Advertise data from database...');
        
        // Fetch page content data with page_id=1 and widget_id=11
        const response = await fetch('/api/frontend/page-contents?page_id=1&widget_id=11&paginate=false');
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        console.log('Advertise API Response:', result);
        
        if (result.success && result.data && result.data.length > 0) {
          // Find the widget with widget_id = 11 (advertise widget)
          const advertiseWidget = result.data.find(item => item.widget_id === 11);
          
          if (advertiseWidget && advertiseWidget.pageContent) {
            const config = typeof advertiseWidget.pageContent === 'string' 
              ? JSON.parse(advertiseWidget.pageContent) 
              : advertiseWidget.pageContent;
              
            console.log('Parsed Advertise config:', config);
            
            // Map the API response structure to our component structure
            const mappedAdvertiseData = {
              title: config.title,
              subtitle: config.subTitle,
              description: config.details ? config.details.replace(/<[^>]*>/g, '') : "A car that is dependable and has a low risk of breakdowns is highly desirable.", // Strip HTML tags
              image: config.image 
            };
            
            setAdvertiseData(mappedAdvertiseData);
          
          } 
        } 
      } catch (err) {
        console.error('Error fetching advertise data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAdvertiseData();
  }, []);


  if (loading) {
    return (
      <div className="recommended-apartment-section mb-100">
        <div className="container">
          <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
            <div className="spinner-border" role="status">
              <span className="sr-only">Loading Advertisement...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if there's an error or no data
  if (error || !advertiseData) {
    return (
      <div className="recommended-apartment-section mb-100">
        <div className="container">
          <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
            <div className="text-center">
              <h3>Unable to load Advertisement data</h3>
              <p className="text-muted">
                {error ? `Error: ${error}` : 'No data found in database'}
              </p>
              <p className="text-muted">Please check the database for page_id=1 and widget_id=11</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
  return (
    <>
      <div className="recommended-apartment-section mb-100">
        <div className="recomended-content-wrap">
          <div className="section-title1 wow fadeInUp" data-wow-delay="200ms">
            <span>{advertiseData.subtitle}</span>
            <h2>{advertiseData.title}</h2>
          </div>

          <div className="divider d-xl-flex d-none">
            {/* <img 
              src="/Frontend/assets/img/home1/Divider.svg" 
              alt="Divider" 
            /> */}
          </div>

          <div className="recomended-content wow fadeInUp" data-wow-delay="200ms">
            <p>{advertiseData.description}</p>
            <a 
              href="property-listing-left-sidebar.html" 
              className="primary-btn3"
            >
              Show Best Home
            </a>
          </div>
        </div>

        <div className="aparment-img">
          <img 
            src={advertiseData.image} 
            alt={advertiseData.title} 
          />
        </div>
      </div>
    </>
  );
};

export default Advertise;
