<?php

namespace App\Http\Controllers;
use App\Models\PaymentStatus;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class PaymentStatusController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        //
        try {
            $query = PaymentStatus::query();
            // search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where('name', 'LIKE', "%{$search}%");
            }
            // status filter
            if ($request->has('status') && $request->status) {
                $query->where('is_active', $request->status === 'active');
            }
            // sorting
            $sortBy = $request->get('sort_by', 'sort_order');
            $sortOrder = $request->get('sort_order', 'asc');
            if (in_array($sortBy, ['name', 'description', 'sort_order', 'created_at'])) {
                $query->orderBy($sortBy, $sortOrder);
            }
            // pagination
            $perPage = $request->get('per_page', 10);
            $paymentStatuses = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $paymentStatuses
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment statuses: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:payment_statuses,name',
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $paymentStatus = PaymentStatus::create($validated);
            return response()->json([
                'success' => true,
                'data' => $paymentStatus,
                'message' => 'Payment status created successfully'
            ], 201);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to create payment status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        //
        try{
            $paymentStatus = PaymentStatus::findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $paymentStatus,
                'message' => 'Payment status retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        //
        try {
            $paymentStatus = PaymentStatus::findOrFail($id);
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:payment_statuses,name,' . $id,
                'description' => 'nullable|string',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $paymentStatus->update($validated);
            return response()->json([
                'success' => true,
                'data' => $paymentStatus->fresh(),
                'message' => 'Payment status updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        //
        try {
            $paymentStatus = PaymentStatus::findOrFail($id);
            $paymentStatus->delete();
            return response()->json([
                'success' => true,
                'message' => 'Payment status deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete payment status: ' . $e->getMessage()
            ], 500);
        }
    }

    public function toggleStatus(string $id): JsonResponse
    {
        try{    
            $paymentStatus = PaymentStatus::findOrFail($id);
            $paymentStatus->is_active = !$paymentStatus->is_active;
            $paymentStatus->save();
            return response()->json([
                'success' => true,
                'data' => $paymentStatus,
                'message' => 'Payment status status updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update payment status status',
                'error' => $e->getMessage()
            ], 500);
        }
    }
    public function dropdown(): JsonResponse
    {
        try{    
            $paymentStatuses = PaymentStatus::active()
                ->ordered()
                ->select('id', 'name', 'description')
                ->get();
            return response()->json([
                'success' => true,
                'data' => $paymentStatuses,
                'message' => 'Payment statuses for dropdown retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve payment statuses for dropdown',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
