 import axios from 'axios';
 
const API_URL = '/api/manage-types';

// Create axios instance with default config
const api = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});
  
// Add request interceptor to include auth token 
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            // Handle unauthorized access
            localStorage.removeItem('auth_token');
            // Redirect to login if needed
        }
        return Promise.reject(error);
    }
);

// Manage Types API functions
const manageTypesAPI = {
    // Get all manage types with pagination and filters
    getAll: async (params = {}) => {
        try {
            const response = await api.get(API_URL, { params });
            return response.data;
        } catch (error) {
            console.error('Error fetching manage types:', error);
            throw error.response?.data || error;
        }
    },

    toggleStatus: async (id, type) => {
        try {
            const response = await api.patch(`${API_URL}/${id}/toggle-status/${type}`);
            return response.data;
        } catch (error) {
            console.error('Error toggling status:', error);
            throw error.response?.data || error;
        }
    },

    // Get manage type by ID
    getById: async (id) => {
        try {
            const response = await api.get(`${API_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching manage type:', error);
            throw error.response?.data || error;
        }
    },

    // Create new manage type
    create: async (data) => {
        try {
            const response = await api.post(API_URL, data);
            return response.data;
        } catch (error) {
            console.error('Error creating manage type:', error);
            throw error.response?.data || error;
        }
    },

    // Update manage type
    update: async (id, data) => {
        try {
            const response = await api.put(`${API_URL}/${id}`, data);
            return response.data;
        } catch (error) {
            console.error('Error updating manage type:', error);
            throw error.response?.data || error;
        }
    },

    // Delete manage type
    delete: async (id, type) => {
        try {
            const response = await api.delete(`${API_URL}/${id}/${type}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting manage type:', error);
            throw error.response?.data || error;
        }
    },

    // propertyTYpe Dropdown
    propertyTypeDropDown: async () => {
        try {
            console.log('Calling property-types-dropdown API');
            const response = await api.get('/api/property-types-dropdown');
            console.log('Property types API response:', response);
            console.log('Property types data:', response.data);
            return response.data;
        } catch (error) {
            console.error('Error fetching property types dropdown:', error);
            console.error('Error details:', error.response?.data || error);
            throw error.response?.data || error;
        }
    },

    // propertyStatus Dropdown
    propertyStatusDropDown: async () => {
        try {
            const response = await api.get('/api/property-statuses-dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching property statuses dropdown:', error);
            throw error.response?.data || error;
        }
    },

    // propertyStage Dropdown

    propertyStageDropDown: async () => {
        try {
            const response = await api.get('/api/property-stages-dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching property stages dropdown:', error);
            throw error.response?.data || error;
        }
    },

  

    // unitType Dropdown
    unitTypeDropDown: async () => {
        try {
            const response = await api.get('/api/unit-types-dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching unit types dropdown:', error);
            throw error.response?.data || error;
        }
    },

    // propertyService Dropdown
    propertyServiceDropDown: async () => {
        try {
            const response = await api.get('/api/property-services-dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching property services dropdown:', error);
            throw error.response?.data || error;
        }
    },

    // rentType Dropdown
    rentTypeDropDown: async () => {
        try {
            const response = await api.get('/api/rent-types-dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching rent types dropdown:', error);
            throw error.response?.data || error;
        }
    },

    // leaseType Dropdown
    leaseTypeDropDown: async () => {
        try {
            const response = await api.get('/api/lease-types-dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching lease types dropdown:', error);
            throw error.response?.data || error;
        }
    },

    // paymentType Dropdown
    paymentTypeDropDown: async () => {
        try {
            const response = await api.get('/api/payment-types-dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching payment types dropdown:', error);
            throw error.response?.data || error;
        }
    },

    // paymentMethod Dropdown
    paymentMethodDropDown: async () => {
        try {
            const response = await api.get('/api/payment-methods-dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching payment methods dropdown:', error);
            throw error.response?.data || error;
        }
    },


    // paymentStatus Dropdown
    paymentStatusDropDown: async () => {
        try {
            const response = await api.get('/api/payment-statuses-dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching payment statuses dropdown:', error);
            throw error.response?.data || error;
        }
    },

    // vendorType Dropdown
    vendorTypeDropDown: async () => {
        try {
            const response = await api.get('/api/vendor-types-dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching vendor types dropdown:', error);
            throw error.response?.data || error;
        }
    },

    // propertyAmenity Dropdown
    propertyAmenityDropDown: async () => {
        try {
            const response = await api.get('/api/property-amenities-dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching property amenities dropdown:', error);
            throw error.response?.data || error;
        }
    },

}

export default manageTypesAPI;
