import React, { useState, useEffect, useRef, useCallback } from "react";
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';   
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { showAlertMethods as showAlert } from '@/utils/sweetAlert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Edit,
  Eye,
  Package, 
  Loader2,
  MoreHorizontal,
  Trash2
} from "lucide-react";
import menuAPI from '@/services/menuAPI';
import MenuModal from './MenuModal';

export default function Pages() {
  const navigate = useNavigate();
  const [menus, setMenus] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedMenu, setSelectedMenu] = useState(null);

  useEffect(() => {
    const fetchMenus = async () => {
      try {
        setLoading(true);
        const response = await menuAPI.getAll();
        setMenus(response.data || []);
      } catch (error) {
        console.error("Error fetching menus:", error);
        showAlert.error('Error', 'Failed to load menus');
      } finally {
        setLoading(false);
      }
    };
    fetchMenus();
  }, []);

  const handleSuccess = () => {
    // Refresh menus after modal success
    setLoading(true);
    menuAPI.getAll().then(response => {
      setMenus(response.data || []);
      setLoading(false);
    });
  };

  const openNewMenuModal = () => { setSelectedMenu(null); setModalOpen(true); };
  const openEditMenuModal = (menu) => { setSelectedMenu(menu); setModalOpen(true); };
  const closeModal = () => { setModalOpen(false); setSelectedMenu(null); };
  const handleEditPage = (id) => { navigate(`/admin/pageEdit/${id}`); };

  const handleDeleteMenu = async (menu) => {
    if (!window.confirm(`Are you sure you want to delete "${menu.name}"?`)) return;
    try {
      setLoading(true);
      await menuAPI.delete(menu.id);
      showAlert.success('Deleted', 'Menu deleted successfully');
      // Refresh menus
      const response = await menuAPI.getAll();
      setMenus(response.data || []);
    } catch (error) {
      showAlert.error('Error', error.message || 'Failed to delete menu');
    } finally {
      setLoading(false);
    }
  };

  const filteredMenus = menus.filter(menu => {
    const q = searchTerm.toLowerCase();
    const matchesSearch = menu.name?.toLowerCase().includes(q) || menu.description?.toLowerCase().includes(q);
    const matchesStatus = statusFilter === 'all' || menu.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  return (
  <Card className="w-full mx-auto mt-8">
      <CardContent className="p-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Page Management</h1>
            <p className="text-gray-600">Manage your website menus and Pages</p>
          </div>
          <Button onClick={openNewMenuModal}>
            <Plus className="h-4 w-4 mr-2" />
            Add Menu
          </Button>
        </div>

        {/* Filters */}
        <div className="flex gap-4 items-center mb-4">
          <Input
            placeholder="Search menus..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="flex-1"
          />
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Table */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading menus...</span>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full text-sm" style={{ width: '100%' }}>
              <thead>
                <tr className="border-b">
                  <th className="px-4 py-3 w-12">
                    {/* Checkbox for select all (not functional) */}
                    <input type="checkbox" className="accent-primary" />
                  </th>
                  <th className="px-4 py-3 text-left">Name</th>
                  <th className="px-4 py-3 text-left w-24">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredMenus.length === 0 ? (
                  <tr>
                    <td colSpan={3} className="text-center py-8 text-gray-500">
                      <Package className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                      No menus found
                    </td>
                  </tr>
                ) : (
                  filteredMenus.map(menu => (
                    <tr key={menu.id} className="border-b hover:bg-muted/50">
                      <td className="px-4 py-3">
                        <input type="checkbox" className="accent-primary" />
                      </td>
                      <td className="px-4 py-3">{menu.name}</td>
                      <td className="px-4 py-3">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuLabel>Actions</DropdownMenuLabel>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem onClick={() => openEditMenuModal(menu)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleEditPage(menu.id)}>
                              <Eye className="h-4 w-4 mr-2" />
                              Edit Page
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem className="text-red-600" onClick={() => handleDeleteMenu(menu)}>
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        )}

        {/* Modal */}
        <MenuModal
          isOpen={modalOpen}
          onClose={closeModal}
          menu={selectedMenu}
          onSuccess={handleSuccess}
        />
      </CardContent>
    </Card>
  );
}
