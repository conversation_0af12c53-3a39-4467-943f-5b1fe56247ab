import React, { useState, useEffect, useRef, useCallback } from "react";
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';   
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { showAlertMethods as showAlert } from '@/utils/sweetAlert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DndProvider, useDrag, useDrop } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Plus,
  Edit,
  Eye,
  Package, 
  Loader2,
  MoreHorizontal,
  Trash2,
  GripVertical,
} from "lucide-react";
import menuAPI from '@/services/menuAPI';
import MenuModal from './MenuModal';

const ITEM_TYPE = 'MENU_ITEM';

/* ----------------------- Draggable Card ----------------------- */
function DraggableMenuCard({ menu, index, moveItem, onEdit, onEditPage, isDragEnabled }) {
  const ref = useRef(null);

  const [, drop] = useDrop({
    accept: ITEM_TYPE,
    hover(dragged, monitor) {
      if (!ref.current || !isDragEnabled) return;
      const dragIndex = dragged.index;
      const hoverIndex = index;
      if (dragIndex === hoverIndex) return;

      const hoverBoundingRect = ref.current.getBoundingClientRect();
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;

      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      const hoverClientX=clientOffset.x-hoverBoundingRect.left;
      const hoverMiddleX = (hoverBoundingRect.right - hoverBoundingRect.left) / 2;

      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) return;  
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) return;
    
      moveItem(dragIndex, hoverIndex);
      dragged.index = hoverIndex;
    },
  });

  const [{ isDragging }, drag] = useDrag({
    type: ITEM_TYPE,
    item: { id: menu.id, index },
    collect: (monitor) => ({ isDragging: monitor.isDragging() }),
    canDrag: isDragEnabled,
  });

  drag(drop(ref));

  return (
    <div
      ref={ref}
      style={{ opacity: isDragging ? 0.9 : 1, cursor: isDragEnabled ? 'grab' : 'default' }}
    >
      <Card>
        <CardContent className="p-6">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-2">
                {isDragEnabled && (
                  <div className="p-2 rounded-md hover:bg-gray-100 mr-1">
                    <GripVertical className="h-5 w-5 text-gray-500" />
                  </div>
                )}
                <h3 className="text-lg font-semibold">{menu.name}</h3>
                
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                
                <DropdownMenuItem onClick={() => onEdit(menu)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit
                </DropdownMenuItem>

                <DropdownMenuItem onClick={() => onEditPage(menu.id)}>
                  <Eye className="h-4 w-4 mr-2" />
                  Edit Page
                </DropdownMenuItem>


                <DropdownMenuSeparator />
                <DropdownMenuItem className="text-red-600">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/* ----------------------- Page ----------------------- */
export default function Pages() {
  const navigate = useNavigate();
  const [menus, setMenus] = useState([]);
  const menusRef = useRef(menus);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modalOpen, setModalOpen] = useState(false);
  const [selectedMenu, setSelectedMenu] = useState(null);
  const [savingOrder, setSavingOrder] = useState(false);
  const saveTimeoutRef = useRef(null);
  
  useEffect(() => { menusRef.current = menus; }, [menus]);

  /* Fetch menus */
  const fetchMenus = useCallback(async () => {
    try {
      setLoading(true);
      const response = await menuAPI.getAll();
      setMenus(response.data || []);
    } catch (error) {
      console.error("Error fetching menus:", error);
      showAlert.error('Error', 'Failed to load menus');
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchMenus();
    return () => {
      if (saveTimeoutRef.current) clearTimeout(saveTimeoutRef.current);
    };
  }, [fetchMenus]);

  const handleSuccess = () => fetchMenus();

  /* Modal handlers */
  const openNewMenuModal = () => { setSelectedMenu(null); setModalOpen(true); };
  const openEditMenuModal = (menu) => { setSelectedMenu(menu); setModalOpen(true); };
  const closeModal = () => { setModalOpen(false); setSelectedMenu(null); };

  /* New handler for Edit Page redirection */
  const handleEditPage = (id) => {
    navigate(`/admin/pageEdit/${id}`);
  };

  /* Filters */
  const filteredMenus = menus.filter(menu => {
    const q = searchTerm.toLowerCase();
    const matchesSearch = menu.name?.toLowerCase().includes(q) || menu.description?.toLowerCase().includes(q);
    const matchesStatus = statusFilter === 'all' || menu.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const isDragEnabled = searchTerm.trim() === '' && statusFilter === 'all';

  /* Move item locally */
  const moveItem = (fromIndex, toIndex) => {
    setMenus(prev => {
      const updated = [...prev];
      const [moved] = updated.splice(fromIndex, 1);
      updated.splice(toIndex, 0, moved);
      return updated.map((m, idx) => ({ ...m, position: idx + 1 }));
    });
    scheduleSaveOrder();
  };

  /* Debounced save */
  const scheduleSaveOrder = () => {
    if (saveTimeoutRef.current) clearTimeout(saveTimeoutRef.current);
    saveTimeoutRef.current = setTimeout(() => saveOrder(), 700);
  };

  /* Save order to backend */
  const saveOrder = async () => {
    const reordered = menusRef.current.map((m, idx) => ({
      id: m.id,
      position: idx + 1,
    }));
    if (!reordered.length) return;

    setSavingOrder(true);
    try {
      await menuAPI.reorder(reordered); // <-- must be implemented
      showAlert.success('Saved', 'Pages order saved');
    } catch (err) {
      console.error('Failed to save new order', err);
      showAlert.error('Error', 'Failed to save Pages order');
      fetchMenus(); // rollback
    } finally {
      setSavingOrder(false);
    }
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Page Management</h1>
            <p className="text-gray-600">Manage your website menus and Pages</p>
          </div>
          <div className="flex items-center gap-4">
            <Button onClick={openNewMenuModal}>
              <Plus className="h-4 w-4 mr-2" />
              Add Menu
            </Button>
            {savingOrder && (
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Loader2 className="h-4 w-4 animate-spin" />
                Saving order...
              </div>
            )}
          </div>
        </div>

        {/* Filters */}
        <div className="flex gap-4 items-center">
          <div className="flex-1">
            <Input
              placeholder="Search menus..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Select value={statusFilter} onValueChange={setStatusFilter}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="active">Active</SelectItem>
              <SelectItem value="inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {!isDragEnabled && (
          <div className="text-sm text-gray-500">
            Clear search/filters to reorder menus.
          </div>
        )}

        {/* List */}
        {loading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading menus...</span>
          </div>
        ) : (
          <div className="grid gap-4">
            {filteredMenus.length === 0 ? (
              <Card>
                <CardContent className="py-12 text-center">
                  <Package className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">No menus found</h3>
                  <p className="text-gray-600 mb-4">
                    {searchTerm || statusFilter !== 'all'
                      ? 'Try adjusting your search or filters'
                      : 'Get started by creating your first menu'}
                  </p>
                  <Button onClick={openNewMenuModal}>
                    <Plus className="h-4 w-4 mr-2" />
                    Create Menu
                  </Button>
                </CardContent>
              </Card>
            ) : (
              filteredMenus.map((menu, idx) => (
                <DraggableMenuCard
                  key={menu.id}
                  menu={menu}
                  index={idx}
                  moveItem={moveItem}
                  onEdit={openEditMenuModal}
                  onEditPage={handleEditPage}
                  isDragEnabled={isDragEnabled}
                />
              ))
            )}
          </div>
        )}

        {/* Modal */}
        <MenuModal
          isOpen={modalOpen}
          onClose={closeModal}
          menu={selectedMenu}
          onSuccess={handleSuccess}
        />
      </div>
    </DndProvider>
  );
}
