@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 0 0% 0%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 0 0% 100%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* SweetAlert2 z-index fixes to ensure modals appear above all other content */
@layer utilities {
  .swal2-z-index-high {
    z-index: 99999 !important;
  }
  
  .swal2-backdrop-z-index-high {
    z-index: 99998 !important;
  }
}

/* Global SweetAlert2 z-index overrides - Maximum priority */
.swal2-container {
  z-index: 99999 !important;
  position: fixed !important;
}

.swal2-backdrop {
  z-index: 99998 !important;
  position: fixed !important;
}

.swal2-popup {
  z-index: 99999 !important;
  position: relative !important;
}

/* Toast specific overrides */
.swal2-toast {
  z-index: 99999 !important;
}

/* Additional SweetAlert2 element overrides */
.swal2-modal {
  z-index: 99999 !important;
}

.swal2-overlay {
  z-index: 99998 !important;
}

/* Modal animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    transform: scale(0.95) translateY(-10px);
    opacity: 0;
  }
  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

.animate-fadeIn {
  animation: fadeIn 0.2s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

/* Smooth form transitions */
.form-transition {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.form-enter {
  animation: slideIn 0.3s ease-out;
}

/* Ensure all SweetAlert2 related elements have high z-index */
[class*="swal2"] {
  z-index: 99999 !important;
}

/* SweetAlert2 Z-Index Overrides */
.swal2-z-index-high {
  z-index: 999999 !important;
}

.swal2-backdrop-z-index-high {
  z-index: 999998 !important;
}

/* Override any framework z-index that might interfere */
body.swal2-shown .swal2-container {
  z-index: 999999 !important;
}

body.swal2-toast-shown .swal2-container {
  z-index: 999999 !important;
}

.swal2-backdrop {
  z-index: 999998 !important;
}

.swal2-popup {
  z-index: 999999 !important;
}
.font-semibold {
    font-weight: 400 !important;
}