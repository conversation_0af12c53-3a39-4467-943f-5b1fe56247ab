import axios from 'axios';

// Determine the correct API base URL
const getApiBaseUrl = () => {
  // In development with <PERSON><PERSON> serve
  if (window.location.port === '5173') {
    return 'http://localhost:8000/api';
  }
  // In production or <PERSON><PERSON> served directly
  return '/api';
};

const API_BASE_URL = getApiBaseUrl();



// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Country API methods
export const countryAPI = {
  // Get all countries
  getCountries: async (params = {}) => {
    try {
      const response = await api.get('/countries', { params });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get country by ID
  getCountry: async (id) => {
    try {
      const response = await api.get(`/countries/${id}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Create new country
  createCountry: async (data) => {
    try {
      const response = await api.post('/countries', data);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Update country
  updateCountry: async (id, data) => {
    try {
      const response = await api.put(`/countries/${id}`, data);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Delete country
  deleteCountry: async (id) => {
    try {
      const response = await api.delete(`/countries/${id}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get country statistics
  getStatistics: async () => {
    try {
      const response = await api.get('/countries-statistics');
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get countries for dropdown (public access)
  getCountriesDropdown: async (params = {}) => {
    try {
      const url = `${API_BASE_URL}/countries-dropdown`;
      
      const response = await axios.get(url, { params });
  
      return response.data;
    } catch (error) {
     
      throw error.response?.data || error.message;
    }
  },

  // Public API methods (no authentication required)
  public: {
    getCountriesDropdown: async (params = {}) => {
      try {
        const url = `${API_BASE_URL}/public/countries-dropdown`;
       
        
        const response = await axios.get(url, { 
          params,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        });
        
      
        return response.data;
      } catch (error) {
       
        throw error.response?.data || error.message;
      }
    }
  }
};

export default countryAPI;
