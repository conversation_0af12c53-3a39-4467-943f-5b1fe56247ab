import React from "react";
const propertyType = ({ widget, handleConfigUpdate }) => {

return(
     <div className="border p-4 rounded-md space-y-4">
                            <h3 className="text-lg font-semibold">Search</h3>
                            <div className="flex items-center space-x-2">
                              <label htmlFor={`propertyType-switch-${widget.id}`} className="text-sm font-medium text-gray-700">
                                Turn on Widget
                              </label>
                              <input
                                id={`propertyType-switch-${widget.id}`}
                                type="checkbox"
                                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-black"
                                checked={widget.config?.propertyTypeEnabled || false}
                                onChange={(e) => handleConfigUpdate(widget.id, "propertyTypeEnabled", e.target.checked)}
                              />
                              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1 checked:translate-x-6"></span>
                            </div>

                          </div>
)};


export default propertyType;
