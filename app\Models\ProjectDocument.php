<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProjectDocument extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'title',
        'file_path',
        'show_on_frontend',
    ];

    protected $casts = [
        'show_on_frontend' => 'boolean',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }
}
