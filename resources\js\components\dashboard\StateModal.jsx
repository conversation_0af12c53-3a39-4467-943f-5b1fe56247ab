import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useTranslation } from '@/hooks/useTranslation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  MapPin,
  X
} from 'lucide-react';
import Swal from 'sweetalert2';
import { stateAPI } from '../../services/stateAPI';
import { countryAPI } from '../../services/countryAPI';

const StateModal = ({ 
  isOpen, 
  onClose, 
  editingState, 
  onSuccess 
}) => {
  const { t } = useTranslation();
  
  // Form state
  const [formData, setFormData] = useState({
    name: '',
    country_id: '',
    status: 'active',
    image: null
  });
  const [formErrors, setFormErrors] = useState({});
  const [submitLoading, setSubmitLoading] = useState(false);
  const [imagePreview, setImagePreview] = useState(null);

  // Countries state
  const [countries, setCountries] = useState([]);
  const [countriesLoading, setCountriesLoading] = useState(false);

  // Load countries for dropdown
  const loadCountries = async () => {
    try {
      setCountriesLoading(true);
      console.log('StateModal: Loading countries...');
      
      // Try multiple endpoints as fallback
      let response = null;
      let endpoint = '';
      
      try {
        // First try the public endpoint
        endpoint = 'public.getCountriesDropdown';
        console.log('StateModal: Trying public endpoint...');
        response = await countryAPI.public.getCountriesDropdown();
      } catch (publicError) {
        console.warn('StateModal: Public endpoint failed:', publicError);
        
        try {
          // Fallback to authenticated endpoint
          endpoint = 'getCountriesDropdown';
          console.log('StateModal: Trying authenticated endpoint...');
          response = await countryAPI.getCountriesDropdown();
        } catch (authError) {
          console.warn('StateModal: Authenticated endpoint failed:', authError);
          throw authError;
        }
      }
      
      console.log('StateModal: Countries response from', endpoint, ':', response);
      
      if (response && response.success && response.data) {
        setCountries(response.data || []);
        console.log('StateModal: Countries loaded successfully:', response.data.length, 'countries');
      } else {
        console.warn('StateModal: Countries response not successful:', response);
        setCountries([]);
      }
    } catch (err) {
      console.error('Error loading countries:', err);
      setCountries([]);
    } finally {
      setCountriesLoading(false);
    }
  };

  // Load countries when modal opens
  useEffect(() => {
    if (isOpen) {
      loadCountries();
      
      // Load edit data if provided
      if (editingState) {
        // Clean up any existing image preview
        if (imagePreview) {
          URL.revokeObjectURL(imagePreview);
          setImagePreview(null);
        }
        
        // Ensure country_id is properly handled
        let countryId = '';
        if (editingState.country_id !== null && editingState.country_id !== undefined) {
          countryId = editingState.country_id.toString();
        } else if (editingState.country && editingState.country.id) {
          countryId = editingState.country.id.toString();
        }
        
        setFormData({
          name: editingState.name || '',
          country_id: countryId,
          status: editingState.status || 'active',
          image: null
        });
      } else {
        // Reset for new state
        resetForm();
      }
    }
  }, [isOpen, editingState]);

  // Cleanup image preview on unmount
  useEffect(() => {
    return () => {
      if (imagePreview) {
        URL.revokeObjectURL(imagePreview);
      }
    };
  }, [imagePreview]);

  // Reset form
  const resetForm = () => {
    // Clean up image preview URL
    if (imagePreview) {
      URL.revokeObjectURL(imagePreview);
      setImagePreview(null);
    }
    
    setFormData({
      name: '',
      country_id: '',
      status: 'active',
      image: null
    });
    setFormErrors({});
    
    // Reset file input
    const fileInput = document.getElementById('image');
    if (fileInput) {
      fileInput.value = '';
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    
    try {
      setSubmitLoading(true);
      setFormErrors({});

      // Validate required fields on frontend
      const requiredFields = ['name', 'country_id', 'status'];
      const missingFields = requiredFields.filter(field => !formData[field] || formData[field] === '');
      
      if (missingFields.length > 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Missing Required Fields',
          text: `Please fill in the following required fields: ${missingFields.join(', ')}`,
          confirmButtonText: 'OK'
        });
        return;
      }
      
      // Validate required fields before sending
      if (!formData.name?.trim()) {
        setFormErrors({name: ['Name is required']});
        return;
      }
      
      if (!formData.country_id || formData.country_id === '' || formData.country_id === 'no-data' || formData.country_id === 'loading') {
        setFormErrors({country_id: ['Please select a country']});
        return;
      }
      
      if (!formData.status) {
        setFormErrors({status: ['Status is required']});
        return;
      }

      // Convert country_id to integer and validate
      const countryId = parseInt(formData.country_id);
      if (isNaN(countryId) || countryId <= 0) {
        setFormErrors({country_id: ['Invalid country selection']});
        return;
      }

      // Prepare form data based on whether we have a file upload or not
      let dataToSend;
      let isFileUpload = false;
      
      // Check if we have a file to upload
      if (formData.image && 
          formData.image instanceof File && 
          formData.image.size > 0) {
        // Use FormData for file uploads
        dataToSend = new FormData();
        dataToSend.append('name', formData.name.trim());
        dataToSend.append('country_id', countryId);
        dataToSend.append('status', formData.status);
        dataToSend.append('image', formData.image);
        
        // Add _method for Laravel to handle PUT with FormData
        if (editingState) {
          dataToSend.append('_method', 'PUT');
        }
        
        isFileUpload = true;
        console.log('StateModal: Using FormData for file upload');
      } else {
        // Use regular JSON for updates without files
        dataToSend = {
          name: formData.name.trim(),
          country_id: countryId,
          status: formData.status
        };
        
        console.log('StateModal: Using JSON data (no file upload)');
      }
      
      // Debug form data being sent
      console.log('StateModal: Form data being sent:', {
        name: formData.name,
        country_id: formData.country_id,
        country_id_parsed: countryId,
        status: formData.status,
        image: formData.image,
        isFileUpload: isFileUpload
      });
      
      console.log('StateModal: EditingState:', editingState);
      console.log('StateModal: Countries available:', countries.length, countries.slice(0, 3));
      
      // Debug data being sent
      console.log('StateModal: Data to send:', dataToSend);
      if (isFileUpload) {
        console.log('StateModal: FormData contents:');
        for (let pair of dataToSend.entries()) {
          console.log(pair[0] + ': ' + (pair[1] instanceof File ? `File(${pair[1].name})` : pair[1]));
        }
      }
      
      let response;
      if (editingState) {
        if (isFileUpload) {
          // Use POST with _method override for file uploads
          response = await stateAPI.updateStateWithFile(editingState.id, dataToSend);
        } else {
          // Use regular PUT for JSON updates
          response = await stateAPI.updateState(editingState.id, dataToSend);
        }
        Swal.fire({
          icon: 'success',
          title: t('common.messages.success'),
          text: t('state.messages.updateSuccess')
        });
      } else {
        response = await stateAPI.createState(dataToSend);
        Swal.fire({
          icon: 'success',
          title: t('common.messages.success'),
          text: t('state.messages.createSuccess')
        });
      }
      
      onClose();
      resetForm();
      onSuccess(); // Refresh parent data
      
    } catch (err) {
      console.error('StateModal: Submit error:', err);
      console.error('StateModal: Error response:', err.response?.data);
      console.error('StateModal: Error status:', err.response?.status);
      
      if (err.response?.status === 401) {
        Swal.fire({
          icon: 'error',
          title: 'Authentication Error',
          text: 'You are not authenticated. Please log in again.'
        });
      } else if (err.response?.status === 403) {
        Swal.fire({
          icon: 'error',
          title: 'Permission Error',
          text: 'You do not have permission to create states. Contact your administrator.'
        });
      } else if (err.response?.data?.errors) {
        setFormErrors(err.response.data.errors);
        console.log('StateModal: Validation errors:', err.response.data.errors);
        
        // Show validation errors in a more user-friendly way
        const errorMessages = Object.entries(err.response.data.errors)
          .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
          .join('\n');
        
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: errorMessages,
          confirmButtonText: 'OK'
        });
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: err.response?.data?.message || err.message || 'An unexpected error occurred'
        });
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-2xl max-h-[90vh] flex flex-col overflow-hidden">
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <MapPin className="h-6 w-6" />
            <div>
              <h3 className="text-lg font-semibold">
                {editingState ? t('state.editState') : t('state.addState')}
              </h3>
              <p className="text-blue-100 text-sm">
                {editingState ? 'Edit the state/province information' : 'Add a new state/province to the system'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-blue-600/50"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Modal Body */}
        <div className="flex-1 overflow-y-auto p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Name Field */}
            <div>
              <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                {t('state.fields.name')} *
              </Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => setFormData({...formData, name: e.target.value})}
                className={`mt-1 ${formErrors.name ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'}`}
                placeholder="Enter state/province name"
              />
              {formErrors.name && (
                <p className="text-sm text-red-600 mt-1">{formErrors.name[0]}</p>
              )}
            </div>

            {/* Country Dropdown */}
            <div>
              <Label htmlFor="country_id" className="text-sm font-medium text-gray-700">
                {t('state.fields.country')} *
              </Label>
              <Select value={formData.country_id} onValueChange={(value) => setFormData({...formData, country_id: value})}>
                <SelectTrigger className={`mt-1 ${formErrors.country_id ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'}`}>
                  <SelectValue placeholder={countriesLoading ? "Loading countries..." : "Select country"} />
                </SelectTrigger>
                <SelectContent>
                  {countriesLoading ? (
                    <SelectItem value="loading" disabled>Loading countries...</SelectItem>
                  ) : countries.length === 0 ? (
                    <SelectItem value="no-data" disabled>No countries available</SelectItem>
                  ) : (
                    countries.map(country => (
                      <SelectItem key={country.id} value={country.id.toString()}>
                        {country.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {countriesLoading && (
                <p className="text-sm text-blue-600 mt-1">Loading countries...</p>
              )}
              {!countriesLoading && countries.length === 0 && (
                <p className="text-sm text-orange-600 mt-1">No countries loaded. Check console for errors.</p>
              )}
              {formErrors.country_id && (
                <p className="text-sm text-red-600 mt-1">{formErrors.country_id[0]}</p>
              )}
            </div>

            {/* Status Field */}
            <div>
              <Label htmlFor="status" className="text-sm font-medium text-gray-700">
                {t('state.fields.status')} *
              </Label>
              <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                <SelectTrigger className={`mt-1 ${formErrors.status ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'}`}>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="active">{t('common.status.active')}</SelectItem>
                  <SelectItem value="inactive">{t('common.status.inactive')}</SelectItem>
                </SelectContent>
              </Select>
              {formErrors.status && (
                <p className="text-sm text-red-600 mt-1">{formErrors.status[0]}</p>
              )}
            </div>

            {/* Image Upload Field */}
            <div>
              <Label htmlFor="image" className="text-sm font-medium text-gray-700">
                State Image
              </Label>
              <div className="mt-1">
                <Input
                  id="image"
                  type="file"
                  accept="image/*"
                  onChange={(e) => {
                    const file = e.target.files[0] || null;
                    console.log('StateModal: File selected:', file);
                    console.log('StateModal: File details:', file ? {
                      name: file.name,
                      size: file.size,
                      type: file.type
                    } : 'No file');
                    
                    // Clean up previous preview URL
                    if (imagePreview) {
                      URL.revokeObjectURL(imagePreview);
                    }
                    
                    // Create new preview URL if file selected
                    const previewUrl = file ? URL.createObjectURL(file) : null;
                    setImagePreview(previewUrl);
                    setFormData({...formData, image: file});
                  }}
                  className={`${formErrors.image ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'}`}
                />
                
                {formErrors.image && (
                  <p className="text-sm text-red-600 mt-1">{formErrors.image[0]}</p>
                )}
                
                {formData.image && formData.image.name && (
                  <p className="text-sm text-green-600 mt-1">
                    Selected: {formData.image.name} ({(formData.image.size / 1024).toFixed(2)} KB)
                  </p>
                )}
                
                {editingState && editingState.image && !formData.image && (
                  <p className="text-sm text-blue-600 mt-1">Current image: {editingState.image}</p>
                )}
                
                {/* Image Preview */}
                <div className="mt-3">
                  {imagePreview ? (
                    /* New image preview */
                    <div className="relative">
                      <p className="text-sm font-medium text-gray-700 mb-2">Preview:</p>
                      <div className="relative inline-block">
                        <img 
                          src={imagePreview} 
                          alt="Selected image preview" 
                          className="w-32 h-32 object-cover rounded-lg border-2 border-gray-300 shadow-sm"
                        />
                        <button
                          type="button"
                          onClick={() => {
                            URL.revokeObjectURL(imagePreview);
                            setImagePreview(null);
                            setFormData({...formData, image: null});
                            // Reset file input
                            document.getElementById('image').value = '';
                          }}
                          className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600 transition-colors"
                        >
                          ×
                        </button>
                      </div>
                    </div>
                  ) : editingState && editingState.image ? (
                    /* Current image display for edit mode */
                    <div className="relative">
                      <p className="text-sm font-medium text-gray-700 mb-2">Current Image:</p>
                      <div className="relative inline-block">
                        <img 
                          src={`/state/${editingState.image}`} 
                          alt="Current state image" 
                          className="w-32 h-32 object-cover rounded-lg border-2 border-gray-300 shadow-sm"
                          onError={(e) => {
                            e.target.src = '/placeholder.png'; // fallback image
                            e.target.alt = 'Image not found';
                          }}
                        />
                        <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs p-1 rounded-b-lg text-center">
                          Current
                        </div>
                      </div>
                    </div>
                  ) : (
                    /* No image placeholder */
                    <div className="text-center py-4">
                      <div className="w-32 h-32 mx-auto bg-gray-100 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                        <div className="text-center">
                          <svg className="w-8 h-8 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                          </svg>
                          <p className="text-xs text-gray-500">No image selected</p>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </form>
        </div>

        {/* Modal Footer */}
        <div className="bg-gray-50 px-6 py-4 flex justify-end space-x-3 border-t">
          <Button 
            type="button" 
            variant="outline" 
            onClick={onClose}
            className="px-4 py-2"
          >
            {t('common.buttons.cancel')}
          </Button>
          <Button 
            type="submit" 
            disabled={submitLoading}
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white"
          >
            {submitLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              editingState ? t('common.buttons.save') : t('common.buttons.add')
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default StateModal;