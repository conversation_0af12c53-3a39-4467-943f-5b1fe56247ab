import React, { useState, useEffect } from "react";

const WhyChooseUs = () => {
  const [whyChooseData, setWhyChooseData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchWhyChooseData = async () => {
      try {
        setLoading(true);
        console.log('Fetching Why Choose Us data from database...');
        
        // Fetch page content data with page_id=1 and widget_id=7
        const response = await fetch('/api/frontend/page-contents?page_id=1&widget_id=7&paginate=false');
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        console.log('Why Choose Us API Response:', result);
        
        if (result.success && result.data && result.data.length > 0) {
          // Find the widget with widget_id = 7 (why choose us widget)
          const whyChooseWidget = result.data.find(item => item.widget_id === 7);
          
          if (whyChooseWidget && whyChooseWidget.pageContent) {
            const config = typeof whyChooseWidget.pageContent === 'string' 
              ? JSON.parse(whyChooseWidget.pageContent) 
              : whyChooseWidget.pageContent;
              
            console.log('Parsed Why Choose Us config:', config);
            
            // Map the API response structure to our component structure
            const mappedWhyChooseData = {
              title: config.shoulder || "Why Only Choose Neckle",
              subtitle: config.underTitle || "Here are some of the featured Apartment in different categories",
              features: config.items ? config.items.map(item => ({
                title: item.title || "Feature Title",
                description: item.subTitle || "Feature description goes here...",
                icon: "/Frontend/assets/img/home2/icon/affordable.svg" // Default icon, could be made dynamic later
              })) : [],
              logoIcon: config.iconImage ,
              image1: config.image ,
              image2: config.imagetwo,
              reviewText: config.reviewText
            };
            
            setWhyChooseData(mappedWhyChooseData);
            console.log('Why Choose Us data set from database:', mappedWhyChooseData);
          } else {
            console.log('No widget found with widget_id 7');
          }
        } else {
          console.log('No data found in API response');
        } 
      } catch (err) {
        console.error('Error fetching why choose us data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchWhyChooseData();
  }, []);

  // Add debugging info
  console.log('Why Choose Us data from database:', whyChooseData);

  if (loading) {
    return (
      <div className="home2-why-choose-section mb-100 pt-90 pb-90">
        <div className="container">
          <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
            <div className="spinner-border" role="status">
              <span className="sr-only">Loading Why Choose Us...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if there's an error or no data
  if (error || !whyChooseData) {
    return (
      <div className="home2-why-choose-section mb-100 pt-90 pb-90">
        <div className="container">
          <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
            <div className="text-center">
              <h3>Unable to load Why Choose Us data</h3>
              <p className="text-muted">
                {error ? `Error: ${error}` : 'No data found in database'}
              </p>
              <p className="text-muted">Please check the database for page_id=1 and widget_id=7</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <>
     

      {/* Start home 2 Why Choose section */}
      <div className="home2-why-choose-section mb-100 pt-90 pb-90">
        <div className="container">
          <div className="row mb-50 wow fadeInUp" data-wow-delay="200ms">
            <div className="col-lg-12">
              <div className="section-title-2 text-center">
                <h2>{whyChooseData.title }</h2>
                <p>{whyChooseData.subtitle}</p>
              </div>
            </div>
          </div>

          <div className="row g-lg-4 gy-5 mb-50">
            <div className="col-lg-6">
              <div className="why-choose-content-area">
                <ul>
                  {whyChooseData.features && whyChooseData.features.length > 0 && (
                    whyChooseData.features.map((feature, index) => (
                      <li key={index} className="single-choose wow fadeInUp" data-wow-delay={`${300 + index * 100}ms`}>
                        <div className="icon">
                          <img 
                            src={feature.icon || "/Frontend/assets/img/home2/icon/affordable.svg"} 
                            alt={feature.title || "Feature"} 
                          />
                        </div>
                        <div className="content">
                          <h5>
                            <span>{feature.title}</span>
                          </h5>
                          <p>
                            {feature.description}
                          </p>
                        </div>
                      </li>
                    ))
                  )}
                </ul>
              </div>
            </div>

            <div className="col-lg-6">
              <div className="why-choose-img-wrap wow zoomIn" data-wow-delay="400ms">
                <div className="logo-area">
                  <img 
                    src={whyChooseData.logoIcon || "/Frontend/assets/img/home2/icon/house-1.svg"} 
                    alt="House" 
                  />
                </div>
                <div className="row g-lg-4 g-2">
                  <div className="col-6">
                    <div className="choose-img">
                      <img 
                        src={whyChooseData.image1 || "/Frontend/assets/img/home2/choose-01.png"} 
                        alt="Choose 01" 
                      />
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="choose-img">
                      <img 
                        src={whyChooseData.image2 || "/Frontend/assets/img/home2/choose-02.png"} 
                        alt="Choose 02" 
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div className="row wow fadeInUp" data-wow-delay="200ms">
            <div className="col-lg-12">
              <div className="trustpilot-review">
                <div dangerouslySetInnerHTML={{ 
                  __html: whyChooseData.reviewText || "Excellent! 5.0 Rating out of <strong>5.0</strong> based on <strong>245354</strong> reviews" 
                }} />
                <img src="/Frontend/assets/img/home1/icon/trustpilot-star2.svg" alt="Trustpilot Stars" />
                <img src="/Frontend/assets/img/home1/icon/trustpilot-logo.svg" alt="Trustpilot Logo" />
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* End home 2 Why Choose section */}
    </>
  );
};

export default WhyChooseUs;
