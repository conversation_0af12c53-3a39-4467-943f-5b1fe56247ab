import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './components/App';
import { Toaster } from 'react-hot-toast';

// Suppress React deprecation warnings globally for ReactQuill compatibility
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;

console.error = (...args) => {
  const message = args[0];
  if (typeof message === 'string') {
    // Suppress specific ReactQuill related deprecation warnings
    if (message.includes('findDOMNode') || 
        message.includes('DOMNodeInserted') ||
        message.includes('mutation event') ||
        (message.includes('Warning:') && message.includes('ReactQuill'))) {
      return;
    }
  }
  originalConsoleError.apply(console, args);
};

console.warn = (...args) => {
  const message = args[0];
  if (typeof message === 'string') {
    // Suppress specific ReactQuill related deprecation warnings
    if (message.includes('findDOMNode') || 
        message.includes('DOMNodeInserted') ||
        message.includes('mutation event') ||
        (message.includes('Warning:') && message.includes('ReactQuill'))) {
      return;
    }
  }
  originalConsoleWarn.apply(console, args);
};

try {
  const appElement = document.getElementById('app');
  // console.log('App element found:', appElement);
  
  if (!appElement) {
    throw new Error('Could not find app element');
  }
  
  const root = ReactDOM.createRoot(appElement);
  
  
  root.render(<App />);
  // console.log('App rendered successfully');
} catch (error) {
  console.error('Error initializing React app:', error);
  // Show error message in the DOM
  document.body.innerHTML = `
    <div style="padding: 20px; color: red; font-family: Arial;">
      <h1>React App Error</h1>
      <p>Error: ${error.message}</p>
      <p>Check browser console for details.</p>
    </div>
  `;
}
  