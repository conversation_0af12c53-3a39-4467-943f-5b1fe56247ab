import axios from 'axios';

// Create axios instance with default configuration
const apiClient = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add request interceptor to include auth token
apiClient.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
apiClient.interceptors.response.use(
    (response) => {
        return response;
    },
    (error) => {
        // Handle common HTTP errors
        if (error.response) {
            // The request was made and the server responded with an error status
            const { status, data } = error.response;
            
            if (status === 401) {
                // Unauthorized - clear token and redirect to login
                localStorage.removeItem('auth_token');
                // You can add redirect logic here if needed
                // window.location.href = '/login';
            } else if (status === 403) {
                // Forbidden - user doesn't have permission
                console.error('Access forbidden:', data.message || 'You do not have permission to perform this action');
            } else if (status === 422) {
                // Validation error
                console.error('Validation errors:', data.errors || data.message);
            } else if (status >= 500) {
                // Server error
                console.error('Server error:', data.message || 'Internal server error');
            }
        } else if (error.request) {
            // The request was made but no response was received
            console.error('Network error:', error.message);
        } else {
            // Something happened in setting up the request
            console.error('Request error:', error.message);
        }
        
        return Promise.reject(error);
    }
);

export default apiClient;