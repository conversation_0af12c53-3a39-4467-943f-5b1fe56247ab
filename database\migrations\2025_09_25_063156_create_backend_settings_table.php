<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('backend_settings', function (Blueprint $table) {
            $table->id();
            $table->string('applicationName')->nullable();
            $table->string('vatTaxRateForCustomers')->nullable();
            $table->string('vatTaxRateForMerchants')->nullable();
            $table->string('systemTimeZone')->nullable();
            $table->string('dateFormat')->nullable();
            $table->string('commissionFromMerchant')->nullable();
            $table->string('adminLogo')->nullable();
            $table->string('invoiceLogo')->nullable();
            $table->string('driver')->nullable();
            $table->string('host')->nullable();
            $table->string('port')->nullable();
            $table->string('username')->nullable();
            $table->string('password')->nullable();
            $table->string('fromAdress')->nullable();
            $table->string('fromName')->nullable();
            $table->string('encryption')->nullable();
            $table->string('enableRecaptcha')->nullable();
            $table->string('recaptchaKey')->nullable();
            $table->string('recaptchaSecret')->nullable();
            $table->string('whatsappChat')->nullable();
            $table->string('whatsappNumber')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('backend_settings');
    }
};
