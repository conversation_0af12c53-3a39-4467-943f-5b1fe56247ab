import React from "react";

const SellUsModal = () => {
  return (
    <div
      className="modal signUp-modal sell-with-us fade"
      id="sellUsModal01"
      tabIndex="-1"
      aria-labelledby="sellUsModal01Label"
      aria-hidden="true"
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-header">
            <h4 className="modal-title" id="sellUsModal01Label">
              Sell Your Car
            </h4>
            <button
              type="button"
              className="btn-close"
              data-bs-dismiss="modal"
              aria-label="Close"
            >
              <i className="bi bi-x"></i>
            </button>
          </div>
          <div className="modal-body">
            <form>
              <div className="row">
                <div className="col-lg-12 mb-15">
                  <h5>Your Personal Info</h5>
                </div>
                <div className="col-md-6 mb-20">
                  <div className="form-inner">
                    <label>Full Name*</label>
                    <input type="text" placeholder="Full Name*" />
                  </div>
                </div>
                <div className="col-md-6 mb-20">
                  <div className="form-inner">
                    <label>Phone*</label>
                    <input type="text" placeholder="+880- 123 234 ***" />
                  </div>
                </div>
                <div className="col-md-6 mb-20">
                  <div className="form-inner">
                    <label>Email (Optional)</label>
                    <input type="text" placeholder="Enter your email address" />
                  </div>
                </div>
                <div className="col-md-6 mb-20">
                  <div className="form-inner">
                    <label>Location*</label>
                    <input type="text" placeholder="Enter your address" />
                  </div>
                </div>
              </div>
              <div className="row">
                <div className="col-lg-12 mb-15 mt-25">
                  <h5>Your Car Info</h5>
                </div>
                <div className="col-md-6 mb-20">
                  <div className="form-inner">
                    <label>Car Brand Name*</label>
                    <input type="text" placeholder="Toyota" />
                  </div>
                </div>
                <div className="col-md-6 mb-20">
                  <div className="form-inner">
                    <label>Model*</label>
                    <input type="text" placeholder="RS eTN 80" />
                  </div>
                </div>
                <div className="col-md-6 mb-20">
                  <div className="form-inner">
                    <label>Reg. Year*</label>
                    <input type="text" placeholder="2022" />
                  </div>
                </div>
                <div className="col-md-6 mb-20">
                  <div className="form-inner">
                    <label>Mileage*</label>
                    <input type="text" placeholder="23,456 miles" />
                  </div>
                </div>
                <div className="col-md-6 mb-20">
                  <div className="form-inner">
                    <label>Fuel Type*</label>
                    <input type="text" placeholder="Petrol" />
                  </div>
                </div>
                <div className="col-md-6 mb-20">
                  <div className="form-inner">
                    <label>Selling Price*</label>
                    <input type="text" placeholder="Ex- $23,342.000" />
                  </div>
                </div>
                <div className="col-md-12 mb-35">
                  <div className="form-inner">
                    <label>Your Car Note*</label>
                    <textarea placeholder="Write somethings"></textarea>
                  </div>
                </div>
                <div className="col-lg-12">
                  <div className="form-inner">
                    <button className="primary-btn2" type="submit">
                      Submit Now
                    </button>
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SellUsModal;
