<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Menu;
use Illuminate\Http\JsonResponse;

class MenuController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        try {
            $menus = Menu::all();

            return response()->json([
                'success' => true,
                'data' => $menus
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch menus: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:menus,name',
                'slug' => 'required|string|max:255|unique:menus,slug',
                'status' => 'required|in:active,inactive',
                'parent_id' => 'nullable|exists:menus,id',
            ]);

            $menu = Menu::create($validated);

            return response()->json([
                'success' => true,
                'data' => $menu,
                'message' => 'Menu created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create menu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try {
            $menu = Menu::findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $menu
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch menu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        try {
            $menu = Menu::findOrFail($id);

            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:menus,name,' . $id,
                'slug' => 'required|string|max:255|unique:menus,slug,' . $id,
                'status' => 'nullable|in:active,inactive',
                'parent_id' => 'nullable|exists:menus,id',
            ]);

            $menu->update($validated);

            return response()->json([
                'success' => true,
                'data' => $menu->fresh(),
                'message' => 'Menu updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update menu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try {
            $menu = Menu::findOrFail($id);
            $menu->delete();

            return response()->json([
                'success' => true,
                'message' => 'Menu deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete menu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Build nested menu structure from flat list.
     */
    protected function buildNestedMenus($menus)
    {
        $items = [];
        foreach ($menus as $menu) {
            $items[$menu->id] = $menu->toArray();
            $items[$menu->id]['children'] = [];
        }

        $tree = [];
        foreach ($items as $id => &$item) {
            $parentId = $item['parent_id'] ?? null;
            if ($parentId && isset($items[$parentId])) {
                $items[$parentId]['children'][] = &$item;
            } else {
                $tree[] = &$item;
            }
        }

        return $tree;
    }

    /**
     * Get menu by slug.
     */
    public function getBySlug(string $slug): JsonResponse
    {
        try {
            $menu = Menu::where('slug', $slug)->first();

            if (!$menu) {
                return response()->json([
                    'success' => false,
                    'message' => 'Menu not found'
                ], 404);
            }

            return response()->json([
                'success' => true,
                'data' => $menu
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch menu: ' . $e->getMessage()
            ], 500);
        }
    }
}
