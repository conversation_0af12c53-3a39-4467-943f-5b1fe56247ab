<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Menu;
use Illuminate\Http\JsonResponse;
class MenuController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        //
        try {
            $query = Menu::query();
            $menus = $query->OrderBy('position','ASC')->get();

            // Build nested structure
            $nestedMenus = $this->buildNestedMenus($menus);

            return response()->json([
                'success' => true,
                'data' => $nestedMenus
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch menus: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:menus,name',
                'is_active' => 'boolean',
            ]);
            $menu = Menu::create($validated);
            return response()->json([
                'success' => true,
                'data' => $menu,
                'message' => 'Menu created successfully'
            ], 201);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to create menu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id):JsonResponse
    {
        //
        try {
            $menu = Menu::findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $menu
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch menu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id):JsonResponse
    {
        //
        try {
            $menu = Menu::findOrFail($id);
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:menus,name,' . $id,
                'is_active' => 'boolean',
            ]);
            $menu->update($validated);
            return response()->json([
                'success' => true,
                'data' => $menu->fresh(),
                'message' => 'Menu updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update menu: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id):JsonResponse
    {
        //
        try {
            $menu = Menu::findOrFail($id);
            $menu->delete();
            return response()->json([
                'success' => true,
                'message' => 'Menu deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete menu: ' . $e->getMessage()
            ], 500);
        }
    }

    public function reorder(Request $request):JsonResponse
    {
        try{
            $validate=$request->validate([
                'menus' => 'required|array',
                'menus.*.id'=> 'required|integer|exists:menus,id',
                'menus.*.parentId'=> 'nullable|integer|exists:menus,id',
                'menus.*.childPosition'=> 'nullable|integer|min:0'
            ]);

            // Flatten the nested structure and update each menu
            $this->updateNestedMenus($request->menus);

            return response()->json([
                'success' => true,
                'message' => 'Menu reordered successfully'],200);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder menu: ' . $e->getMessage()
            ], 500);
        }
    }

    private function updateNestedMenus($menus, $parentId = null, $level = 0)
    {
        foreach($menus as $index => $menuData){
            $position = $level === 0 ? $index + 1 : $menuData['childPosition'] ?? ($index + 1);

            Menu::where('id', $menuData['id'])->update([
                'parentId' => $parentId,
                'position' => $position,
                'childPosition' => $level > 0 ? $position : null
            ]);

            // Recursively update children
            if(isset($menuData['children']) && is_array($menuData['children'])){
                $this->updateNestedMenus($menuData['children'], $menuData['id'], $level + 1);
            }
        }
    }

    private function buildNestedMenus($menus)
    {
        $menuById = [];
        $rootMenus = [];

        // Index menus by id
        foreach ($menus as $menu) {
            $menuById[$menu->id] = $menu->toArray();
            $menuById[$menu->id]['children'] = [];
        }

        // Build tree
        foreach ($menuById as $id => &$menu) {
            if ($menu['parentId']) {
                $menuById[$menu['parentId']]['children'][] = &$menu;
            } else {
                $rootMenus[] = &$menu;
            }
        }

        return $rootMenus;
    }
}
