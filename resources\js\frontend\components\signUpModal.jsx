import React from "react";

const SignUpModal = () => {
  return (
    <div
      className="modal signUp-modal two fade"
      id="signUpModal01"
      tabIndex="-1"
      aria-labelledby="signUpModal01Label"
      aria-hidden="true"
    >
      <div className="modal-dialog modal-dialog-centered">
        <div className="modal-content">
          <div className="modal-body">
            <div className="login-wrapper">
              <div className="login-img">
                <img src="/Frontend/assets/img/home1/login-img.png" alt="login" />
                <div className="logo">
                  <a href="index.html">
                    <img src="/Frontend/assets/img/logo.svg" alt="logo" />
                  </a>
                </div>
              </div>

              <div className="login-content">
                <div className="login-header">
                  <h4 className="modal-title" id="signUpModal01Label">
                    Sign Up
                  </h4>
                  <p>
                    Already have an account?{" "}
                    <button
                      type="button"
                      data-bs-toggle="modal"
                      data-bs-target="#logInModal01"
                    >
                      Log In
                    </button>
                  </p>
                  <button
                    type="button"
                    className="btn-close"
                    data-bs-dismiss="modal"
                    aria-label="Close"
                  >
                    <i className="bi bi-x"></i>
                  </button>
                </div>

                <form>
                  <div className="row g-4">
                    <div className="col-md-6">
                      <div className="form-inner">
                        <label>First Name*</label>
                        <input type="text" placeholder="Daniel" />
                      </div>
                    </div>

                    <div className="col-md-6">
                      <div className="form-inner">
                        <label>Last Name*</label>
                        <input type="text" placeholder="Last name" />
                      </div>
                    </div>

                    <div className="col-md-12">
                      <div className="form-inner">
                        <label>Enter your email address*</label>
                        <input type="email" placeholder="Type email" />
                      </div>
                    </div>

                    <div className="col-md-6">
                      <div className="form-inner">
                        <label>Password*</label>
                        <input id="password" type="password" placeholder="*** ***" />
                        <i className="bi bi-eye-slash" id="togglePassword"></i>
                      </div>
                    </div>

                    <div className="col-md-6">
                      <div className="form-inner">
                        <label>Confirm Password*</label>
                        <input id="password2" type="password" placeholder="*** ***" />
                        <i className="bi bi-eye-slash" id="togglePassword2"></i>
                      </div>
                    </div>

                    <div className="col-md-12">
                      <div className="form-inner">
                        <button className="primary-btn2" type="submit">
                          Sign Up Now
                        </button>
                      </div>
                    </div>
                  </div>

                  <div className="terms-conditon">
                    <p>
                      By sign up, you agree to the{" "}
                      <a href="#">‘terms & conditions’</a>
                    </p>
                  </div>

                  <ul className="social-icon">
                    <li>
                      <a href="#">
                        <img src="/Frontend/assets/img/home1/icon/google.svg" alt="Google" />
                      </a>
                    </li>
                    <li>
                      <a href="#">
                        <img src="/Frontend/assets/img/home1/icon/facebook.svg" alt="Facebook" />
                      </a>
                    </li>
                    <li>
                      <a href="#">
                        <img src="/Frontend/assets/img/home1/icon/twiter.svg" alt="Twitter" />
                      </a>
                    </li>
                  </ul>
                </form>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignUpModal;
