import React from "react";

const SearchDreamProperty = () => {
  return (
    <>
      {/* Start Dream Property section */}
      <div className="dream-property-area pt-100 pb-100 mb-100">
        <div className="container">
          <div className="row mb-50 wow fadeInUp" data-wow-delay="200ms">
            <div className="col-lg-12">
              <div className="section-title-2 text-center">
                <h2>Search Dream Property</h2>
                <p>There has 30+ Apartment Types Available</p>
              </div>
            </div>
          </div>

          <div className="row wow fadeInUp" data-wow-delay="300ms">
            <div className="col-lg-12">
              <div className="filter-area d-flex flex-wrap align-items-center justify-content-between">
                <ul className="nav nav-pills" id="pills-tab" role="tablist">
                  <li className="nav-item" role="presentation">
                    <button
                      className="nav-link active"
                      id="pills-make-tab"
                      data-bs-toggle="pill"
                      data-bs-target="#pills-make"
                      type="button"
                      role="tab"
                      aria-controls="pills-make"
                      aria-selected="true"
                    >
                      For Sale
                    </button>
                  </li>
                  <li className="nav-item" role="presentation">
                    <button
                      className="nav-link"
                      id="pills-body-tab"
                      data-bs-toggle="pill"
                      data-bs-target="#pills-body"
                      type="button"
                      role="tab"
                      aria-controls="pills-body"
                      aria-selected="false"
                    >
                      For Rent
                    </button>
                  </li>
                  <li className="nav-item" role="presentation">
                    <button
                      className="nav-link"
                      id="pills-location-tab"
                      data-bs-toggle="pill"
                      data-bs-target="#pills-location"
                      type="button"
                      role="tab"
                      aria-controls="pills-location"
                      aria-selected="false"
                    >
                      Location
                    </button>
                  </li>
                </ul>

                <div className="slider-btn-group2 width-90">
                  <div className="slider-btn prev-13">
                    <svg
                      width="9"
                      height="15"
                      viewBox="0 0 8 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M0 6.50008L8 0L2.90909 6.50008L8 13L0 6.50008Z"></path>
                    </svg>
                  </div>
                  <div className="slider-btn next-13">
                    <svg
                      width="9"
                      height="15"
                      viewBox="0 0 8 13"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <path d="M8 6.50008L0 0L5.09091 6.50008L0 13L8 6.50008Z"></path>
                    </svg>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Tab Content */}
          <div className="tab-content" id="pills-tabContent">
            {/* For Sale */}
            <div
              className="tab-pane fade show active"
              id="pills-make"
              role="tabpanel"
              aria-labelledby="pills-make-tab"
            >
              <div className="row g-4 justify-content-center">
                <div className="col-lg-12">
                  <div className="swiper dream-category-slider">
                    <div className="swiper-wrapper">
                      <div className="swiper-slide">
                        <a
                          href="property-listing-left-sidebar.html"
                          className="car-category text-center"
                        >
                          <div className="icon">
                            <img
                              src="/Frontend/assets/img/home2/icon/home.svg"
                              alt="Town Home"
                            />
                          </div>
                          <div className="content">
                            <h6>Town Home</h6>
                            <span>(1,02)</span>
                          </div>
                        </a>
                      </div>

                      <div className="swiper-slide">
                        <a
                          href="property-listing-left-sidebar.html"
                          className="car-category text-center"
                        >
                          <div className="icon">
                            <img
                              src="/Frontend/assets/img/home2/icon/industrial.svg"
                              alt="Industrial"
                            />
                          </div>
                          <div className="content">
                            <h6>Industrial</h6>
                            <span>(3,45)</span>
                          </div>
                        </a>
                      </div>

                      <div className="swiper-slide">
                        <a
                          href="property-listing-left-sidebar.html"
                          className="car-category text-center"
                        >
                          <div className="icon">
                            <img
                              src="/Frontend/assets/img/home2/icon/devlopment.svg"
                              alt="Development"
                            />
                          </div>
                          <div className="content">
                            <h6>Development</h6>
                            <span>(1,00)</span>
                          </div>
                        </a>
                      </div>

                      <div className="swiper-slide">
                        <a
                          href="property-listing-left-sidebar.html"
                          className="car-category text-center"
                        >
                          <div className="icon">
                            <img
                              src="/Frontend/assets/img/home2/icon/hotel.svg"
                              alt="Hotel"
                            />
                          </div>
                          <div className="content">
                            <h6>Hotel</h6>
                            <span>(3,46)</span>
                          </div>
                        </a>
                      </div>

                      <div className="swiper-slide">
                        <a
                          href="property-listing-left-sidebar.html"
                          className="car-category text-center"
                        >
                          <div className="icon">
                            <img
                              src="/Frontend/assets/img/home2/icon/health-care.svg"
                              alt="Health & Care"
                            />
                          </div>
                          <div className="content">
                            <h6>Health & Care</h6>
                            <span>(1,22)</span>
                          </div>
                        </a>
                      </div>

                      <div className="swiper-slide">
                        <a
                          href="property-listing-left-sidebar.html"
                          className="car-category text-center"
                        >
                          <div className="icon">
                            <img
                              src="/Frontend/assets/img/home2/icon/office.svg"
                              alt="Office"
                            />
                          </div>
                          <div className="content">
                            <h6>Office</h6>
                            <span>(10)</span>
                          </div>
                        </a>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* For Rent */}
            <div
              className="tab-pane fade"
              id="pills-body"
              role="tabpanel"
              aria-labelledby="pills-body-tab"
            >
              {/* Same structure as For Sale, repeat or map dynamically */}
            </div>

            {/* Location */}
            <div
              className="tab-pane fade"
              id="pills-location"
              role="tabpanel"
              aria-labelledby="pills-location-tab"
            >
              {/* Same structure as above with location icons */}
            </div>
          </div>

          <div className="explore-btn d-lg-none d-flex pt-40">
            <a className="explore-btn2" href="brand-category.html">
              Explore More <i className="bi bi-arrow-right-short"></i>
            </a>
          </div>
        </div>
      </div>
      {/* End Dream Property section */}
    </>
  );
};

export default SearchDreamProperty;
