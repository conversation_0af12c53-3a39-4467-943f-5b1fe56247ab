import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from '@/hooks/useTranslation';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Plus,
  Search,
  MapPin,
  Globe,
  Navigation,
  Download
} from 'lucide-react';
import Swal from 'sweetalert2';
import { stateAPI } from '../../services/stateAPI';
import { countryAPI } from '../../services/countryAPI';
import StateModal from './StateModal';
import StateTable from './StateTable';

const StatePage = () => {
  const { t } = useTranslation();
  
  // Add error boundary for this component
  const [componentError, setComponentError] = useState(null);
  
  // Add fallback for translation function
  const translate = (key) => {
    try {
      return t ? t(key) : key;
    } catch (error) {
      console.warn('Translation error for key:', key);
      return key;
    }
  };

  // Add error handling
  React.useEffect(() => {
    const handleError = (error) => {
      setComponentError(error.message);
    };
    
    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (componentError) {
    return (
      <div className="p-6">
        <h2 className="text-2xl font-bold mb-4">State Management</h2>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">Error: {componentError}</p>
          <button 
            onClick={() => setComponentError(null)} 
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  // State management
  const [states, setStates] = useState([]);
  const [countries, setCountries] = useState([]);
  const [loading, setLoading] = useState(false);
  const [countriesLoading, setCountriesLoading] = useState(false);

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage] = useState(15);

  // Filter state
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingState, setEditingState] = useState(null);

  // Statistics
  const [statistics, setStatistics] = useState({
    total_states: 0,
    active_states: 0,
    inactive_states: 0,
    states_by_country: [],
    states_by_type: {}
  });

  // Load data on component mount
  useEffect(() => {
    console.log('StatePage: Component mounted, loading data...');
    try {
      loadStates();
      loadCountries();
      loadStatistics();
    } catch (error) {
      console.error('StatePage: Error in useEffect:', error);
      setComponentError(error.message);
    }
  }, [currentPage, searchTerm, selectedCountry, selectedStatus]);

  // Load states
  const loadStates = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        per_page: itemsPerPage,
        search: searchTerm,
        country_id: selectedCountry === 'all' ? '' : selectedCountry,
        status: selectedStatus === 'all' ? '' : selectedStatus
      };
      
      // Try public paginated endpoint first, fallback to dropdown or authenticated endpoint
      let response = null;
      let endpoint = '';
      
      try {
        console.log('StatePage: Trying public states index endpoint...');
        endpoint = 'public.getStates';
        response = await stateAPI.public.getStates(params);
        console.log('StatePage: Public states index response:', response);
      } catch (publicError) {
        console.warn('StatePage: Public states index endpoint failed, trying dropdown:', publicError);
        try {
          endpoint = 'public.getStatesDropdown';
          response = await stateAPI.public.getStatesDropdown(params);
          console.log('StatePage: Public states dropdown response:', response);
        } catch (dropdownError) {
          console.warn('StatePage: Public dropdown failed, trying authenticated:', dropdownError);
          endpoint = 'getStates';
          response = await stateAPI.getStates(params);
        }
      }
      
      // Handle the response structure safely
      if (response.success && response.data) {
        setStates(response.data || []);
        if (response.pagination) {
          setTotalPages(response.pagination.last_page || 1);
          setTotalItems(response.pagination.total || 0);
        } else {
          // For dropdown endpoints without pagination
          setTotalPages(1);
          setTotalItems(response.data.length || 0);
        }
        console.log(`StatePage: Loaded ${response.data.length} states using ${endpoint}`);
      } else {
        console.warn('StatePage: Response not successful:', response);
        setStates([]);
        setTotalPages(1);
        setTotalItems(0);
      }
    } catch (err) {
      console.error('Error loading states:', err);
      setStates([]);
      setTotalPages(1);
      setTotalItems(0);
    } finally {
      setLoading(false);
    }
  };

  // Load countries for dropdown
  const loadCountries = async () => {
    try {
      setCountriesLoading(true);
      console.log('StatePage: Loading countries...');
      
      // Try multiple endpoints as fallback
      let response = null;
      let endpoint = '';
      
      try {
        // First try the public endpoint
        endpoint = 'public.getCountriesDropdown';
        console.log('StatePage: Trying public endpoint...');
        response = await countryAPI.public.getCountriesDropdown();
      } catch (publicError) {
        console.warn('StatePage: Public endpoint failed:', publicError);
        
        try {
          // Fallback to authenticated endpoint
          endpoint = 'getCountriesDropdown';
          console.log('StatePage: Trying authenticated endpoint...');
          response = await countryAPI.getCountriesDropdown();
        } catch (authError) {
          console.warn('StatePage: Authenticated endpoint failed:', authError);
          throw authError;
        }
      }
      
      console.log('StatePage: Countries response from', endpoint, ':', response);
      
      if (response && response.success && response.data) {
        setCountries(response.data || []);
        console.log('StatePage: Countries loaded successfully:', response.data.length, 'countries');
      } else {
        console.warn('StatePage: Countries response not successful:', response);
        setCountries([]);
      }
    } catch (err) {
      console.error('Error loading countries:', err);
      setCountries([]);
    } finally {
      setCountriesLoading(false);
    }
  };

  // Load statistics
  const loadStatistics = async () => {
    try {
      const response = await stateAPI.getStatistics();
      if (response.success) {
        setStatistics(response.data);
      }
    } catch (err) {
      console.error('Error loading statistics:', err);
    }
  };

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page when searching
  };

  // Handle country filter
  const handleCountryFilter = (countryId) => {
    setSelectedCountry(countryId);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle status filter
  const handleStatusFilter = (status) => {
    setSelectedStatus(status);
    setCurrentPage(1); // Reset to first page when filtering
  };

  // Handle add new state
  const handleAddNew = () => {
    setEditingState(null);
    setIsModalOpen(true);
  };

  // Handle edit
  const handleEdit = (state) => {
    setEditingState(state);
    setIsModalOpen(true);
  };

  // Handle delete
  const handleDelete = async (state) => {
    const result = await Swal.fire({
      title: t('common.messages.confirmDelete'),
      text: t('state.messages.confirmDelete'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: t('common.buttons.delete'),
      cancelButtonText: t('common.buttons.cancel')
    });

    if (result.isConfirmed) {
      try {
        await stateAPI.deleteState(state.id);
        loadStates();
        loadStatistics();
        Swal.fire({
          icon: 'success',
          title: t('common.messages.success'),
          text: t('state.messages.deleteSuccess')
        });
      } catch (err) {
        console.error('Error deleting state:', err);
        Swal.fire({
          icon: 'error',
          title: t('common.messages.error'),
          text: t('state.messages.deleteError')
        });
      }
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (state) => {
    try {
      await stateAPI.toggleStatus(state.id);
      loadStates();
      loadStatistics();
    } catch (err) {
      console.error('Error toggling status:', err);
      Swal.fire({
        icon: 'error',
        title: t('common.messages.error'),
        text: 'Failed to update status'
      });
    }
  };

  // Handle modal success (refresh data)
  const handleModalSuccess = () => {
    loadStates();
    loadStatistics();
  };

  // Handle export
  const handleExport = async () => {
    try {
      // Implement export functionality here
      Swal.fire({
        icon: 'info',
        title: 'Export',
        text: 'Export functionality will be implemented soon'
      });
    } catch (err) {
      console.error('Error exporting:', err);
    }
  };

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('state.title')}</h1>
          <p className="text-muted-foreground mt-1">
            Manage states and provinces in your system
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button onClick={handleExport} variant="outline" size="sm">
            <Download className="h-4 w-4 mr-2" />
            Export
          </Button>
          <Button onClick={handleAddNew} size="sm">
            <Plus className="h-4 w-4 mr-2" />
            {t('state.addState')}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total States</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{statistics.total_states}</div>
            <p className="text-xs text-muted-foreground">
              All states in system
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active States</CardTitle>
            <Globe className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{statistics.active_states}</div>
            <p className="text-xs text-muted-foreground">
              Currently active
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Inactive States</CardTitle>
            <Navigation className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{statistics.inactive_states}</div>
            <p className="text-xs text-muted-foreground">
              Currently inactive
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card>
        <CardHeader>
          <CardTitle>{t('state.filters')}</CardTitle>
          <CardDescription>
            Filter and search states by various criteria
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col sm:flex-row gap-4">
            {/* Search */}
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search states..."
                  value={searchTerm}
                  onChange={handleSearch}
                  className="pl-10"
                />
              </div>
            </div>
            
            {/* Country Filter */}
            <div className="w-full sm:w-48">
              <Select value={selectedCountry} onValueChange={handleCountryFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Countries" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Countries</SelectItem>
                  {countries.map(country => (
                    <SelectItem key={country.id} value={country.id.toString()}>
                      {country.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            {/* Status Filter */}
            <div className="w-full sm:w-32">
              <Select value={selectedStatus} onValueChange={handleStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="All Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="inactive">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* States Table */}
      <Card>
        <CardHeader>
          <CardTitle>{t('state.list')}</CardTitle>
          <CardDescription>
            A list of all states in your system
          </CardDescription>
        </CardHeader>
        <CardContent>
          <StateTable
            states={states}
            loading={loading}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onEdit={handleEdit}
            onDelete={handleDelete}
            onToggleStatus={handleToggleStatus}
            onPageChange={handlePageChange}
          />
        </CardContent>
      </Card>

      {/* State Modal */}
      <StateModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        editingState={editingState}
        onSuccess={handleModalSuccess}
      />
    </div>
  );
};

export default StatePage;