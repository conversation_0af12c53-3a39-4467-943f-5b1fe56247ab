<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class LeaseTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('lease_types')->insert([
            [
                'name' => 'Short-Term Lease',
                'description' => 'Lease agreement typically for 6 months or less, ideal for flexibility.',
                'is_active' => true,
                'sort_order' => 1,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Long-Term Lease',
                'description' => 'Lease agreement for 12 months or more, ideal for stability.',
                'is_active' => true,
                'sort_order' => 2,
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Month-to-Month Lease',
                'description' => 'Renews every month until terminated by either party.',
                'is_active' => false,
                'sort_order' => 3,
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
