import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { TabsContent } from "@/components/ui/tabs";

const GoogleLogin = ({ formData, handleInputChange, handleSubmit, loading }) => {
    return (
        <TabsContent value="google-login" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                    <Label htmlFor="googleLoginClientId">Google Client ID</Label>
                    <Input
                        id="googleLoginClientId"
                        value={formData.googleLoginClientId || ""}
                        onChange={(e) => handleInputChange('googleLoginClientId', e.target.value)}
                        placeholder="Google Client ID"
                    />
                </div>
                <div>
                    <Label htmlFor="googleLoginClientSecret">Google Client Secret</Label>
                    <Input
                        id="googleLoginClientSecret"
                        value={formData.googleClientSecret || ""}
                        onChange={(e) => handleInputChange('googleLoginClientSecret', e.target.value)}
                        placeholder="Google Client Secret"
                    />
                </div>
                <div>
                    <Label htmlFor="googleLoginRedirectionUrl">Google Redirection URL</Label>
                    <Input
                        id="googleLoginRedirectionUrl"
                        value={formData.googleLoginRedirectionUrl || ""}
                        onChange={(e) => handleInputChange('googleLoginRedirectionUrl', e.target.value)}
                        placeholder="Google Redirection URL"
                    />
                </div>
            </div>

            <div className="flex justify-end">
                <button
                onClick={handleSubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
                disabled={loading}
                >
                {loading ? "Saving..." : "Save Settings"}
                </button>
            </div>
        </TabsContent>
    )};
export default GoogleLogin;
