import React from "react";

const Sidebar = () => {
  return (
    <div className="sidebar-menu">
      <div className="mobile-logo-area d-flex justify-content-between align-items-center">
        <div className="mobile-logo-wrap">
          <a href="index.html">
            <img alt="image" src="/Frontend/assets/img/sb-logo.svg" />
          </a>
        </div>
        <div className="menu-button menu-close-btn">
          <i className="bi bi-x"></i>
        </div>
      </div>

      <ul className="menu-list">
        <li className="menu-item-has-children active">
          <a href="#" className="drop-down">
            Home
          </a>
          <i className="bi bi-plus dropdown-icon"></i>
          <ul className="sub-menu">
            <li><a href="index.html">Home 01</a></li>
            <li><a href="index2.html">Home 02</a></li>
            <li><a href="index3.html">Home 03</a></li>
            <li><a href="index4.html">Home 04</a></li>
            <li><a href="index5.html">Home 05</a></li>
            <li><a href="index6.html">Home 06</a></li>
          </ul>
        </li>

        <li className="position-inherit">
          <a href="#" className="drop-down">FOR SALE</a>
          <i className="bi bi-plus dropdown-icon"></i>
          <div className="mega-menu">
            <ul className="menu-row">
              <li className="menu-single-item">
                <h6>Apartment Types</h6>
                <ul>
                  <li><a href="poperty-listing-no-sidebar.html">Houses (10)</a></li>
                  <li><a href="poperty-listing-no-sidebar.html">Industires (13)</a></li>
                  <li><a href="poperty-listing-no-sidebar.html">Home Twon (33)</a></li>
                  <li><a href="poperty-listing-no-sidebar.html">Development (15)</a></li>
                  <li><a href="poperty-listing-no-sidebar.html">Health Care (20)</a></li>
                  <li><a href="poperty-listing-no-sidebar.html">Office (10)</a></li>
                  <li><a href="poperty-listing-no-sidebar.html">Banglow (10)</a></li>
                  <li><a href="poperty-listing-no-sidebar.html">Hotel (10)</a></li>
                  <li className="explore-more-btn">
                    <a href="poperty-listing-no-sidebar.html">
                      Explore More <i className="bi bi-arrow-right-short"></i>
                    </a>
                  </li>
                </ul>
              </li>

              <li className="menu-single-item">
                <h6>Popular Cities </h6>
                <ul>
                  <li><a href="poperty-listing-no-sidebar.html">Panama City (10)<img src="/Frontend/assets/img/menu-icon/panama.svg" alt="" /></a></li>
                  <li><a href="poperty-listing-no-sidebar.html">Sydne City (10)<img src="/Frontend/assets/img/menu-icon/sydne.svg" alt="" /></a></li>
                  <li><a href="poperty-listing-no-sidebar.html">Melbourne City (10)<img src="/Frontend/assets/img/menu-icon/melbourne.svg" alt="" /></a></li>
                  <li><a href="poperty-listing-no-sidebar.html">New Delhi (10)<img src="/Frontend/assets/img/menu-icon/delhi.svg" alt="" /></a></li>
                  <li><a href="poperty-listing-no-sidebar.html">New York (10)<img src="/Frontend/assets/img/menu-icon/newYork.svg" alt="" /></a></li>
                  <li><a href="poperty-listing-no-sidebar.html">Menchester City (10)<img src="/Frontend/assets/img/menu-icon/menchester.svg" alt="" /></a></li>
                  <li><a href="poperty-listing-no-sidebar.html">City of Greece (10)<img src="/Frontend/assets/img/menu-icon/greece.svg" alt="" /></a></li>
                  <li><a href="poperty-listing-no-sidebar.html">City of Abu-dabi (10)<img src="/Frontend/assets/img/menu-icon/abudabi.svg" alt="" /></a></li>
                  <li className="explore-more-btn">
                    <a href="poperty-listing-no-sidebar.html">
                      Explore More <i className="bi bi-arrow-right-short"></i>
                    </a>
                  </li>
                </ul>
              </li>
            </ul>
          </div>
        </li>

        {/* Repeat similar pattern for FOR RENT, PROPERTY, Pages, CONTACT US, etc. */}
      </ul>

      <div className="topbar-right">
        <button
          type="button"
          className="modal-btn header-user-btn sell-btn"
          data-bs-toggle="modal"
          data-bs-target="#logInModal01"
        >
          REGISTER/ LOGIN
        </button>

        <a className="primary-btn3" href="dashboard-add-property.html">
          ADD PROPERTY
        </a>
      </div>
    </div>
  );
};

export default Sidebar;
