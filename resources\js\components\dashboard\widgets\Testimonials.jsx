import React, { useState, useEffect } from "react";

const Testimonials = ({ widget, handleConfigUpdate, handleSubmit }) => {
  const [ratingLogoPreview, setRatingLogoPreview] = useState(
    widget.config?.ratingLogo || ""
  );

  const items = widget.config?.items || [
    { customerName: "", ratingPoint: "", customerReview: "" },
  ];

  const addItem = () => {
    const newItems = [
      ...items,
      { customerName: "", ratingPoint: "", customerReview: "" },
    ];
    handleConfigUpdate(widget.id, "items", newItems);
  };

  const removeItem = (index) => {
    const newItems = items.filter((_, i) => i !== index);
    handleConfigUpdate(widget.id, "items", newItems);
  };

  const updateItem = (index, field, value) => {
    const newItems = [...items];
    newItems[index][field] = value;
    handleConfigUpdate(widget.id, "items", newItems);
  };

  useEffect(() => {
    setRatingLogoPreview(widget.config?.ratingLogo || "");
  }, [widget.config]);

  const handleRatingLogoChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append("image", file);

      try {
        const response = await fetch("/api/page-contents/upload-image", {
          method: "POST",
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          setRatingLogoPreview(data.data.url);
          handleConfigUpdate(widget.id, "ratingLogo", data.data.url);
        } else {
          alert("Rating logo upload failed: " + data.message);
        }
      } catch (error) {
        alert("Error uploading rating logo: " + error.message);
      }
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Title and Sub Title */}
      <div className="flex gap-4">
        <div className="flex-1">
          <label>Title</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={widget.config?.title || ""}
            onChange={(e) =>
              handleConfigUpdate(widget.id, "title", e.target.value)
            }
            placeholder="Title"
          />
        </div>
        <div className="flex-1">
          <label>Sub Title</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={widget.config?.subTitle || ""}
            onChange={(e) =>
              handleConfigUpdate(widget.id, "subTitle", e.target.value)
            }
            placeholder="Sub Title"
          />
        </div>
      </div>

      {/* Rating Logo */}
      <div className="flex gap-4">
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">
            Rating Logo Upload
          </label>
          <input
            type="file"
            accept="image/*"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            onChange={handleRatingLogoChange}
          />
        </div>
        {ratingLogoPreview && (
          <div className="flex items-center">
            <img
              src={ratingLogoPreview}
              alt="Rating Logo Preview"
              className="w-16 h-16 object-cover rounded-md border"
            />
          </div>
        )}
      </div>

      {/* Items */}
      <div className="space-y-6">
        {items.map((item, index) => (
          <div key={index} className="border p-4 rounded-md space-y-4">
            <div className="flex gap-4">
              <div className="flex-1">
                <label>Customer Name</label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                  value={item.customerName}
                  onChange={(e) =>
                    updateItem(index, "customerName", e.target.value)
                  }
                  placeholder="Customer Name"
                />
              </div>
              <div className="flex-1">
                <label>Rating Point</label>
                <input
                  type="number"
                  className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                  value={item.ratingPoint}
                  onChange={(e) =>
                    updateItem(index, "ratingPoint", e.target.value)
                  }
                  placeholder="e.g. 4.5"
                />
              </div>
            </div>

            {/* Customer Review + Remove Button on the same line */}
            <div className="flex gap-4 items-end">
              <div className="flex-1">
                <label>Customer Review</label>
                <input
                  type="text"
                  className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                  value={item.customerReview}
                  onChange={(e) =>
                    updateItem(index, "customerReview", e.target.value)
                  }
                  placeholder="Customer Review"
                />
              </div>

              <button
                type="button"
                className="px-3 py-2 bg-red-500 text-white rounded h-fit"
                onClick={() => removeItem(index)}
              >
                Remove
              </button>
            </div>
          </div>
        ))}

        {/* Add Item */}
        <button
          type="button"
          className="px-4 py-2 bg-blue-500 text-white rounded"
          onClick={addItem}
        >
          Add Item
        </button>
      </div>
    </form>
  );
};

export default Testimonials;
