<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('menu_manages', function (Blueprint $table) {
            $table->id();
            $table->integer('menu_position_id');
            $table->string('title');
            $table->string('slug');
            $table->string('type');
            $table->string('target');
            $table->integer('parent_id')->nullable();
            $table->integer('order');
            $table->integer('new_tap');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('menu_manages');
    }
};
