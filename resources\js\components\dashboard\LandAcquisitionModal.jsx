import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { landAcquisitionAPI } from '@/services/landAcquisitionAPI';
import { countryAPI } from '@/services/countryAPI';
import { stateAPI } from '@/services/stateAPI';
import { Plus, Edit, Eye, X } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import Swal from 'sweetalert2';

const LandAcquisitionModal = ({ isOpen, onClose, onSave, editingRecord, mode, landOwners }) => {
    // Form state
    const [formData, setFormData] = useState({
        // Land acquisition fields
        dag_number: '',
        khatian_number: '',
        mauza: '',
        land_size: '',
        acquisition_price: '',
        cs_khatian: '',
        rs_khatian: '',
        bs_khatian: '',
        sa_khatian: '',
        
        // Land owner selection
        land_owner_id: '',
        use_existing_owner: true,
        
        // New land owner fields
        first_name: '',
        last_name: '',
        father_name: '',
        mother_name: '',
        phone: '',
        nid_number: '',
        email: '',
        address: '',
        document_type: '',
        
        // Land address
        land_address: {
            country_id: '',
            state_id: '',
            city: '',
            specific_address: ''
        }
    });

    // File state
    const [files, setFiles] = useState({
        photo: null,
        nid_front: null,
        nid_back: null,
        passport_photo: null
    });

    // Dropdown data
    const [countries, setCountries] = useState([]);
    const [states, setStates] = useState([]);
    const [loading, setLoading] = useState(false);

    // Load dropdown data
    useEffect(() => {
        loadCountries();
    }, []);

    // Load states when country changes
    useEffect(() => {
        if (formData.land_address.country_id) {
            loadStates(formData.land_address.country_id);
        }
    }, [formData.land_address.country_id]);

    // Initialize form data when editing
    useEffect(() => {
        if (editingRecord && mode !== 'create') {
            setFormData({
                dag_number: editingRecord.dag_number || '',
                khatian_number: editingRecord.khatian_number || '',
                mauza: editingRecord.mauza || '',
                land_size: editingRecord.land_size || '',
                acquisition_price: editingRecord.acquisition_price || '',
                cs_khatian: editingRecord.cs_khatian || '',
                rs_khatian: editingRecord.rs_khatian || '',
                bs_khatian: editingRecord.bs_khatian || '',
                sa_khatian: editingRecord.sa_khatian || '',
                
                land_owner_id: editingRecord.land_owner?.id || '',
                use_existing_owner: true,
                
                first_name: editingRecord.land_owner?.first_name || '',
                last_name: editingRecord.land_owner?.last_name || '',
                father_name: editingRecord.land_owner?.father_name || '',
                mother_name: editingRecord.land_owner?.mother_name || '',
                phone: editingRecord.land_owner?.phone || '',
                nid_number: editingRecord.land_owner?.nid_number || '',
                email: editingRecord.land_owner?.email || '',
                address: editingRecord.land_owner?.address || '',
                document_type: editingRecord.land_owner?.document_type || '',
                
                land_address: {
                    country_id: editingRecord.land_address?.country_id || '',
                    state_id: editingRecord.land_address?.state_id || '',
                    city: editingRecord.land_address?.city || '',
                    specific_address: editingRecord.land_address?.specific_address || ''
                }
            });
        }
    }, [editingRecord, mode]);

    // Load countries
    const loadCountries = async () => {
        try {
            const response = await countryAPI.getCountries({ per_page: 250 });
            if (response.success) {
                setCountries(response.data);
            }
        } catch (error) {
            console.error('Error loading countries:', error);
        }
    };

    // Load states
    const loadStates = async (countryId) => {
        try {
            const response = await stateAPI.getStatesByCountry(countryId, { per_page: 250 });
            if (response.success) {
                setStates(response.data);
            }
        } catch (error) {
            console.error('Error loading states:', error);
        }
    };

    // Handle input change
    const handleInputChange = (field, value) => {
        if (field.includes('.')) {
            const [parent, child] = field.split('.');
            setFormData(prev => ({
                ...prev,
                [parent]: {
                    ...prev[parent],
                    [child]: value
                }
            }));
        } else {
            setFormData(prev => ({
                ...prev,
                [field]: value
            }));
        }
    };

    // Handle file change
    const handleFileChange = (field, file) => {
        setFiles(prev => ({
            ...prev,
            [field]: file
        }));
    };

    // Handle form submit
    const handleSubmit = async (e) => {
        e.preventDefault();
        
        if (mode === 'view') {
            onClose();
            return;
        }

        try {
            setLoading(true);

            // Create FormData for file uploads
            const submitData = new FormData();

            // Add land acquisition fields
            submitData.append('dag_number', formData.dag_number);
            submitData.append('khatian_number', formData.khatian_number);
            submitData.append('mauza', formData.mauza);
            submitData.append('land_size', formData.land_size);
            submitData.append('acquisition_price', formData.acquisition_price);
            if (formData.cs_khatian) submitData.append('cs_khatian', formData.cs_khatian);
            if (formData.rs_khatian) submitData.append('rs_khatian', formData.rs_khatian);
            if (formData.bs_khatian) submitData.append('bs_khatian', formData.bs_khatian);
            if (formData.sa_khatian) submitData.append('sa_khatian', formData.sa_khatian);

            // Add land owner data
            if (formData.use_existing_owner && formData.land_owner_id) {
                submitData.append('land_owner_id', formData.land_owner_id);
            } else {
                // New land owner fields
                submitData.append('first_name', formData.first_name);
                submitData.append('last_name', formData.last_name);
                submitData.append('father_name', formData.father_name);
                if (formData.mother_name) submitData.append('mother_name', formData.mother_name);
                if (formData.phone) submitData.append('phone', formData.phone);
                if (formData.nid_number) submitData.append('nid_number', formData.nid_number);
                if (formData.email) submitData.append('email', formData.email);
                submitData.append('address', formData.address);
                if (formData.document_type) submitData.append('document_type', formData.document_type);

                // Add files
                Object.keys(files).forEach(key => {
                    if (files[key]) {
                        submitData.append(key, files[key]);
                    }
                });
            }

            // Add land address
            if (formData.land_address.country_id) {
                submitData.append('land_address[country_id]', formData.land_address.country_id);
            }
            if (formData.land_address.state_id) {
                submitData.append('land_address[state_id]', formData.land_address.state_id);
            }
            if (formData.land_address.city) {
                submitData.append('land_address[city]', formData.land_address.city);
            }
            if (formData.land_address.specific_address) {
                submitData.append('land_address[specific_address]', formData.land_address.specific_address);
            }

            let response;
            if (mode === 'edit') {
                response = await landAcquisitionAPI.update(editingRecord.id, submitData);
            } else {
                response = await landAcquisitionAPI.create(submitData);
            }

            if (response.success) {
                Swal.fire('Success', response.message, 'success');
                onSave();
            } else {
                Swal.fire('Error', response.message || 'Operation failed', 'error');
            }

        } catch (error) {
            console.error('Error submitting form:', error);
            Swal.fire('Error', 'An error occurred while saving', 'error');
        } finally {
            setLoading(false);
        }
    };

    const { t } = useTranslation();
    const isReadOnly = mode === 'view';

    if (!isOpen) return null;

    const title = mode === 'create' ? 'Add New Land Acquisition Record' :
                  mode === 'edit' ? 'Edit Land Acquisition Record' :
                  'View Land Acquisition Record';

    const description = mode === 'create' ? 'Create a new land acquisition record' :
                       mode === 'edit' ? 'Update land acquisition information' :
                       'View land acquisition details';

    const icon = mode === 'create' ? <Plus className="h-6 w-6" /> :
                mode === 'edit' ? <Edit className="h-6 w-6" /> :
                <Eye className="h-6 w-6" />;

    return (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
                {/* Modal Header */}
                <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
                    <div className="flex items-center space-x-3">
                        {icon}
                        <div>
                            <h3 className="text-lg font-semibold">{title}</h3>
                            <p className="text-blue-100 text-sm">{description}</p>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
                    >
                        <X className="h-5 w-5" />
                    </button>
                </div>

                {/* Modal Content */}
                <div className="flex-1 overflow-y-auto p-6">
                    <style jsx>{`
                        .no-focus-outline input {
                            transition: all 0.2s ease-in-out;
                            transform: scale(1);
                        }
                        .no-focus-outline input:focus {
                            outline: none !important;
                            border-color: #d1d5db !important;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                            transform: scale(1.02) !important;
                        }
                        .no-focus-outline input:focus-visible {
                            outline: none !important;
                            border-color: #d1d5db !important;
                            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
                            transform: scale(1.02) !important;
                        }
                        .no-focus-outline input:hover {
                            border-color: #9ca3af !important;
                            transform: scale(1.01) !important;
                        }
                    `}</style>

                    <form onSubmit={handleSubmit} className="space-y-6">
                    {/* Land Acquisition Information */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Land Acquisition Information</h3>
                        <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div className="no-focus-outline">
                                    <Label htmlFor="dag_number">DAG Number *</Label>
                                    <Input
                                        id="dag_number"
                                        value={formData.dag_number}
                                        onChange={(e) => handleInputChange('dag_number', e.target.value)}
                                        required
                                        disabled={isReadOnly}
                                    />
                                </div>
                                <div className="no-focus-outline">
                                    <Label htmlFor="khatian_number">Khatian Number *</Label>
                                    <Input
                                        id="khatian_number"
                                        value={formData.khatian_number}
                                        onChange={(e) => handleInputChange('khatian_number', e.target.value)}
                                        required
                                        disabled={isReadOnly}
                                    />
                                </div>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="no-focus-outline">
                                    <Label htmlFor="mauza">Mauza *</Label>
                                    <Input
                                        id="mauza"
                                        value={formData.mauza}
                                        onChange={(e) => handleInputChange('mauza', e.target.value)}
                                        required
                                        disabled={isReadOnly}
                                    />
                                </div>
                                <div className="no-focus-outline">
                                    <Label htmlFor="land_size">Land Size (Decimal) *</Label>
                                    <Input
                                        id="land_size"
                                        type="number"
                                        step="0.01"
                                        value={formData.land_size}
                                        onChange={(e) => handleInputChange('land_size', e.target.value)}
                                        required
                                        disabled={isReadOnly}
                                    />
                                </div>
                                <div className="no-focus-outline">
                                    <Label htmlFor="acquisition_price">Acquisition Price *</Label>
                                    <Input
                                        id="acquisition_price"
                                        type="number"
                                        step="0.01"
                                        value={formData.acquisition_price}
                                        onChange={(e) => handleInputChange('acquisition_price', e.target.value)}
                                        required
                                        disabled={isReadOnly}
                                    />
                                </div>
                            </div>
                        </div>

                        {/* Additional Khatian Fields */}
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div className="no-focus-outline">
                                <Label htmlFor="cs_khatian">CS Khatian</Label>
                                <Input
                                    id="cs_khatian"
                                    value={formData.cs_khatian}
                                    onChange={(e) => handleInputChange('cs_khatian', e.target.value)}
                                    disabled={isReadOnly}
                                />
                            </div>
                            <div className="no-focus-outline">
                                <Label htmlFor="rs_khatian">RS Khatian</Label>
                                <Input
                                    id="rs_khatian"
                                    value={formData.rs_khatian}
                                    onChange={(e) => handleInputChange('rs_khatian', e.target.value)}
                                    disabled={isReadOnly}
                                />
                            </div>
                            <div className="no-focus-outline">
                                <Label htmlFor="bs_khatian">BS Khatian</Label>
                                <Input
                                    id="bs_khatian"
                                    value={formData.bs_khatian}
                                    onChange={(e) => handleInputChange('bs_khatian', e.target.value)}
                                    disabled={isReadOnly}
                                />
                            </div>
                            <div className="no-focus-outline">
                                <Label htmlFor="sa_khatian">SA Khatian</Label>
                                <Input
                                    id="sa_khatian"
                                    value={formData.sa_khatian}
                                    onChange={(e) => handleInputChange('sa_khatian', e.target.value)}
                                    disabled={isReadOnly}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Land Owner Selection */}
                    {!isReadOnly && (
                        <div className="space-y-4">
                            <h3 className="text-lg font-semibold">Land Owner</h3>
                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="use_existing_owner"
                                    checked={formData.use_existing_owner}
                                    onCheckedChange={(checked) => handleInputChange('use_existing_owner', checked)}
                                />
                                <Label htmlFor="use_existing_owner">Use existing land owner</Label>
                            </div>

                            {formData.use_existing_owner ? (
                                <div>
                                    <Label htmlFor="land_owner_id">Select Land Owner *</Label>
                                    <Select
                                        value={formData.land_owner_id}
                                        onValueChange={(value) => handleInputChange('land_owner_id', value)}
                                    >
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select a land owner" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {landOwners.map((owner) => (
                                                <SelectItem key={owner.id} value={owner.id.toString()}>
                                                    {owner.first_name} {owner.last_name} - {owner.father_name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>
                                </div>
                            ) : (
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="no-focus-outline">
                                        <Label htmlFor="first_name">First Name *</Label>
                                        <Input
                                            id="first_name"
                                            value={formData.first_name}
                                            onChange={(e) => handleInputChange('first_name', e.target.value)}
                                            required={!formData.use_existing_owner}
                                        />
                                    </div>
                                    <div className="no-focus-outline">
                                        <Label htmlFor="last_name">Last Name *</Label>
                                        <Input
                                            id="last_name"
                                            value={formData.last_name}
                                            onChange={(e) => handleInputChange('last_name', e.target.value)}
                                            required={!formData.use_existing_owner}
                                        />
                                    </div>
                                    <div className="no-focus-outline">
                                        <Label htmlFor="father_name">Father's Name *</Label>
                                        <Input
                                            id="father_name"
                                            value={formData.father_name}
                                            onChange={(e) => handleInputChange('father_name', e.target.value)}
                                            required={!formData.use_existing_owner}
                                        />
                                    </div>
                                    <div className="no-focus-outline">
                                        <Label htmlFor="mother_name">Mother's Name</Label>
                                        <Input
                                            id="mother_name"
                                            value={formData.mother_name}
                                            onChange={(e) => handleInputChange('mother_name', e.target.value)}
                                        />
                                    </div>
                                    <div className="no-focus-outline">
                                        <Label htmlFor="phone">Phone</Label>
                                        <Input
                                            id="phone"
                                            value={formData.phone}
                                            onChange={(e) => handleInputChange('phone', e.target.value)}
                                        />
                                    </div>
                                    <div className="no-focus-outline">
                                        <Label htmlFor="nid_number">NID Number</Label>
                                        <Input
                                            id="nid_number"
                                            value={formData.nid_number}
                                            onChange={(e) => handleInputChange('nid_number', e.target.value)}
                                        />
                                    </div>
                                    <div className="no-focus-outline">
                                        <Label htmlFor="email">Email</Label>
                                        <Input
                                            id="email"
                                            type="email"
                                            value={formData.email}
                                            onChange={(e) => handleInputChange('email', e.target.value)}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="document_type">Document Type</Label>
                                        <Select
                                            value={formData.document_type}
                                            onValueChange={(value) => handleInputChange('document_type', value)}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Select document type" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="nid">NID</SelectItem>
                                                <SelectItem value="passport">Passport</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div className="md:col-span-2">
                                        <Label htmlFor="address">Address *</Label>
                                        <Textarea
                                            id="address"
                                            value={formData.address}
                                            onChange={(e) => handleInputChange('address', e.target.value)}
                                            required={!formData.use_existing_owner}
                                        />
                                    </div>

                                    {/* File uploads */}
                                    <div>
                                        <Label htmlFor="photo">Photo</Label>
                                        <Input
                                            id="photo"
                                            type="file"
                                            accept="image/*"
                                            onChange={(e) => handleFileChange('photo', e.target.files[0])}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="nid_front">NID Front</Label>
                                        <Input
                                            id="nid_front"
                                            type="file"
                                            accept="image/*"
                                            onChange={(e) => handleFileChange('nid_front', e.target.files[0])}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="nid_back">NID Back</Label>
                                        <Input
                                            id="nid_back"
                                            type="file"
                                            accept="image/*"
                                            onChange={(e) => handleFileChange('nid_back', e.target.files[0])}
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="passport_photo">Passport Photo</Label>
                                        <Input
                                            id="passport_photo"
                                            type="file"
                                            accept="image/*"
                                            onChange={(e) => handleFileChange('passport_photo', e.target.files[0])}
                                        />
                                    </div>
                                </div>
                            )}
                        </div>
                    )}

                    {/* Land Address */}
                    <div className="space-y-4">
                        <h3 className="text-lg font-semibold">Land Address</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <Label htmlFor="country_id">Country</Label>
                                <Select
                                    value={formData.land_address.country_id}
                                    onValueChange={(value) => handleInputChange('land_address.country_id', value)}
                                    disabled={isReadOnly}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select country" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {countries.map((country) => (
                                            <SelectItem key={country.id} value={country.id.toString()}>
                                                {country.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label htmlFor="state_id">State</Label>
                                <Select
                                    value={formData.land_address.state_id}
                                    onValueChange={(value) => handleInputChange('land_address.state_id', value)}
                                    disabled={isReadOnly}
                                >
                                    <SelectTrigger>
                                        <SelectValue placeholder="Select state" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        {states.map((state) => (
                                            <SelectItem key={state.id} value={state.id.toString()}>
                                                {state.name}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                            </div>
                            <div>
                                <Label htmlFor="city">City</Label>
                                <Input
                                    id="city"
                                    value={formData.land_address.city}
                                    onChange={(e) => handleInputChange('land_address.city', e.target.value)}
                                    disabled={isReadOnly}
                                />
                            </div>
                            <div>
                                <Label htmlFor="specific_address">Specific Address</Label>
                                <Textarea
                                    id="specific_address"
                                    value={formData.land_address.specific_address}
                                    onChange={(e) => handleInputChange('land_address.specific_address', e.target.value)}
                                    disabled={isReadOnly}
                                />
                            </div>
                        </div>
                    </div>

                    {/* Form Actions */}
                    <div className="flex justify-end gap-2 pt-4 border-t">
                        <Button type="button" variant="outline" onClick={onClose} disabled={loading}>
                            {isReadOnly ? 'Close' : 'Cancel'}
                        </Button>
                        {!isReadOnly && (
                            <Button type="submit" disabled={loading} className="bg-blue-600 hover:bg-blue-700 transition-colors">
                                {loading ? 'Saving...' : (mode === 'edit' ? 'Update' : 'Create')}
                            </Button>
                        )}
                    </div>
                </form>
                </div>
            </div>
        </div>
    );
};

export default LandAcquisitionModal;
