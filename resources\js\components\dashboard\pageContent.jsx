import React, { useState, useEffect, useCallback, Fragment, memo } from "react";
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Trash, GripVertical } from "lucide-react";
import { useDrag, useDrop } from "react-dnd";
import SliderItem from "./widgets/SliderItem";
import SearchItem from "./widgets/SearchItem";
import SubNavigation from "./widgets/SubNavigation";
import AboutUs from "./widgets/AboutUs";
import PropertyType from "./widgets/PropertyType";
import PropertyByCity from "./widgets/PropertyByCity";
import WhyChooseUs from "./widgets/WhyChooseUs";
import FeaturedProperty from "./widgets/FeaturedProperty";
import HowItWorks from "./widgets/HowItWorks";
import RecentProperty from "./widgets/RecentProperty";
import Advertisement from "./widgets/Advertisement";
import Testimonials from "./widgets/Testimonials";
import TrustedPartners from "./widgets/TrustedPartners";
import Blog from "./widgets/Blog";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";

const getFormSections = (widgetId) => {
  if (widgetId === 1) return ["slider"];
  if (widgetId === 2) return ["search"];
  if (widgetId === 3) return ["SubNavigation"];
  if (widgetId === 4) return ["AboutUs"];
  if (widgetId === 5) return ["PropertyType"];
  if (widgetId === 6) return ["Property By City"];
  if (widgetId === 7) return ["Why Choose Us"];
  if (widgetId === 8) return ["Featured Property"];
  if (widgetId === 9) return ["How it Works"];
  if (widgetId === 10) return ["Recent Property"];
  if (widgetId === 11) return ["Advertisement"];
  if (widgetId === 12) return ["Testimonials"];
  if (widgetId === 13) return ["Trusted Partners"];
  if (widgetId === 14) return ["Blog"];
  return [];
};

const DraggableWidget = memo(({ widget, index, moveWidget, openItem, setOpenItem, removeWidget, getFormSections, handleConfigUpdate }) => {
  const [{ isDragging }, drag] = useDrag({
    type: 'widget',
    item: { index, id: widget.id },
    collect: (monitor) => ({
      isDragging: monitor.isDragging(),
    }),
  });

  const [{ isOver, canDrop }, drop] = useDrop({
    accept: 'widget',
    hover: (draggedItem, monitor) => {
      if (!monitor.isOver({ shallow: true })) return;
      
      const dragIndex = draggedItem.index;
      const hoverIndex = index;
      
      if (dragIndex === hoverIndex) return;
      
      // Get the bounding rectangle of the hovered item
      const hoverBoundingRect = monitor.getClientRect();
      if (!hoverBoundingRect) return;
      
      // Get vertical middle
      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;
      
      // Determine mouse position
      const clientOffset = monitor.getClientOffset();
      if (!clientOffset) return;
      
      // Get pixels to the top
      const hoverClientY = clientOffset.y - hoverBoundingRect.top;
      
      // Only perform the move when the mouse has crossed half of the items height
      // When dragging downwards, only move when the cursor is below 50%
      // When dragging upwards, only move when the cursor is above 50%
      if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
        return;
      }
      if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
        return;
      }
      
      // Time to actually perform the action
      moveWidget(dragIndex, hoverIndex);
      
      // Note: we're mutating the monitor item here!
      // Generally it's better to avoid mutations,
      // but it's good here for the sake of performance
      // to avoid expensive index searches.
      draggedItem.index = hoverIndex;
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop(),
    }),
  });

  return (
    <div ref={(node) => drag(drop(node))} className="relative">
      {/* Drop zone indicator */}
      {isOver && canDrop && (
        <div className="absolute inset-0 border-2 border-dashed border-blue-500 bg-blue-50/20 rounded-md z-10 pointer-events-none">
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 text-blue-600 font-medium text-sm">
            Drop here
          </div>
        </div>
      )}
      
      <div 
        style={{ 
          opacity: isDragging ? 0.4 : 1,
          transform: isDragging ? 'rotate(5deg) scale(1.05)' : 'none',
          transition: isDragging ? 'none' : 'all 0.2s ease-in-out'
        }}
      >
        <AccordionItem value={`item-${index}`} key={`widget-${widget.id}-${index}`}>
          <Card className="mb-[15px] min-h-[80px] transition-shadow duration-200 hover:shadow-md">
            <AccordionTrigger className="flex justify-between [&>svg]:hidden py-6 px-4">
              <div className="flex items-center">
                <GripVertical className="h-5 w-5 mr-3 text-gray-400 cursor-grab" />
                <h3 className="!font-extralight text-base">{widget.name}</h3>
              </div>
              <div
                className="inline-flex items-center justify-center pr-[15px]"
                onClick={(e) => { e.stopPropagation(); removeWidget(widget.id); }}
              >
                <Trash className="h-5 w-5" />
              </div>
            </AccordionTrigger>
            <AccordionContent className="px-5" onClick={(e) => e.stopPropagation()}>
              <div className="space-y-4">
              {getFormSections(widget.id).includes("slider") && (
                <div className="border p-4 rounded-md space-y-4">
                  {(widget.config?.sliders || []).map((slider, sliderIndex) => (
                    <SliderItem
                      key={sliderIndex}
                      slider={slider}
                      sliderIndex={sliderIndex}
                      updateSlider={(i, field, value) => {
                        const newSliders = [...(widget.config?.sliders || [])];
                        newSliders[i] = { ...newSliders[i], [field]: value };
                        handleConfigUpdate(widget.id, "sliders", newSliders);
                      }}
                      removeSlider={(i) => {
                        const newSliders = [...(widget.config?.sliders || [])];
                        newSliders.splice(i, 1);
                        handleConfigUpdate(widget.id, "sliders", newSliders);
                      }}
                    />
                  ))}
                </div>
              )}

              {getFormSections(widget.id).includes("search") && (
                <SearchItem widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("SubNavigation") && (
                <SubNavigation widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("AboutUs") && (
                <AboutUs widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("PropertyType") && (
                <PropertyType widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("Property By City") && (
                <PropertyByCity widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("Why Choose Us") && (
                <WhyChooseUs widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("Featured Property") && (
                <FeaturedProperty widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("How it Works") && (
                <HowItWorks widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("Recent Property") && (
                <RecentProperty widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("Advertisement") && (
                <Advertisement widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("Testimonials") && (
                <Testimonials widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("Trusted Partners") && (
                <TrustedPartners widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}

              {getFormSections(widget.id).includes("Blog") && (
                <Blog widget={widget} handleConfigUpdate={handleConfigUpdate} />
              )}
              </div>
            </AccordionContent>
          </Card>
        </AccordionItem>
      </div>
    </div>
  );
});

// Drop zone indicator component
const DropZone = ({ index, moveWidget, droppedWidgets, onNewWidgetDrop }) => {
  const [{ isOver, canDrop }, drop] = useDrop({
    accept: ['widget', 'WIDGET'], // Accept both internal reordering and new widgets from sidebar
    drop: (draggedItem) => {
      console.log('DropZone drop:', draggedItem, 'at index:', index); // Debug log
      
      // Check if this is a new widget from sidebar
      // New widgets have id but no index property, or index is undefined
      if (draggedItem.id && (draggedItem.index === undefined || draggedItem.index === null)) {
        // This is a new widget from sidebar
        const dropIndex = index === -1 ? 0 : index + 1;
        console.log('Adding new widget at index:', dropIndex); // Debug log
        onNewWidgetDrop(draggedItem, dropIndex);
      } else if (typeof draggedItem.index === 'number') {
        // This is reordering existing widgets (has numeric index)
        const dragIndex = draggedItem.index;
        let dropIndex = index === -1 ? 0 : index + 1;
        
        // Adjust drop index if dragging from above
        if (dragIndex < dropIndex) {
          dropIndex -= 1;
        }
        
        console.log('Reordering widget from', dragIndex, 'to', dropIndex); // Debug log
        if (dragIndex !== dropIndex) {
          moveWidget(dragIndex, dropIndex);
        }
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  });

  if (!canDrop && !isOver) return <div className="h-2" />; // Always show some spacing

  return (
    <div 
      ref={drop}
      className={`mx-4 my-2 rounded-full transition-all duration-200 ${
        isOver 
          ? 'bg-blue-500 h-10 shadow-lg flex items-center justify-center border-2 border-blue-400' 
          : canDrop 
            ? 'bg-blue-100 border-2 border-dashed border-blue-300 h-6 flex items-center justify-center'
            : 'bg-transparent hover:bg-blue-50 h-3'
      }`}
    >
      {isOver && (
        <div className="text-sm font-semibold text-white px-4 py-2 bg-blue-600 rounded-full shadow-md">
          Drop widget here
        </div>
      )}
      {canDrop && !isOver && (
        <div className="text-xs text-blue-600 font-medium">
          Drop zone
        </div>
      )}
    </div>
  );
};

const PageContent = ({ droppedWidgets, updateWidgetConfig, removeWidget, isOver, drop, onWidgetUpdate, reorderWidget, onNewWidgetDrop }) => {
  const [openItem, setOpenItem] = useState(null);

  useEffect(() => {
    // Only auto-open the last widget if no item is currently open
    if (droppedWidgets.length > 0 && !openItem) {
      const lastIndex = droppedWidgets.length - 1;
      setOpenItem(`item-${lastIndex}`);
    }
  }, [droppedWidgets.length]); // Only depend on length, not the entire array

  const handleConfigUpdate = useCallback((widgetId, field, value) => {
    updateWidgetConfig(widgetId, field, value);
    if (onWidgetUpdate) onWidgetUpdate(widgetId, field, value);
    
    // Keep accordion open after updates
    // The accordion should remain in its current state without resetting
  }, [updateWidgetConfig, onWidgetUpdate]);

  const moveWidget = useCallback((dragIndex, dropIndex) => {
    if (reorderWidget) {
      reorderWidget(dragIndex, dropIndex);
    }
  }, [reorderWidget]);

  return (
    <div className="md:col-span-8">
      <div ref={drop} className={`p-2 border rounded ${isOver ? "border-blue-500 bg-blue-50" : "border-gray-200"}`}>
        {droppedWidgets.length === 0 && <p className="text-gray-500">No widgets added yet</p>}

        <Accordion type="single" collapsible value={openItem} onValueChange={setOpenItem}>
          {droppedWidgets.length > 0 && (
            <>
              {/* Drop zone at the beginning */}
              <DropZone index={-1} moveWidget={moveWidget} droppedWidgets={droppedWidgets} onNewWidgetDrop={onNewWidgetDrop} />
              
              {droppedWidgets.map((widget, index) => (
                <Fragment key={`fragment-${widget.id}`}>
                  <DraggableWidget
                    key={`widget-${widget.id}`}
                    widget={widget}
                    index={index}
                    moveWidget={moveWidget}
                    openItem={openItem}
                    setOpenItem={setOpenItem}
                    removeWidget={removeWidget}
                    getFormSections={getFormSections}
                    handleConfigUpdate={handleConfigUpdate}
                  />
                  {/* Drop zone after each widget */}
                  <DropZone index={index} moveWidget={moveWidget} droppedWidgets={droppedWidgets} onNewWidgetDrop={onNewWidgetDrop} />
                </Fragment>
              ))}
            </>
          )}
          
          {/* Show drop zone when no widgets exist */}
          {droppedWidgets.length === 0 && (
            <DropZone index={-1} moveWidget={moveWidget} droppedWidgets={droppedWidgets} onNewWidgetDrop={onNewWidgetDrop} />
          )}
        </Accordion>
      </div>
    </div>
  );
};

export default PageContent;
