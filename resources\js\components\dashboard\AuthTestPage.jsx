
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { useAuth } from '../../contexts/AuthContext';
import { authAPI } from '../../services/authAPI';
import { 
  Shield, 
  User, 
  Mail, 
  Calendar, 
  CheckCircle, 
  XCircle,
  RefreshCw,
  LogOut,
  Key
} from 'lucide-react';

const AuthTestPage = () => {
  const { user, role, permissions, accessibleModules, logout, isAuthenticated } = useAuth();
  const [testResults, setTestResults] = useState({});
  const [loading, setLoading] = useState(false);

  const runAPITest = async (testName, apiCall) => {
    setLoading(true);
    try {
      const result = await apiCall();
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: true, data: result }
      }));
    } catch (error) {
      setTestResults(prev => ({
        ...prev,
        [testName]: { success: false, error: error.message }
      }));
    }
    setLoading(false);
  };

  const testAPIs = [
    {
      name: 'Profile',
      test: () => runAPITest('profile', () => authAPI.getProfile())
    },
    {
      name: 'Permissions',
      test: () => runAPITest('permissions', () => authAPI.getPermissions())
    },
    {
      name: 'Refresh Token',
      test: () => runAPITest('refreshToken', () => authAPI.refreshToken())
    }
  ];

  if (!isAuthenticated) {
    return (
      <div className="space-y-6">
        <Alert>
          <XCircle className="h-4 w-4" />
          <AlertDescription>
            You are not authenticated. Please log in to access this page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Authentication Test</h1>
          <p className="text-muted-foreground">
            Test the authentication system and view user permissions
          </p>
        </div>
        <Button variant="outline" onClick={logout}>
          <LogOut className="mr-2 h-4 w-4" />
          Logout
        </Button>
      </div>

      {/* Authentication Status */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckCircle className="h-5 w-5 text-green-600" />
            Authentication Status
          </CardTitle>
          <CardDescription>
            Current authentication state and user information
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-blue-600" />
                <span className="font-medium">Name:</span>
                <span>{user?.name || 'Not available'}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Mail className="h-4 w-4 text-blue-600" />
                <span className="font-medium">Email:</span>
                <span>{user?.email || 'Not available'}</span>
              </div>
              
              <div className="flex items-center gap-2">
                <Shield className="h-4 w-4 text-blue-600" />
                <span className="font-medium">Role:</span>
                {role ? (
                  <Badge variant="secondary">{role.name}</Badge>
                ) : (
                  <span>No role assigned</span>
                )}
              </div>
              
              <div className="flex items-center gap-2">
                <Key className="h-4 w-4 text-blue-600" />
                <span className="font-medium">Token:</span>
                <Badge variant={authAPI.getToken() ? "default" : "destructive"}>
                  {authAPI.getToken() ? "Valid" : "Missing"}
                </Badge>
              </div>
            </div>
            
            <div className="space-y-3">
              <div>
                <span className="font-medium">Accessible Modules:</span>
                <div className="flex flex-wrap gap-1 mt-1">
                  {accessibleModules.map(module => (
                    <Badge key={module} variant="outline" className="text-xs">
                      {module}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Permissions Matrix */}
      <Card>
        <CardHeader>
          <CardTitle>Permissions Matrix</CardTitle>
          <CardDescription>
            Your permissions for each accessible module
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(permissions).map(([module, perms]) => (
              <div key={module} className="border rounded-lg p-3">
                <div className="flex items-center justify-between mb-2">
                  <h4 className="font-medium capitalize">{module.replace('-', ' ')}</h4>
                  <Badge variant="secondary">{perms.length} permissions</Badge>
                </div>
                <div className="flex flex-wrap gap-1">
                  {perms.map(permission => (
                    <Badge key={permission} variant="outline" className="text-xs">
                      {permission}
                    </Badge>
                  ))}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* API Tests */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <RefreshCw className="h-5 w-5" />
            API Tests
          </CardTitle>
          <CardDescription>
            Test various authentication API endpoints
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
              {testAPIs.map(({ name, test }) => (
                <Button
                  key={name}
                  variant="outline"
                  onClick={test}
                  disabled={loading}
                  className="h-auto p-3"
                >
                  <div className="text-center">
                    <div className="font-medium">Test {name}</div>
                    {testResults[name.toLowerCase()] && (
                      <div className="text-xs mt-1">
                        {testResults[name.toLowerCase()].success ? (
                          <Badge variant="default" className="text-xs">Success</Badge>
                        ) : (
                          <Badge variant="destructive" className="text-xs">Failed</Badge>
                        )}
                      </div>
                    )}
                  </div>
                </Button>
              ))}
            </div>

            {/* Test Results */}
            {Object.keys(testResults).length > 0 && (
              <div className="mt-6 space-y-2">
                <h4 className="font-medium">Test Results:</h4>
                {Object.entries(testResults).map(([test, result]) => (
                  <Alert key={test} variant={result.success ? "default" : "destructive"}>
                    <div className="flex items-center gap-2">
                      {result.success ? (
                        <CheckCircle className="h-4 w-4" />
                      ) : (
                        <XCircle className="h-4 w-4" />
                      )}
                      <AlertDescription>
                        <strong>{test}:</strong> {result.success ? 'Success' : result.error}
                      </AlertDescription>
                    </div>
                  </Alert>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AuthTestPage;
