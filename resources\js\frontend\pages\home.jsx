import React from "react";

import Slider from '../components/pages/Home/slider';
import HomeAbout from '../components/pages/Home/home-about.jsx';
import SearchDreamProperty  from "../components/pages/Home/searchDreamProperty.jsx";
import PropertyByCities from "../components/pages/Home/propertyByCities";
import WhyChooseUs from "../components/pages/Home/whyChooseUs";
import FeaturedProperties from "../components/pages/Home/featuredProperties"; 
import HowItWorks from "../components/pages/Home/howItWorks";
import RecentProperties from "../components/pages/Home/recentProperties";
import Advertise from "../components/pages/Home/advertise";
import Testimonial from "../components/pages/Home/testimonial";
import Partners from "../components/pages/Home/partners";
import Blog from "../components/pages/Home/blog";

const Home = () => {
  const [showSlider, setShowSlider] = React.useState(false);
  const [showHomeAbout, setShowHomeAbout] = React.useState(false);
  const [showFeaturedProperties, setShowFeaturedProperties] = React.useState(false);
  const [showHowItWorks, setShowHowItWorks] = React.useState(false);
  const [showPropertyByCities, setShowPropertyByCities] = React.useState(false);
  const [showWhyChooseUs, setShowWhyChooseUs] = React.useState(false);
  const [showRecentProperties, setShowRecentProperties] = React.useState(false);
  const [showAdvertise, setShowAdvertise] = React.useState(false);
  const [showTestimonial, setShowTestimonial] = React.useState(false);
  const [showPartners, setShowPartners] = React.useState(false);
  const [showBlog, setShowBlog] = React.useState(false);

  React.useEffect(() => {
    fetch("/api/frontend/page-contents?page_id=1&paginate=false")
      .then((res) => res.json())
      .then((data) => {
        if (data.success && Array.isArray(data.data)) {
          const hasWidget1 = data.data.some(item => item.widget_id === 1);
          const hasWidget4 = data.data.some(item => item.widget_id === 4);
          const hasWidget6 = data.data.some(item => item.widget_id === 6);
          const hasWidget7 = data.data.some(item => item.widget_id === 7);
          const hasWidget8 = data.data.some(item => item.widget_id === 8);
          const hasWidget9 = data.data.some(item => item.widget_id === 9);
          const hasWidget10 = data.data.some(item => item.widget_id === 10);
          const hasWidget11 = data.data.some(item => item.widget_id === 11);
          const hasWidget12 = data.data.some(item => item.widget_id === 12);
          const hasWidget13 = data.data.some(item => item.widget_id === 13);
          const hasWidget14 = data.data.some(item => item.widget_id === 14);
          setShowSlider(hasWidget1);
          setShowHomeAbout(hasWidget4);
          setShowPropertyByCities(hasWidget6);
          setShowWhyChooseUs(hasWidget7);
          setShowFeaturedProperties(hasWidget8);
          setShowHowItWorks(hasWidget9);
          setShowRecentProperties(hasWidget10);
          setShowAdvertise(hasWidget11);
          setShowTestimonial(hasWidget12);
          setShowPartners(hasWidget13);
          setShowBlog(hasWidget14);
        }
      });
  }, []);

  return (
    <div>
    {showSlider && <Slider/>}
    {showHomeAbout && <HomeAbout/>}
    <SearchDreamProperty/>
    {showPropertyByCities && <PropertyByCities/>}
    {showWhyChooseUs && <WhyChooseUs/>}
    {showFeaturedProperties && <FeaturedProperties/>}
    {showHowItWorks && <HowItWorks/>}
    {showRecentProperties && <RecentProperties/>}
    {showAdvertise && <Advertise/>}
    {showTestimonial && <Testimonial/>}
    {showPartners && <Partners/>}
    {showBlog && <Blog/>}
  

    </div>
  );
};

export default Home;