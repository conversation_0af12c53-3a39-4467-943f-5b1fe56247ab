import axios from 'axios';
import { API_BASE_URL, getValidatedApiUrl, getEnvironmentInfo } from '../config/api.js';



// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Accept': 'application/json',
  },
  timeout: 30000, // 30 second timeout
});

// Add request interceptor to include auth token 
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Store for dynamic API URL
let currentApiBaseUrl = API_BASE_URL;

// Initialize and test API connectivity
const initializeApi = async () => {
  try {
    
    const validatedUrl = await getValidatedApiUrl();
    
    if (validatedUrl !== currentApiBaseUrl) {
     
      currentApiBaseUrl = validatedUrl;
      api.defaults.baseURL = validatedUrl;
    }
    
   
  } catch (error) {
   
  }
};

// Initialize API on module load
initializeApi();

// Export function to manually test/reinitialize API
export const reinitializeApi = initializeApi;

// Export function to get current API info
export const getApiInfo = () => ({
  currentUrl: currentApiBaseUrl,
  originalUrl: API_BASE_URL,
  environmentInfo: getEnvironmentInfo()
});

// Add request interceptor for authentication and dynamic URL handling
api.interceptors.request.use(
  async (config) => {
    // Update base URL if it has changed
    if (currentApiBaseUrl !== API_BASE_URL) {
      config.baseURL = currentApiBaseUrl;
   
    }
    
    // Add auth token if available (TEMPORARILY DISABLED FOR DEBUGGING)
    // const token = localStorage.getItem('auth_token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    
    // Only set Content-Type to application/json for non-FormData requests
    if (!(config.data instanceof FormData)) {
      config.headers['Content-Type'] = 'application/json';
    }
    
   
    
    return config;
  },
  (error) => {
  
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling and automatic URL switching
api.interceptors.response.use(
  (response) => {
    // Log successful response
   
    return response;
  },
  async (error) => {
   
    
    // Handle authentication errors
    if (error.response?.status === 401) {
      console.warn('🔒 Authentication failed - clearing token');
      localStorage.removeItem('auth_token');
      window.location.reload();
      return Promise.reject(error);
    }
    
    // Handle connection errors - try to find a working API URL
    if (!error.response && (error.code === 'ECONNABORTED' || error.message.includes('Network Error'))) {
   
      
      try {
        const newApiUrl = await getValidatedApiUrl();
        if (newApiUrl !== currentApiBaseUrl) {
       
          currentApiBaseUrl = newApiUrl;
          api.defaults.baseURL = newApiUrl;
          
          // Retry the original request with new URL
          const retryConfig = { ...error.config };
          retryConfig.baseURL = newApiUrl;
         
          return api.request(retryConfig);
        }
      } catch (urlError) {
        console.error('❌ Failed to find working API URL:', urlError);
      }
    }
    
    return Promise.reject(error);
  }
);

// Land Owner API functions

// Get all land owners with pagination and search
export const getAll = async (params = {}) => {
  try {
    const response = await api.get('/land-owners', { params });
    return response.data;
  } catch (error) {
    console.error('Error fetching land owners:', error);
    throw error;
  }
};

// Get single land owner by ID
export const getById = async (id) => {
  try {
    const response = await api.get(`/land-owners/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching land owner ${id}:`, error);
    throw error;
  }
};

// Create new land owner
export const create = async (data) => {
  try {
  

    // Create FormData for file uploads
    const formData = new FormData();
    
    // Add all non-empty fields to FormData
    for (const key in data) {
      const value = data[key];
      
      // Skip null, undefined, or empty string values
      if (value !== null && value !== undefined && value !== '') {
        if (value instanceof File) {
          // For file uploads, add the file directly
          formData.append(key, value);
         
        } else {
          // For regular fields, add as string
          formData.append(key, String(value));
        
        }
      }
    }

  
   
   

    // Don't set Content-Type header - let browser set it with proper boundary
    const response = await api.post('/land-owners', formData);
    
  
    return response.data;
    
  } catch (error) {
   
    
    // Log detailed error information
    if (error.response) {
     
      
      // Return the error response data for better error handling
      if (error.response.data) {
        return error.response.data;
      }
    } 
    
    // Re-throw for handling in component
    throw error;
  }
};

// Update existing land owner
export const update = async (id, data) => {
  try {
    // Check if we have any file data that needs FormData
    const hasFiles = Object.keys(data).some(key => {
      const value = data[key];
      return value instanceof File;
    });

   
    let requestData;
    let headers = {};

    if (hasFiles) {
     
      // Use FormData for file uploads
      requestData = new FormData();

      // Add _method for Laravel PUT request simulation
      requestData.append('_method', 'PUT');

      // Append all form fields to FormData
      Object.keys(data).forEach(key => {
        const value = data[key];

        // Skip null, undefined, or empty string values
        if (value === null || value === undefined || value === '') {
        
          return;
        }

        // Handle file fields vs regular fields
        if (value instanceof File) {
       
          requestData.append(key, value);
        } else {
         
          requestData.append(key, value);
        }
      });

      // Log FormData contents for debugging
     
    

      // Don't set Content-Type header - let browser set it with proper boundary
      const response = await api.post(`/land-owners/${id}`, requestData);
     
      return response.data;
    } else {
    
      // Use regular JSON for non-file data
      requestData = { ...data };
      headers['Content-Type'] = 'application/json';

      // Remove file fields if they're existing URLs to preserve them
      ['photo', 'nid_front', 'nid_back', 'passport_photo'].forEach(field => {
        if (requestData[field] && typeof requestData[field] === 'string' && requestData[field].startsWith('/landowners/')) {
        
          delete requestData[field];
        } else if (requestData[field] === '') {
         
          delete requestData[field];
        }
      });


      const response = await api.put(`/land-owners/${id}`, requestData, { headers });
     
      return response.data;
    }
  } catch (error) {
  
    
    // Log detailed error information
    if (error.response) {
     

      // Return the error response data for better error handling
      if (error.response.data) {
        return error.response.data;
      }
    } 
    // Re-throw for handling in component
    throw error;
  }
};

// Delete land owner
export const delete_ = async (id) => {
  try {
    const response = await api.delete(`/land-owners/${id}`);
    return response.data;
  } catch (error) {
 
    throw error;
  }
};

// Get land owners for dropdown (if this endpoint exists)
export const getForDropdown = async () => {
  try {
    const response = await api.get('/land-owners/dropdown');
    return response.data;
  } catch (error) {
   
    throw error;
  }
};

// Get statistics (if this endpoint exists)
export const getStatistics = async () => {
  try {
    const response = await api.get('/land-owners/statistics');
    return response.data;
  } catch (error) {
   
    throw error;
  }
};

// Audit Log API functions



// Activate a land owner
export const activate = async (id) => {
  try {
    const response = await api.patch(`/land-owners/${id}/activate`);
    return response.data;
  } catch (error) {
    console.error('Error activating land owner:', error);
    throw error;
  }
};

// Deactivate a land owner
export const deactivate = async (id) => {
  try {
    const response = await api.patch(`/land-owners/${id}/deactivate`);
    return response.data;
  } catch (error) {
    console.error('Error deactivating land owner:', error);
    throw error;
  }
};


