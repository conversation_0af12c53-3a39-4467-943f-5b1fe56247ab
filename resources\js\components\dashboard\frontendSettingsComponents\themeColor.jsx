import React, { useState } from "react";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { TabsContent } from "@/components/ui/tabs";

const ThemeColor = ({ formData, handleInputChange, handleSubmit, loading }) => {
  // Remove local loading state since it's now managed in parent
  // const [loading, setLoading] = useState(false);

  return (
    <TabsContent value="theme" className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <Label htmlFor="primaryColor">Primary Color</Label>
          <Input
            id="primaryColor"
            type="color"
            value={formData.primaryColor || "#2563eb"}
            onChange={(e) => handleInputChange("primaryColor", e.target.value)}
            className="w-20 h-10"
          />
        </div>

        <div>
          <Label htmlFor="secondaryColor">Secondary Color</Label>
          <Input
            id="secondaryColor"
            type="color"
            value={formData.secondaryColor || "#2563eb"}
            onChange={(e) => handleInputChange("secondaryColor", e.target.value)}
            className="w-20 h-10"
          />
        </div>
      </div>

      <div className="flex justify-end">
        <button
          onClick={handleSubmit}
          className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
          disabled={loading}
        >
          {loading ? "Saving..." : "Save Settings"}
        </button>
      </div>
    </TabsContent>
  );
};

export default ThemeColor;