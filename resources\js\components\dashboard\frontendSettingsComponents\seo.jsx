import React, { useState } from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import Select from 'react-select';
import RichTextEditor from "../../ui/RichTextEditor";
import { TabsContent } from "@/components/ui/tabs";

const Seo = ({ 
  formData, 
  handleInputChange, 
  handleSubmit, 
  loading,
  handleMetaImageFileChange,   
  metaImagePreview             
}) => {
  return (
    <TabsContent value="seo" className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <Label htmlFor="metaImage">Meta Image</Label>
          <div className="space-y-2">
            <Input
              id="metaImage"
              type="file"
              accept="image/*"
              onChange={handleMetaImageFileChange} // ✅ parent handler
            />

            {metaImagePreview && (
              <div className="mt-4">
                <Label className="text-sm font-medium">Preview:</Label>
                <div className="mt-2 border rounded-lg p-2 bg-gray-50">
                  <img
                    src={metaImagePreview}
                    alt="Meta image preview"
                    className="max-w-full max-h-32 mx-auto rounded"
                  />
                </div>
              </div>
            )}
          </div>
        </div>
        
        {/* Title + Keywords */}
        <div>
          <Label htmlFor="MetaTitle">Meta Title</Label>
          <Input
            id="MetaTitle"
            value={formData.MetaTitle || ""}
            onChange={(e) => handleInputChange("MetaTitle", e.target.value)}
            placeholder="Your Meta Title"
          />
        </div>
        <div>
          <Label htmlFor="MetaKeyWord">Meta Keyword</Label>
          <Select
            isMulti
            value={formData.meta_key_word ? formData.meta_key_word.map(option => ({ value: option, label: option })) : []}
            onChange={(selectedOptions) =>
              handleInputChange(
                "meta_key_word",
                selectedOptions ? selectedOptions.map(option => option.value) : []
              )
            }
            options={[
              { value: "laravel", label: "Laravel" },
              { value: "Oikko", label: "Oikko" },
              { value: "Real Estate", label: "Real Estate" },
              { value: "PMS", label: "PMS" }
            ]}
            placeholder="Select options"
          />
        </div>
      </div>

      <div>
        <Label htmlFor="metaDescription">Meta Description</Label>
        <RichTextEditor
          value={formData.metaDescription || ""}
          onChange={(value) => handleInputChange("metaDescription", value)}
          placeholder="Enter the meta description..."
          height="150px"
        />
      </div>

      <div className="flex justify-end">
        <button
          onClick={handleSubmit}
          className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
          disabled={loading}
        >
          {loading ? "Saving..." : "Save Settings"}
        </button>
      </div>
    </TabsContent>
  );
};

export default Seo;
