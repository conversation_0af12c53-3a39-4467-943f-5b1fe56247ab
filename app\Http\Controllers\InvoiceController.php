<?php

namespace App\Http\Controllers;
use App\Models\Invoice;
use App\Models\Tanants;
use App\Models\Project;
use App\Models\PropertyService;
use App\Models\RentType;
use App\Models\LeaseType;
use App\Models\User;
use App\Models\InvoiceItem;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class InvoiceController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        //
        try{
            $query = Invoice::query();
            if ($request->has('search') && $request->search) {
                $search = $request->search; 
                $query->where(function ($q) use ($search) {
                    $q->where('invoice_number', 'LIKE', "%{$search}%")
                      ->orWhereHas('tenant', function ($subQ) use ($search) {
                          $subQ->where('first_name', 'LIKE', "%{$search}%")
                               ->orWhere('last_name', 'LIKE', "%{$search}%");
                      });
                });
            }
            $perPage = $request->get('per_page', 10);
            $invoices = $query->with(['tenant', 'property','customer','propertyItems.unit:id,unit_number','items','paymentStatus'])->paginate($perPage);
            
        
           
            return response()->json([
                'success' => true,
                'data' => $invoices
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve invoices: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        // Debugging the request data - this will show all request data including property repeater values
        // dd($request->all());

        try{
            $validated = $request->validate([
              
                'invoice_type' => 'required|string',
                'invoice_number' => 'required|string|max:255|unique:invoices,invoice_number',
                'invoice_date' => 'required|date',
                'customer_id' => 'nullable|exists:customers,id',
                'tenant_id' => 'nullable|exists:tanants,id',
                'property_id' => 'nullable|exists:projects,id',
                'amount' => 'nullable',
                'grand_total' => 'nullable',
                'terms_and_conditions' => 'nullable|string',
                'discount' => 'nullable',
                'is_percentage' => 'nullable|boolean',
                'payment_method_id' => 'nullable|exists:payment_methods,id',
                'payment_Type_id' => 'nullable|exists:payment_types,id',
                'payment_amount' => 'nullable',
                'due_amount' => 'nullable',
                'payment_status_id' => 'nullable|exists:payment_statuses,id',
                'due_date' => 'nullable|date',
                'notes' => 'nullable|string',
                'properties' => 'nullable|array', // Validate properties as an optional array
                'properties.*.property_id' => 'nullable|exists:projects,id',
                'properties.*.unit_id' => 'nullable|exists:project_units,id',
                'properties.*.amount' => 'nullable|numeric',
                'properties.*.tax_amount' => 'nullable|numeric',
                'properties.*.total_amount' => 'nullable|numeric',
            ]);
            
            $invoice = Invoice::create($validated);
 
            // Store property items if present
            if ($request->has('properties') && is_array($request->properties)) {
                foreach ($request->properties as $property) {
                    \App\Models\PropertyItem::create([
                        'invoice_id' => $invoice->id,
                        'property_id' => $property['property_id'],
                        'unit_id' => $property['unit_id'],
                        'amount' => $property['amount'],
                        'tax_amount' => $property['tax_amount'] ?? 0,
                        'total_amount' => $property['total_amount'] ?? 0,
                    ]);
                }
            }
                // Store invoice items if present
        if ($request->has('items') && is_array($request->items)) {
            foreach ($request->items as $item) {
                // Only save if item_name is not empty/null
                if (!empty($item['item_name'])) {
                    \App\Models\InvoiceItem::create([
                        'invoice_id' => $invoice->id,
                        'item_name' => $item['item_name'],
                        'qty' => $item['qty'] ?? 1,
                        'item_price' => $item['item_price'] ?? 0,
                        'item_tax' => $item['item_tax'] ?? 0,
                        'item_total_price' => $item['item_total_price'] ?? 0,
                    ]);
                }
            }
        }
            return response()->json([
                'success' => true,
                'data' => $invoice,
                'message' => 'Invoice created successfully'
            ], 201);
        }catch(\Exception $e){
           
            return response()->json([
                'success' => false,
                'message' => 'Failed to create invoice: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id):jsonResponse
    {
        //
        try{
            $invoice = Invoice::with(['tenant', 'property'])->findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $invoice
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve invoice: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id):jsonResponse
    {
        // Debugging the request data - uncomment to see all request data
        // dd($request->all());

        try{
            $invoice = Invoice::findOrFail($id);

            $validated = $request->validate([
          
                'invoice_type' => 'required|string',
                'invoice_number' => 'required|string|max:255|unique:invoices,invoice_number,' . $id,
                'invoice_date' => 'required|date',
                'customer_id' => 'nullable|exists:customers,id',
                'tenant_id' => 'nullable|exists:tanants,id',
                'property_id' => 'nullable|exists:projects,id',
                'amount' => 'nullable',
                'grand_total' => 'nullable',
                'terms_and_conditions' => 'nullable|string',
                'discount' => 'nullable',
                'is_percentage' => 'nullable|boolean',
                'payment_method_id' => 'nullable|exists:payment_methods,id',
                'payment_Type_id' => 'nullable|exists:payment_types,id',
                'payment_amount' => 'nullable',
                'due_amount' => 'nullable',
                'payment_status_id' => 'nullable|exists:payment_statuses,id',
                'due_date' => 'nullable|date',
                'notes' => 'nullable|string',
                'properties' => 'nullable|array', // Validate properties as an optional array
                'properties.*.property_id' => 'nullable|exists:projects,id',
                'properties.*.unit_id' => 'nullable|exists:project_units,id',
                'properties.*.amount' => 'nullable|numeric',
                'properties.*.tax_amount' => 'nullable|numeric',
                'properties.*.total_amount' => 'nullable|numeric',
                'items' => 'nullable|array', // Validate items as an optional array
                'items.*.item_name' => 'nullable|string',
                'items.*.qty' => 'nullable|numeric',
                'items.*.item_price' => 'nullable|numeric',
                'items.*.item_tax' => 'nullable|numeric',
                'items.*.item_total_price' => 'nullable|numeric',
            ]);

            // Update the main invoice record
            $invoice->update($validated);

            // Update property items - delete existing and create new ones
            if ($request->has('properties') && is_array($request->properties)) {
                // Delete existing property items
                \App\Models\PropertyItem::where('invoice_id', $invoice->id)->delete();

                // Create new property items
                foreach ($request->properties as $property) {
                    if (!empty($property['property_id'])) { // Only create if property_id is not empty
                        \App\Models\PropertyItem::create([
                            'invoice_id' => $invoice->id,
                            'property_id' => $property['property_id'],
                            'unit_id' => $property['unit_id'],
                            'amount' => $property['amount'],
                            'tax_amount' => $property['tax_amount'] ?? 0,
                            'total_amount' => $property['total_amount'] ?? 0,
                        ]);
                    }
                }
            } 

            // Update invoice items - delete existing and create new ones
            if ($request->has('items') && is_array($request->items)) {
                // Delete existing invoice items
                \App\Models\InvoiceItem::where('invoice_id', $invoice->id)->delete();

                // Create new invoice items
                foreach ($request->items as $item) {
                    // Only save if item_name is not empty/null
                    if (!empty($item['item_name'])) {
                        \App\Models\InvoiceItem::create([
                            'invoice_id' => $invoice->id,
                            'item_name' => $item['item_name'],
                            'qty' => $item['qty'] ?? 1,
                            'item_price' => $item['item_price'] ?? 0,
                            'item_tax' => $item['item_tax'] ?? 0,
                            'item_total_price' => $item['item_total_price'] ?? 0,
                        ]);
                    }
                }
            }

            return response()->json([
                'success' => true,
                'data' => $invoice->fresh(),
                'message' => 'Invoice updated successfully'
            ]);
        }catch(\Exception $e){
          
            return response()->json([
                'success' => false,
                'message' => 'Failed to update invoice: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id):jsonResponse
    {
        //
        try{
            $invoice = Invoice::findOrFail($id);
            $invoice->delete();
            return response()->json([
                'success' => true,
                'message' => 'Invoice deleted successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete invoice: ' . $e->getMessage()
            ], 500);
        }
    }

    public function paidInvoices(): JsonResponse
    {
        try{
            $invoices = Invoice::paid()->with(['tenant', 'property'])->get();
            return response()->json([
                'success' => true,
                'data' => $invoices
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve paid invoices: ' . $e->getMessage()
            ], 500);
        }
    }

    public function unpaidInvoices(): JsonResponse
    {
        try{
            $invoices = Invoice::unpaid()->with(['tenant', 'property'])->get();
            return response()->json([
                'success' => true,
                'data' => $invoices
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve unpaid invoices: ' . $e->getMessage()
            ], 500);
        }
    }

    public function overdueInvoices(): JsonResponse
    {
        try{
            $invoices = Invoice::overdue()->with(['tenant', 'property'])->get();
            return response()->json([
                'success' => true,
                'data' => $invoices
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve overdue invoices: ' . $e->getMessage()
            ], 500);
        }
    }

    public function statistics(): JsonResponse
    {
        try{
            $stats = [
                'total_invoices' => Invoice::count(),
                'paid_invoices' => Invoice::paid()->count(),
                'unpaid_invoices' => Invoice::unpaid()->count(),
                'overdue_invoices' => Invoice::overdue()->count(),
            ];
            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve invoice statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    public function softDelete( string $id): JsonResponse{
        try{
            $invoice = Invoice::findOrFail($id);
            $invoice->delete();
            return response()->json([
                'success' => true,
                'message' => 'Invoice deleted successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete invoice: ' . $e->getMessage()
            ], 500);
        }
    }   
    public function restore(string $id): JsonResponse{
        try{
            $invoice = Invoice::withTrashed()->findOrFail($id);
            $invoice->restore();
            return response()->json([
                'success' => true,
                'message' => 'Invoice restored successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to restore invoice: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getStatistics(): JsonResponse
    {
        try {
            // Get payment status IDs
            $paidStatusId = \App\Models\PaymentStatus::where('name', 'Paid')->first()?->id;
            $pendingStatusId = \App\Models\PaymentStatus::where('name', 'Pending')->first()?->id;
            $failedStatusId = \App\Models\PaymentStatus::where('name', 'Failed')->first()?->id;
            $refundedStatusId = \App\Models\PaymentStatus::where('name', 'Refunded')->first()?->id;
            $cancelledStatusId = \App\Models\PaymentStatus::where('name', 'Cancelled')->first()?->id;

            // Count invoices by status
            $totalInvoices = Invoice::count();
            $paidInvoices = Invoice::where('payment_status_id', $paidStatusId)->count();
            $pendingInvoices = Invoice::where('payment_status_id', $pendingStatusId)->count();
            $failedInvoices = Invoice::where('payment_status_id', $failedStatusId)->count();
            $refundedInvoices = Invoice::where('payment_status_id', $refundedStatusId)->count();
            $cancelledInvoices = Invoice::where('payment_status_id', $cancelledStatusId)->count();

            // Calculate overdue invoices (pending invoices past due date)
            $overdueInvoices = Invoice::where('payment_status_id', $pendingStatusId)
                ->where('due_date', '<', now())
                ->count();

            // Calculate amounts by status using grand_total (which includes all calculations)
            $totalAmount = Invoice::sum('grand_total');
            $paidAmount = Invoice::where('payment_status_id', $paidStatusId)->sum('grand_total');
            $pendingAmount = Invoice::where('payment_status_id', $pendingStatusId)->sum('grand_total');
            $failedAmount = Invoice::where('payment_status_id', $failedStatusId)->sum('grand_total');
            $refundedAmount = Invoice::where('payment_status_id', $refundedStatusId)->sum('grand_total');
            $cancelledAmount = Invoice::where('payment_status_id', $cancelledStatusId)->sum('grand_total');

            // Calculate overdue amount
            $overdueAmount = Invoice::where('payment_status_id', $pendingStatusId)
                ->where('due_date', '<', now())
                ->sum('grand_total');

            return response()->json([
                'success' => true,
                'data' => [
                    'counts' => [
                        'total' => $totalInvoices,
                        'paid' => $paidInvoices,
                        'pending' => $pendingInvoices,
                        'overdue' => $overdueInvoices,
                        'failed' => $failedInvoices,
                        'refunded' => $refundedInvoices,
                        'cancelled' => $cancelledInvoices,
                        // Legacy compatibility
                        'unpaid' => $pendingInvoices,
                    ],
                    'amounts' => [
                        'total' => (float) $totalAmount,
                        'paid' => (float) $paidAmount,
                        'pending' => (float) $pendingAmount,
                        'overdue' => (float) $overdueAmount,
                        'failed' => (float) $failedAmount,
                        'refunded' => (float) $refundedAmount,
                        'cancelled' => (float) $cancelledAmount,
                        // Legacy compatibility
                        'unpaid' => (float) $pendingAmount,
                    ]
                ]
            ]);
        } catch (\Exception $e) {
            \Log::error('Invoice statistics failed: ' . $e->getMessage(), [
                'exception' => $e,
                'trace' => $e->getTraceAsString()
            ]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to get invoice statistics',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
