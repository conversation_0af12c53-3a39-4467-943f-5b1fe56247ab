<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Tanants extends Model
{
    //
    protected $fillable = [
       
        'first_name',
        'last_name',
        'photo',
        'email',
        'phone',
        'previous_address',
        'permanent_address',
        'note',
        'status',
    ];

    protected $casts = [
        'status' => 'string',

    ];

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('status', 'active');
    }

    public function scopeInactive($query)
    {
        return $query->where('status', 'inactive');
    }

    public function documents()
    {
        return $this->hasMany(TenantsDocument::class, 'tenant_id');
    }

    public function emergencyContact()
    {
        return $this->hasOne(TenantEmergencyContact::class, 'tenant_id');
    }
}
