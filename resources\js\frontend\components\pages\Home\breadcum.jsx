import React, { useState, useEffect } from "react";

const Breadcum = ({ pageContents = [] }) => {
    const [breadcumData, setBreadcumData] = useState(null);
    useEffect(() => {
         // Find the breadcum widget (widget_id = 16)
    const breadcumWidget = pageContents.find(item => item.widget_id === 16);
    if (!breadcumWidget) return;

      try {
      const config =
        typeof breadcumWidget.pageContent === "string"
          ? JSON.parse(breadcumWidget.pageContent)
          : breadcumWidget.pageContent;
      setBreadcumData(config);
    } catch (error) {
      console.error("Error parsing BreadCum widget data:", error);
    }

    }, [pageContents]);

      // Show loading state
  if (!breadcumData) {
    return (
      <div className="home2-about-area pt-100 mb-100">
        <div className="container text-center">
          <h4>Loading Breadcum section...</h4>
        </div>
      </div>
    );
  }
    return (
        <>
              <div className="inner-page-banner" style={{ backgroundImage: 'url(/Frontend/assets/img/inner-page/inner-bg.png)' }}>
                <div className="banner-wrapper">
                    <div className="container-fluid">
                        <div className="banner-main-content-wrap">
                            <div className="row">
                                <div className="col-xl-6 col-lg-7 d-flex align-items-center">
                                    <div className="banner-content">
                                        <h1>{breadcumData.shoulder || "Our Brief History"}</h1>
                                        <ul className="breadcrumb-list">
                                            <li><a href="index.html">Home</a></li>
                                            <li>About</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </>
    )};
export default Breadcum;
