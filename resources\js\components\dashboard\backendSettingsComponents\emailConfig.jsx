import React from "react";  
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { TabsContent } from "@/components/ui/tabs";

const EmailConfig = ({ formData, handleInputChange, handleSubmit, loading }) => {
  return (
      <TabsContent value="email-configure" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div>
                    <Label htmlFor="driver">Driver</Label>
                        <Input
                            id="driver"
                            value={formData.driver || ""}
                            onChange={(e) => handleInputChange("driver", e.target.value)}
                            placeholder="smtp"
                        />
                    </div>

                     <div>
                        <Label htmlFor="host">Host</Label>
                            <Input
                                id="host"
                                value={formData.host || ""}
                                onChange={(e) => handleInputChange("host", e.target.value)}
                                placeholder="smtp.elasticemail.com"
                            />
                    </div>

                    <div>
                        <Label htmlFor="port">Port</Label>
                            <Input
                                id="port"
                                value={formData.port || ""}
                                onChange={(e) => handleInputChange("port", e.target.value)}
                                placeholder="2525"
                            />
                    </div>

                      <div>
                        <Label htmlFor="fromAdress">From Address</Label>
                            <Input
                                id="fromAdress"
                                value={formData.fromAdress || ""}
                                onChange={(e) => handleInputChange("fromAdress", e.target.value)}
                                placeholder="smtp.elasticemail.com"
                            />
                    </div>

                     <div>
                        <Label htmlFor="fromName">From Name</Label>
                            <Input
                                id="fromName"
                                value={formData.fromName || ""}
                                onChange={(e) => handleInputChange("fromName", e.target.value)}
                                placeholder="<EMAIL>"
                            />
                    </div>

                      <div>
                        <Label htmlFor="encryption">Encryption</Label>
                            <Input
                                id="encryption"
                                value={formData.encryption || ""}
                                onChange={(e) => handleInputChange("encryption", e.target.value)}
                                placeholder="TLS"
                            />
                    </div>

                      <div>
                        <Label htmlFor="username">User Name</Label>
                            <Input
                                id="username"
                                value={formData.username || ""}
                                onChange={(e) => handleInputChange("username", e.target.value)}
                                placeholder="use the username"
                            />
                    </div>

                     <div>
                        <Label htmlFor="password">Password</Label>
                            <Input
                                id="password"
                                type="password"
                                value={formData.password || ""}
                                onChange={(e) => handleInputChange("password", e.target.value)}
                                placeholder="use the password"
                            />
                    </div>
              </div>
                <div className="flex justify-end">
                    <button
                        onClick={handleSubmit}
                        className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
                        disabled={loading}
                    >
                        {loading ? "Saving..." : "Save Settings"}
                    </button>
                </div>
            </TabsContent>
    
  )};
  export default EmailConfig;
