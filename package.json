{"name": "laravel-react-dashboard", "version": "1.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "dev:frontend": "vite --config vite.frontend.config.js", "build:frontend": "vite build --config vite.frontend.config.js"}, "dependencies": {"@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@radix-ui/react-accordion": "^1.2.12", "@radix-ui/react-alert-dialog": "^1.1.15", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "^1.1.15", "@radix-ui/react-radio-group": "^1.3.8", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.14", "@radix-ui/react-tooltip": "^1.2.7", "@react-google-maps/api": "^2.19.2", "axios": "^1.10.0", "bootstrap": "^5.3.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.18", "google-map-react": "^2.2.1", "i18next": "^25.3.2", "jquery": "^3.7.1", "jquery-ui": "^1.14.1", "lucide-react": "^0.523.0", "quill": "^2.0.3", "react": "^18.2.0", "react-bootstrap": "^2.9.0", "react-countup": "^6.4.2", "react-day-picker": "^9.9.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dnd-menu-builder": "^0.0.8", "react-dnd-touch-backend": "^16.0.1", "react-dom": "^18.2.0", "react-fast-marquee": "^1.6.1", "react-hot-toast": "^2.6.0", "react-i18next": "^15.6.0", "react-loading-skeleton": "^3.3.1", "react-modal-video": "^2.0.1", "react-quill": "^2.0.0", "react-router-dom": "^7.6.2", "react-select": "^5.10.2", "react-slick": "^0.29.0", "react-toastify": "^9.1.3", "recharts": "^3.0.0", "slick-carousel": "^1.8.1", "sweetalert2": "^11.22.2", "swiper": "^8.3.2", "tailwind-merge": "^3.3.1", "use-count-up": "^3.0.1", "wow.js": "^1.2.2", "wowjs": "^1.1.3", "yet-another-react-lightbox": "^3.13.0"}, "devDependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "laravel-vite-plugin": "^0.8.1", "postcss": "^8.4.24", "tailwindcss": "^3.3.2", "tailwindcss-animate": "^1.0.7", "vite": "^4.3.9"}}