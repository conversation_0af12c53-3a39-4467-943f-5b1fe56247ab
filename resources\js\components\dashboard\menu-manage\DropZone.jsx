import React from 'react';
import { useDrop } from 'react-dnd';

const DropZone = ({ onDrop, isLast = false, position }) => {
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'MENU_ITEM',
    drop: (draggedItem) => {
      onDrop(draggedItem.index, position);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }), [onDrop, position]);

  if (!isOver && !canDrop) return null;

  return (
    <div
      ref={drop}
      className={`transition-all duration-200 ${
        isOver && canDrop
          ? 'h-8 my-2 bg-blue-50 border-2 border-dashed border-blue-300 rounded-lg flex items-center justify-center'
          : 'h-1 my-1'
      }`}
    >
      {isOver && canDrop && (
        <div className="flex items-center gap-2 text-blue-600 text-sm font-medium">
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
          <span>Drop here to place at position {position + 1}</span>
          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
        </div>
      )}
    </div>
  );
};

export default DropZone;