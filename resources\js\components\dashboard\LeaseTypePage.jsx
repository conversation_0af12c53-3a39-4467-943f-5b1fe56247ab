import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';

import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import LeaseTypeSearch from './LeaseTypeSearch';
import LeaseTypeTable from './LeaseTypeTable';
import LeaseTypeModal from './LeaseTypeModal';
import {
  Plus,
  Settings,
} from 'lucide-react';

import leaseTypeAPI from '@/services/leaseTypeAPI';

export default function LeaseTypePage() {
  const [leaseTypes, setLeaseTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [modalOpen, setModalOpen] = useState(false);
  const [editLeaseType, setEditLeaseType] = useState(null);
  const [form, setForm] = useState({ name: '', description: '', is_active: true, sort_order: 0 });
  const [saving, setSaving] = useState(false);

  // Fetch all lease types using leaseTypeAPI
  const fetchLeaseTypes = async () => {
    setLoading(true);
    try {
      const data = await leaseTypeAPI.getAll();
      // Handle paginated response
      if (data.success && data.data && data.data.data) {
        setLeaseTypes(data.data.data);
      } else {
        setLeaseTypes(data.data || data);
      }
    } catch (err) {
      setError(err.message || 'Failed to fetch lease types');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchLeaseTypes();
  }, []);

  // Handle successful save from modal
  const handleSaveSuccess = () => {
    setModalOpen(false);
    setForm({ name: '', description: '', is_active: true, sort_order: 0 });
    setEditLeaseType(null);
    fetchLeaseTypes();
  };

  // Edit handler
  const handleEdit = (leaseType) => {
    setEditLeaseType(leaseType);
    setForm({
      name: leaseType.name,
      description: leaseType.description,
      is_active: leaseType.is_active,
      sort_order: leaseType.sort_order || 0,
    });
    setModalOpen(true);
  };

  // Delete handler
  const handleDelete = async (leaseType) => {
    if (window.confirm(`Delete lease type "${leaseType.name}"?`)) {
      setSaving(true);
      try {
        await leaseTypeAPI.delete(leaseType.id);
        fetchLeaseTypes();
      } catch (err) {
        setError(err.message || 'Failed to delete lease type');
      } finally {
        setSaving(false);
      }
    }
  };

  // Toggle status handler
  const handleToggleStatus = async (leaseType) => {
    setSaving(true);
    try {
      await leaseTypeAPI.toggleStatus(leaseType.id);
      fetchLeaseTypes();
    } catch (err) {
      setError(err.message || 'Failed to toggle status');
    } finally {
      setSaving(false);
    }
  };

  // Open modal for create
  const handleCreate = () => {
    setEditLeaseType(null);
    setForm({ name: '', description: '', is_active: true, sort_order: 0 });
    setModalOpen(true);
  };

  // Filter results
  const filteredLeaseTypes = leaseTypes.filter((leaseType) => {
    const matchesSearch = leaseType.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus =
      statusFilter === 'all' ||
      (statusFilter === 'active' && leaseType.is_active) ||
      (statusFilter === 'inactive' && !leaseType.is_active);
    return matchesSearch && matchesStatus;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Lease Types</h1>
          <p className="text-muted-foreground">
            Manage available lease type categories
          </p>
        </div>
        <Button onClick={handleCreate} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Lease Type
        </Button>
      </div>

      {/* Error Message */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2 text-red-600">
              <span className="text-sm font-medium">Error:</span>
              <span className="text-sm">{error}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setError(null)}
                className="ml-auto"
              >
                Dismiss
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters */}
      <LeaseTypeSearch 
        searchTerm={searchTerm} 
        statusFilter={statusFilter} 
        setSearchTerm={setSearchTerm}
        setStatusFilter={setStatusFilter} 
        leaseType = {leaseTypes}
      />

      {/* Loading State */}
      {loading ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
            <p className="text-gray-500">Loading lease types...</p>
          </CardContent>
        </Card>
      ) : (
        <>
          {/* Lease Types List */}
          {filteredLeaseTypes.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredLeaseTypes.map((leaseType) => (
              <LeaseTypeTable
                key={leaseType.id}
                leaseType={leaseType}
                handleEdit={handleEdit}
                handleToggleStatus={handleToggleStatus}
                handleDelete={handleDelete}
         
              />
          ))}
        </div>
      ) : (
        <Card>
          <CardContent className="text-center py-12">
            <Settings className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No lease types found</h3>
            <p className="text-gray-500 mb-4">
              {searchTerm || statusFilter !== 'all'
                ? 'No lease types match your current filters.'
                : 'Get started by creating your first lease type.'}
            </p>
            {(!searchTerm && statusFilter === 'all') && (
              <Button onClick={handleCreate} className="gap-2">
                <Plus className="h-4 w-4" />
                Add Lease Type
              </Button>
            )}
          </CardContent>
        </Card>
      )}
        </>
      )}

      {/* Create/Edit Modal */}
      {modalOpen && (
          <LeaseTypeModal
            isOpen={modalOpen}
            onClose={() => setModalOpen(false)}
            onSuccess={handleSaveSuccess}
            leaseType={editLeaseType}
            mode={editLeaseType ? 'edit' : 'create'}
          />
      )}
    </div>
  );
}
