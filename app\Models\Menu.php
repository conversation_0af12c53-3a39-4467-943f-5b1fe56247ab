<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class Menu extends Model
{
    //
    protected $fillable = [
        'name',
        'is_active',
        'position',
        'parentId',
        'childPosition'
    ];

    protected $casts = [
        'is_active' => 'string',
    ];
    protected $attributes = [
        'is_active' => 'active',
    ];
    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', 1);
    }

    public function scopeInactive($query)
    {
        return $query->where('is_active', 0);
    }
}
