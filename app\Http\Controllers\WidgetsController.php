<?php

namespace App\Http\Controllers;

use App\Models\Widgets;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class WidgetsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index():JsonResponse
    {
        //
        try {
            $widgets = Widgets::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $widgets
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch widgets: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request):JsonResponse
    {
        //
        try {
            $validated = $request->validate([
                'image' => 'required|string|max:255',
                'name' => 'required|string|max:255',
            ]);
            $widget = Widgets::create($validated);
            return response()->json([
                'success' => true,
                'data' => $widget,
                'message' => 'Widget created successfully'
            ], 201);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to create widget: ' . $e->getMessage()
            ], 500);
        }

    }

    /**
     * Display the specified resource.
     */
    public function show(Widgets $widgets):JsonResponse
    {
        //
        try {
            return response()->json([
                'success' => true,
                'data' => $widgets
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch widget: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Widgets $widgets):JsonResponse
    {
        //
        try {
            $validated = $request->validate([
                'image' => 'required|string|max:255',
                'name' => 'required|string|max:255',
            ]);
            $widgets->update($validated);
            return response()->json([
                'success' => true,
                'data' => $widgets->fresh(),
                'message' => 'Widget updated successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update widget: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Widgets $widgets):JsonResponse
    {
        //
        try {
            $widgets->delete();
            return response()->json([
                'success' => true,
                'message' => 'Widget deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete widget: ' . $e->getMessage()
            ], 500);
        }
    }
}
