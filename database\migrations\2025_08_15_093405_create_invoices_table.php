<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoices', function (Blueprint $table) {
            $table->id();
            $table->string('invoice_number');
            $table->integer('tenant_id')->nullable();
            $table->integer('property_id');
            $table->date('issue_date');
            $table->date('due_date');
            $table->date('billing_date');
            $table->date('billing_end');
            $table->decimal('amount', 10, 2);
            $table->decimal('tax_amount', 10, 2 );
            $table->decimal('discount_amount', 10, 2 );
            $table->decimal('total_amount', 10, 2 );
            $table->enum('status', ['paid', 'unpaid', 'overdue'])->default('unpaid');
            $table->date('payment_date')->nullable();
            $table->enum  ('payment_method', ['cash', 'cheque', 'bank_transfer', 'card', 'other'])->nullable();
            $table->text('notes')->nullable();
            $table->integer('created_by'); 
            $table->integer('updated_by');
            $table->integer('soft_deleted')->default(0);       
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoices');
    }
};
