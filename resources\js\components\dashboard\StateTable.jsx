import React from 'react';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/hooks/useTranslation';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  ChevronLeft,
  ChevronRight,
} from 'lucide-react';
import Swal from 'sweetalert2';

const StateTable = ({
  states,
  loading,
  currentPage,
  totalPages,
  totalItems,
  itemsPerPage,
  onEdit,
  onDelete,
  onToggleStatus,
  onPageChange,
  onImagePreview
}) => {
  const { t } = useTranslation();

  // Handle image preview
  const handleImagePreview = (state) => {
    if (state.image) {
      Swal.fire({
        title: `${state.name} - State Image`,
        imageUrl: `/state/${state.image}`,
        imageWidth: 400,
        imageHeight: 300,
        imageAlt: `${state.name} image`,
        showCloseButton: true,
        showConfirmButton: false,
        customClass: {
          image: 'rounded-lg'
        }
      });
    } else {
      Swal.fire({
        icon: 'info',
        title: 'No Image',
        text: `No image available for ${state.name}`,
        confirmButtonText: 'OK'
      });
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2 text-gray-600">Loading states...</span>
      </div>
    );
  }

  if (states.length === 0) {
    return (
      <div className="text-center py-8">
        <p className="text-gray-500">{t('state.messages.noStatesFound')}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Table */}
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">#</TableHead>
              <TableHead>Image</TableHead>
              <TableHead>{t('state.fields.name')}</TableHead>
              <TableHead>{t('state.fields.country')}</TableHead>
              <TableHead>{t('state.fields.status')}</TableHead>
              <TableHead>{t('common.fields.createdAt')}</TableHead>
              <TableHead className="text-right">{t('common.fields.actions')}</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {states.map((state, index) => (
              <TableRow key={state.id} className="hover:bg-gray-50">
                <TableCell className="font-medium">
                  {(currentPage - 1) * itemsPerPage + index + 1}
                </TableCell>
                <TableCell>
                  {state.image ? (
                    <button
                      onClick={() => handleImagePreview(state)}
                      className="relative group"
                    >
                      <img
                        src={`/state/${state.image}`}
                        alt={`${state.name} image`}
                        className="w-12 h-12 object-cover rounded-lg border border-gray-200 hover:border-blue-400 transition-colors cursor-pointer"
                        onError={(e) => {
                          e.target.src = '/placeholder.png';
                          e.target.alt = 'Image not found';
                        }}
                      />
                      <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 rounded-lg transition-all duration-200 flex items-center justify-center">
                        <Eye className="w-4 h-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                      </div>
                    </button>
                  ) : (
                    <div className="w-12 h-12 bg-gray-100 border border-gray-200 rounded-lg flex items-center justify-center">
                      <span className="text-xs text-gray-400">No img</span>
                    </div>
                  )}
                </TableCell>
                <TableCell>
                  <div className="font-medium">{state.name}</div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center space-x-2">
                    <span>{state.country?.name || 'N/A'}</span>
                    {state.country?.code && (
                      <Badge variant="outline" className="text-xs">
                        {state.country.code}
                      </Badge>
                    )}
                  </div>
                </TableCell>
                <TableCell>
                  <button
                    onClick={() => onToggleStatus(state)}
                    className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium transition-colors ${
                      state.status === 'active'
                        ? 'bg-green-100 text-green-800 hover:bg-green-200'
                        : 'bg-red-100 text-red-800 hover:bg-red-200'
                    }`}
                  >
                    {state.status === 'active' ? t('common.status.active') : t('common.status.inactive')}
                  </button>
                </TableCell>
                <TableCell>
                  <div className="text-sm text-gray-600">
                    {new Date(state.created_at).toLocaleDateString()}
                  </div>
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem onClick={() => onEdit(state)}>
                        <Edit className="mr-2 h-4 w-4" />
                        {t('common.buttons.edit')}
                      </DropdownMenuItem>
                      {state.image && (
                        <DropdownMenuItem onClick={() => handleImagePreview(state)}>
                          <Eye className="mr-2 h-4 w-4" />
                          View Image
                        </DropdownMenuItem>
                      )}
                      <DropdownMenuItem 
                        onClick={() => onDelete(state)}
                        className="text-red-600"
                      >
                        <Trash2 className="mr-2 h-4 w-4" />
                        {t('common.buttons.delete')}
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-700">
            Showing {(currentPage - 1) * itemsPerPage + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} results
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage - 1)}
              disabled={currentPage <= 1}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            
            <div className="flex items-center space-x-1">
              {[...Array(totalPages)].map((_, i) => {
                const page = i + 1;
                const isCurrentPage = page === currentPage;
                const shouldShow = 
                  page === 1 || 
                  page === totalPages || 
                  (page >= currentPage - 1 && page <= currentPage + 1);

                if (!shouldShow) {
                  if (page === currentPage - 2 || page === currentPage + 2) {
                    return <span key={page} className="px-2 text-gray-400">...</span>;
                  }
                  return null;
                }

                return (
                  <Button
                    key={page}
                    variant={isCurrentPage ? "default" : "outline"}
                    size="sm"
                    onClick={() => onPageChange(page)}
                    className="w-8 h-8 p-0"
                  >
                    {page}
                  </Button>
                );
              })}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => onPageChange(currentPage + 1)}
              disabled={currentPage >= totalPages}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};

export default StateTable;