import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Accept': 'application/json',
    // Removed 'Content-Type': 'application/json' to allow file uploads
  },
});

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

const frontendSettingsAPI = {
  // Get all frontend settings
  getAll: async () => {
    try {
      const response = await api.get('/frontend-settings');
      return response.data;
    } catch (error) {
      console.error('Error fetching frontend settings:', error);
      throw error;
    }
  },
  // Create new frontend settings
  create: async (data) => {
    try {
      const response = await api.post('/frontend-settings', data);
      return response.data;
    } catch (error) {
      console.error('Error creating frontend settings:', error);
      throw error;
    }
  },
  // Update frontend settings
  update: async (id, data) => {
    try {
      const response = await api.put(`/frontend-settings/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating frontend settings:', error);
      throw error;
    }
  },
  // Delete frontend settings
  delete: async (id) => {
    try {
      const response = await api.delete(`/frontend-settings/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting frontend settings:', error);
      throw error;
    }
  },

  // Upload logo file
  uploadLogo: async (file) => {
    try {
      const formData = new FormData();
      formData.append('logo', file);

      const response = await api.post('/frontend-settings/upload-logo', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading logo:', error);
      throw error;
    }
  },
};

export default frontendSettingsAPI;
