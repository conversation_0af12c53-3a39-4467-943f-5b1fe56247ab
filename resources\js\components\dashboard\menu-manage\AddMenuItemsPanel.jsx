import React from 'react';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { FileText, ExternalLink } from "lucide-react";
import MenuItemsList from './MenuItemsList';
import CustomLinkForm from './CustomLinkForm';

const AddMenuItemsPanel = ({
  // Menu items props
  availableMenus,
  selectedMenus,
  onMenuSelection,
  onSelectAllMenus,
  onDeselectAllMenus,
  onAddSelectedPages,
  selectedMenuPosition,
  loadingPages,
  
  // Custom link props
  customLink,
  onCustomLinkChange,
  onCustomLinkSubmit,
}) => {
  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold">Add Menu Items</h3>
      
      <Accordion type="single" collapsible className="w-full">
        <AccordionItem value="pages">
          <AccordionTrigger className="text-left">
            <div className="flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Menu Items
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <MenuItemsList
              availableMenus={availableMenus}
              selectedMenus={selectedMenus}
              onMenuSelection={onMenuSelection}
              onSelectAllMenus={onSelectAllMenus}
              onDeselectAllMenus={onDeselectAllMenus}
              onAddSelectedPages={onAddSelectedPages}
              selectedMenuPosition={selectedMenuPosition}
              loadingPages={loadingPages}
            />
          </AccordionContent>
        </AccordionItem>
        
        <AccordionItem value="custom-links">
          <AccordionTrigger className="text-left">
            <div className="flex items-center gap-2">
              <ExternalLink className="h-4 w-4" />
              Custom Link
            </div>
          </AccordionTrigger>
          <AccordionContent>
            <CustomLinkForm
              customLink={customLink}
              onCustomLinkChange={onCustomLinkChange}
              onAddCustomLink={onCustomLinkSubmit}
              selectedMenuPosition={selectedMenuPosition}
            />
          </AccordionContent>
        </AccordionItem>
      </Accordion>
    </div>
  );
};

export default AddMenuItemsPanel;