  import React, { useState, useEffect } from 'react';
  import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
  import { But<PERSON> } from '@/components/ui/button';
  import { Input } from '@/components/ui/input';
  import { Badge } from '@/components/ui/badge';
  import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
  import { toast } from '@/hooks/use-toast';
  import { Loader2, Plus, Search, Edit2, Trash2, Eye, Building2, MapPin, DollarSign, Camera, Video, Home } from 'lucide-react';
  import { useTranslation } from '@/hooks/useTranslation';
  import ProjectModal from './ProjectModal';
  import projectAPI from '@/services/projectAPI';
  import ProjectStatistics from './ProjectStatistics';
  import ProjectSearch from './ProjectSearch';
  import ProjectTable from './ProjectTable';
  const ProjectPage = () => {
    const { t } = useTranslation();
    const [projects, setProjects] = useState([]);
    const [loading, setLoading] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [statusFilter, setStatusFilter] = useState('all'); 
    const [typeFilter, setTypeFilter] = useState('all');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [perPage, setPerPage] = useState(10);
    const [sortBy, setSortBy] = useState('created_at');
    const [sortOrder, setSortOrder] = useState('desc');
    const [statistics, setStatistics] = useState({
      total: 0,
      featured: 0,
      available: 0,
      completed: 0,
      planning: 0,
      under_construction: 0,
      sold: 0,
      rented: 0
    });
    const [showModal, setShowModal] = useState(false);
    const [selectedProject, setSelectedProject] = useState(null);
    const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'
    // Helper to strip HTML tags
    const stripHtml = (html) => {
      const div = document.createElement('div');
      div.innerHTML = html;
      return div.textContent || div.innerText || '';
    };
    // Property types and statuses
    const propertyTypes = {
      residential: 'Residential',
      commercial: 'Commercial',
      industrial: 'Industrial',
      land: 'Land',
      mixed: 'Mixed Use'
    };

    const propertyStatuses = {
      planning: 'Planning',
      under_construction: 'Under Construction',
      completed: 'Completed',
      sold: 'Sold',
      rented: 'Rented',
      maintenance: 'Under Maintenance'
    };

    // Fetch projects
    const fetchProjects = async (page = 1) => {
      setLoading(true);
      try {
        const params = {
          page,
          per_page: perPage,
          search: searchTerm,
          status: statusFilter !== 'all' ? statusFilter : '',
          property_type: typeFilter !== 'all' ? typeFilter : '',
          sort_by: sortBy,
          sort_order: sortOrder
        };

        const response = await projectAPI.getAll(params);
        setProjects(response.data.data);
        setCurrentPage(response.data.current_page);
        setTotalPages(response.data.last_page);
        // Only update statistics if they exist in the response
        if (response.data.statistics) {
          setStatistics(response.data.statistics);
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
        toast({
          title: "Error",
          description: "Failed to fetch projects",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    // Fetch statistics
    const fetchStatistics = async () => {
      try {
        const response = await projectAPI.getStatistics();
        if (response.success && response.data) {
          setStatistics(response.data);
        }
      } catch (error) {
        console.error('Error fetching statistics:', error);
        // Keep the default values from initial state
      }
    };

    useEffect(() => {
      fetchProjects(1);
      fetchStatistics();
    }, [perPage, searchTerm, statusFilter, typeFilter, sortBy, sortOrder]);

    // Handle search
    const handleSearch = (e) => {
      e.preventDefault();
      fetchProjects(1);
    };

    // Handle modal operations
    const handleCreate = () => {
      setSelectedProject(null);
      setModalMode('create');
      setShowModal(true);
    };

    const handleEdit = (project) => {
      setSelectedProject(project);
      setModalMode('edit');
      setShowModal(true);
    };

    const handleView = (project) => {
      setSelectedProject(project);
      setModalMode('view');
      setShowModal(true);
    };

    const handleDelete = async (project) => {
      if (window.confirm('Are you sure you want to delete this project?')) {
        try {
          await projectAPI.delete(project.id);
          toast({
            title: "Success",
            description: "Project deleted successfully",
          });
          fetchProjects(currentPage);
          fetchStatistics();
        } catch (error) {
          toast({
            title: "Error",
            description: error.response?.data?.message || "Failed to delete project",
            variant: "destructive",
          });
        }
      }
    };

    const handleModalSuccess = () => {
      setShowModal(false);
      fetchProjects(currentPage);
      fetchStatistics();
    };

    const getStatusBadgeVariant = (status) => {
      const variants = {
        planning: 'secondary',
        under_construction: 'outline',
        completed: 'default',
        sold: 'destructive',
        rented: 'secondary',
        maintenance: 'outline'
      };
      return variants[status] || 'default';
    };

    return (
      <div className=" mx-auto p-6">
        {/* Header */}
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
              <Building2 className="h-8 w-8" />
              Properties
            </h1>
            <p className="text-gray-600 dark:text-gray-300">
              Manage your property portfolio
            </p>
          </div>
          <Button onClick={handleCreate}>
            <Plus className="h-4 w-4 mr-2" />
            Add Property
          </Button>
        </div>

        {/* Statistics Cards */}
        <ProjectStatistics statistics={statistics} />

        {/* Filters */}
          <ProjectSearch
            searchTerm={searchTerm}
            statusFilter={statusFilter}
            typeFilter={typeFilter}
            loading={loading}
            propertyTypes={propertyTypes}
            propertyStatuses={propertyStatuses}
            handleSearch={handleSearch}
            setSearchTerm={setSearchTerm}
            setStatusFilter={setStatusFilter}
            setTypeFilter={setTypeFilter}
      
          />

        {/* Projects Table */}
          <ProjectTable
            projects={projects}
            loading={loading}
            currentPage={currentPage}
            totalPages={totalPages}
            fetchProjects={fetchProjects}
            handleEdit={handleEdit}
            handleView={handleView}
            handleDelete={handleDelete}
             getStatusBadgeVariant={getStatusBadgeVariant}
             propertyTypes={propertyTypes}
             propertyStatuses={propertyStatuses}
               stripHtml={stripHtml}
          />

        {/* Project Modal */}
        <ProjectModal
          isOpen={showModal}
          onClose={() => setShowModal(false)}
          project={selectedProject}
          mode={modalMode}
          onSuccess={handleModalSuccess}
        />
      </div>
    );
  };

  export default ProjectPage;
