import axios from 'axios';

// Determine the correct API base URL
const getApiBaseUrl = () => {
  // In development with <PERSON><PERSON> serve
  if (window.location.port === '5173') {
    return 'http://localhost:8000/api';
  }
  // In production or <PERSON><PERSON> served directly
  return '/api';
};

const API_BASE_URL = getApiBaseUrl();

// Debug statement removed

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      // Handle unauthorized access
      localStorage.removeItem('auth_token');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// State API methods
export const stateAPI = {
  // Get all states
  getStates: async (params = {}) => {
    try {
      const response = await api.get('/states', { params });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get single state
  getState: async (id) => {
    try {
      const response = await api.get(`/states/${id}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Create new state
  createState: async (stateData) => {
    try {
      // For FormData uploads, we need to remove Content-Type to let browser set it automatically
      const config = {};
      if (stateData instanceof FormData) {
        config.headers = {
          'Content-Type': undefined, // This removes the default Content-Type header
        };
      }
      
      const response = await api.post('/states', stateData, config);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Update state
  updateState: async (id, stateData) => {
    try {
      // For regular JSON updates (no files)
      const response = await api.put(`/states/${id}`, stateData);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Update state with file upload (uses POST with _method override)
  updateStateWithFile: async (id, formData) => {
    try {
      // Use POST with _method override for file uploads to avoid Laravel FormData+PUT issues
      const config = {
        headers: {
          'Content-Type': undefined, // Let browser set multipart/form-data
        }
      };
      
      const response = await api.post(`/states/${id}`, formData, config);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Delete state
  deleteState: async (id) => {
    try {
      const response = await api.delete(`/states/${id}`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Toggle state status
  toggleStatus: async (id) => {
    try {
      const response = await api.patch(`/states/${id}/toggle-status`);
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get states by country
  getStatesByCountry: async (countryId, params = {}) => {
    try {
      const response = await api.get(`/states/country/${countryId}`, { params });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get states for dropdown (public access)
  getStatesDropdown: async (params = {}) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/states-dropdown`, { params });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Search states (public access)
  searchStates: async (params = {}) => {
    try {
      const response = await axios.get(`${API_BASE_URL}/states-search`, { params });
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Get statistics
  getStatistics: async () => {
    try {
      const response = await api.get('/states-statistics');
      return response.data;
    } catch (error) {
      throw error.response?.data || error.message;
    }
  },

  // Public API methods (no authentication required)
  public: {
    // Get states with pagination (public)
    getStates: async (params = {}) => {
      try {
        const response = await axios.get(`${API_BASE_URL}/public/states/index`, { params });
        return response.data;
      } catch (error) {
        throw error.response?.data || error.message;
      }
    },

    // Get states by country (public)
    getStatesByCountry: async (countryId, params = {}) => {
      try {
        const response = await axios.get(`${API_BASE_URL}/public/states/country/${countryId}`, { params });
        return response.data;
      } catch (error) {
        throw error.response?.data || error.message;
      }
    },

    // Get states dropdown (public)
    getStatesDropdown: async (params = {}) => {
      try {
        const response = await axios.get(`${API_BASE_URL}/public/states`, { params });
        return response.data;
      } catch (error) {
        throw error.response?.data || error.message;
      }
    }
  }
};

export default stateAPI;
