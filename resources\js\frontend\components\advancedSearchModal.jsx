import React from "react";

const AdvancedSearchModal = () => {
  return (
    <div
      className="modal adSearch-modal fade"
      id="adSearchModal01"
      tabIndex="-1"
      aria-hidden="true"
    >
      <div className="modal-dialog modal-lg modal-dialog-centered">
        <div className="modal-content">
          <button
            type="button"
            className="btn-close"
            data-bs-dismiss="modal"
            aria-label="Close"
          >
            <i className="bi bi-x"></i>
          </button>
          <div className="modal-body">
            <form>
              <h5 className="main-title">Advanced Option</h5>
              <div className="row">
                <div className="col-md-12 mb-30">
                  <div className="form-inner">
                    <select>
                      <option value="1">Sydne City, Australia</option>
                      <option value="2">Dhaka, Bangladesh</option>
                      <option value="3">Tokyo, Japan</option>
                    </select>
                  </div>
                </div>

                <h5>More Filter</h5>
                <div className="row mb-10">
                  <div className="col-md-6 mb-20">
                    <div className="form-inner">
                      <label>Select For</label>
                      <select>
                        <option value="1">Rent</option>
                        <option value="2">Sale</option>
                        <option value="3">Buy</option>
                      </select>
                    </div>
                  </div>

                  <div className="col-md-6 mb-20">
                    <div className="form-inner">
                      <label>Property Type</label>
                      <select>
                        <option value="1">Health Care</option>
                        <option value="2">Development</option>
                        <option value="3">Industrial</option>
                        <option value="4">Home Town</option>
                        <option value="5">Bungalow</option>
                        <option value="6">House</option>
                      </select>
                    </div>
                  </div>

                  <div className="col-md-6 mb-20">
                    <div className="form-inner">
                      <label>Build Year</label>
                      <select>
                        <option value="1">2015</option>
                        <option value="2">2016</option>
                        <option value="3">2017</option>
                        <option value="4">2018</option>
                      </select>
                    </div>
                  </div>

                  <div className="col-md-6 mb-20">
                    <div className="form-inner">
                      <label>Condition</label>
                      <select>
                        <option value="1">Used</option>
                        <option value="2">New</option>
                      </select>
                    </div>
                  </div>

                  <div className="col-md-6 mb-20">
                    <div className="form-inner">
                      <label>Property Size</label>
                      <select>
                        <option value="1">1200sqf</option>
                        <option value="2">1500sqf</option>
                        <option value="3">2400sqf</option>
                        <option value="4">2500sqf</option>
                      </select>
                    </div>
                  </div>

                  <div className="col-md-6 mb-20">
                    <div className="form-inner">
                      <label>Number Of Room</label>
                      <select>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                      </select>
                    </div>
                  </div>

                  <div className="col-md-6 mb-20">
                    <div className="form-inner">
                      <label>Number Of Bath</label>
                      <select>
                        <option value="1">1</option>
                        <option value="2">2</option>
                        <option value="3">3</option>
                        <option value="4">4</option>
                        <option value="5">5</option>
                      </select>
                    </div>
                  </div>

                  <div className="col-md-6 mb-20">
                    <div className="form-inner">
                      <label>Property Face</label>
                      <select>
                        <option value="1">North</option>
                        <option value="2">South</option>
                        <option value="3">East</option>
                        <option value="4">West</option>
                        <option value="5">Other</option>
                      </select>
                    </div>
                  </div>
                </div>

                <h5 className="mb-20">Price Range</h5>
                <div className="row">
                  <div className="col-lg-6 mb-20">
                    <div className="range-wrapper2">
                      <div className="slider-wrapper">
                        <div id="eg-range-slider"></div>
                      </div>
                      <div className="valus">
                        <div className="min-value">
                          <span>$</span>
                          <input type="text" className="from" value="200" readOnly />
                        </div>
                        <div className="min-value">
                          <span>$</span>
                          <input type="text" className="to" value="2000" readOnly />
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="col-md-3 mb-20">
                    <div className="form-inner">
                      <label>Min (Price)</label>
                      <select>
                        <option value="1">$2,234</option>
                        <option value="2">$3,234</option>
                        <option value="3">$4,234</option>
                        <option value="4">$5,234</option>
                      </select>
                    </div>
                  </div>

                  <div className="col-md-3 mb-20">
                    <div className="form-inner">
                      <label>Max (Price)</label>
                      <select>
                        <option value="1">$12,234</option>
                        <option value="2">$13,234</option>
                        <option value="3">$14,234</option>
                        <option value="4">$15,234</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>

              <div className="apply-btn pt-30">
                <button className="primary-btn2" type="submit">
                  Apply Filter
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdvancedSearchModal;
