import React, { useEffect, useState } from "react";

const FeaturedProperties = () => {
    const [properties, setProperties] = useState([]);

    useEffect(() => {
        fetch("/api/frontend/featured/projects")
            .then((res) => res.json())
            .then((data) => {
                if (data.success && Array.isArray(data.data)) {
                    setProperties(data.data);
                }
            });
    }, []);

    return (
        <div className="featured-car-section mb-100">
            <div className="container">
                <div className="row mb-50 wow fadeInUp" data-wow-delay="200ms">
                    <div className="col-lg-12 d-flex align-items-center justify-content-between gap-3 flex-wrap">
                        <div className="section-title-2">
                            <h2>Featured Property</h2>
                            <p>Here are some of the featured Apartment in different categories</p>
                        </div>
                    </div>
                </div>
            </div>
            <div className="container-fluid">
                <div className="row wow fadeInUp" data-wow-delay="300ms">
                    <div className="swiper home2-featured-slider">
                        <div className="swiper-wrapper">
                            {properties.map((property) => (
                                <div className="swiper-slide" key={property.id}>
                                    <div className="feature-card">
                                        <div className="product-img">
                                            <img className="img-fluid" src={property.featured_image?.image_url || "assets/img/home2/feature-1.png"} alt={property.title} />
                                        </div>
                                        <div className="product-content">
                                            <div className="price">
                                                <strong>{property.total_price ? `$${property.total_price}` : ""}</strong>
                                            </div>
                                            <h5><a href={`property-details/${property.id}`}>{property.title}</a></h5>
                                            <ul className="features">
                                                <li>
                                                    <img src="/Frontend/assets/img/home2/icon/bed1.svg" alt="" />
                                                    {property.bedrooms ?? "-"} Beds
                                                </li>
                                                <li>
                                                    <img src="/Frontend/assets/img/home2/icon/bath1.svg" alt="" />
                                                    {property.bathrooms ?? "-"} Baths
                                                </li>
                                                <li>
                                                    <img src="/Frontend/assets/img/home2/icon/size1.svg" alt="" />
                                                    Sq.ft- {property.area_sqft ?? "-"}
                                                </li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default FeaturedProperties;
