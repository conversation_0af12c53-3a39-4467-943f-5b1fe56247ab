<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('invoice_items', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('invoice_id');
            $table->string('item_name')->nullable();
            $table->integer('qty')->default(1);
            $table->decimal('item_price', 12, 2)->default(0);
            $table->decimal('item_tax', 12, 2)->default(0);
            $table->decimal('item_total_price', 12, 2)->default(0);
            $table->timestamps();
           
        
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('invoice_items');
    }
};
