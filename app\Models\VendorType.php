<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use App\Traits\AuditableTrait;
use App\Traits\EnvironmentAwareTrait;

class VendorType extends Model
{
    use HasFactory, AuditableTrait, EnvironmentAwareTrait;

    protected $fillable = [
        'name',
        'is_active',
        'created_by',
        'updated_by'
    ];

    protected $casts = [
        'status' => 'string'
    ];

    /**
     * Get the user who created this vendor type
     */
    public function creator()
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * Get the user who last updated this vendor type
     */
    public function updater()
    {
        return $this->belongsTo(User::class, 'updated_by');
    }

    /**
     * Scope a query to only include active vendor types
     */
    public function scopeActive($query)
    {
        return $query->where('is_active',1);
    }

    /**
     * Scope a query to only include inactive vendor types
     */
    public function scopeInactive($query)
    {
        return $query->where('is_active', 0);
    }
}
