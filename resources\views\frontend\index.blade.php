@viteReactRefresh 
@vite(['resources/js/frontend/App.jsx'])
<!doctype html>
<html lang="en">

<head>
    <!-- Required meta tags -->
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- Bootstrap CSS -->
    <link href="{{ asset('Frontend/assets/css/bootstrap.min.css')}}" rel="stylesheet">
    <link href="{{ asset('Frontend/assets/css/jquery-ui.css')}}" rel="stylesheet">
    <!-- Bootstrap Icon CSS -->
    <link href="{{ asset('Frontend/assets/css/bootstrap-icons.css')}}" rel="stylesheet">
    <!-- Fontawesome all CSS -->
    <link href="{{ asset('Frontend/assets/css/all.min.css')}}" rel="stylesheet">
    <!-- Animate CSS -->
    <link href="{{ asset('Frontend/assets/css/animate.min.css')}}" rel="stylesheet">
    <!-- FancyBox CSS -->
    <link href="{{ asset('Frontend/assets/css/jquery.fancybox.min.css')}}" rel="stylesheet">

    <!-- Fontawesome CSS -->
    <link href="{{ asset('Frontend/assets/css/fontawesome.min.css')}}" rel="stylesheet">
    <!-- Swiper slider CSS -->
    <link rel="stylesheet" href="{{ asset('Frontend/assets/css/swiper-bundle.min.css')}}">
    <!-- Slick slider CSS -->
    <link rel="stylesheet" href="{{ asset('Frontend/assets/css/slick.css')}}">
    <link rel="stylesheet" href="{{ asset('Frontend/assets/css/slick-theme.css')}}">
    <!-- Magnific-popup CSS -->
    <link rel="stylesheet" href="{{ asset('Frontend/assets/css/magnific-popup.css')}}">
    <!-- BoxIcon  CSS -->
    <link href="{{ asset('Frontend/assets/css/boxicons.min.css')}}" rel="stylesheet">
    <!-- Select2  CSS -->
    <link href="{{ asset('Frontend/assets/css/nice-select.css')}}" rel="stylesheet">
    <!--  Style CSS  -->
    <link rel="stylesheet" href="{{ asset('Frontend/assets/css/style.css')}}">
    <!-- Title -->
    <title>Neckle - Real Estate HTML Template</title>
    <link rel="icon" href="{{ asset('Frontend/assets/img/sm-logo.svg')}}" type="image/gif" sizes="20x20">

    
</head>

<body>
    <div id="react-root"></div>
    
    <!-- Scripts moved here to load after React renders -->
    <!-- jQuery JS -->
    <script src="{{asset('Frontend/assets/js/jquery-3.7.0.min.js')}}"></script>
    <!-- Bootstrap JS -->
    <script src="{{asset('Frontend/assets/js/bootstrap.min.js')}}"></script>
    <!-- jQuery UI JS -->
    <script src="{{asset('Frontend/assets/js/jquery-ui.js')}}"></script>
    <!-- Counter JS -->
    <script src="{{asset('Frontend/assets/js/jquery.counterup.min.js')}}"></script>
    <!-- Swiper slider JS -->
    <script src="{{asset('Frontend/assets/js/swiper-bundle.min.js')}}"></script>
    <!-- Slick slider JS -->
    <script src="{{asset('Frontend/assets/js/slick.js')}}"></script>
    <!-- Waypoints JS -->
    <script src="{{asset('Frontend/assets/js/waypoints.min.js')}}"></script>
    <!-- Magnific popup JS -->
    <script src="{{asset('Frontend/assets/js/jquery.magnific-popup.min.js')}}"></script>
    <!-- Marquee JS -->
    <script src="{{asset('Frontend/assets/js/jquery.marquee.min.js')}}"></script>
    <!-- Select2  JS -->
    <script src="{{asset('Frontend/assets/js/jquery.nice-select.min.js')}}"></script>
    <!-- FancyBox JS -->
    <script src="{{asset('Frontend/assets/js/jquery.fancybox.min.js')}}"></script>
    
    <!-- Wait for React to render before loading custom.js -->
    <script>
        // Wait for React to mount
        setTimeout(() => {
            // Load custom.js after React has rendered
            const script = document.createElement('script');
            script.src = "{{asset('Frontend/assets/js/custom.js')}}";
            document.head.appendChild(script);
            
            // Initialize marquee after custom.js loads
            script.onload = function() {
                if (typeof $ !== 'undefined' && $.fn.marquee) {
                    $(".marquee_text").marquee({
                        direction: "left",
                        duration: 25000,
                        gap: 50,
                        delayBeforeStart: 0,
                        duplicated: true,
                        startVisible: true,
                    });
                    $(".marquee_text2").marquee({
                        direction: "left",
                        duration: 25000,
                        gap: 50,
                        delayBeforeStart: 0,
                        duplicated: true,
                        startVisible: true,
                    });
                }
            };
        }, 1000); // Wait 1 second for React to render
    </script>
</body>
 <!-- Start Footer Section -->
   
    <!-- End Footer Section -->


</html>
