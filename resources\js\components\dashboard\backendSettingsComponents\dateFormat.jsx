const dateFormats = [
  { value: "MM/DD/YYYY", label: " 12/31/2025" },
  { value: "DD/MM/YYYY", label: " 31/12/2025" },
  { value: "YYYY/MM/DD", label: " 2025/12/31" },
  { value: "YYYY-MM-DD", label: " 2025-12-31" },
  { value: "DD-MM-YYYY", label: " 31-12-2025" },
  { value: "MM-DD-YYYY", label: " 12-31-2025" },
  { value: "DD.MM.YYYY", label: " 31.12.2025" },
  { value: "YYYY.MM.DD", label: " 2025.12.31" },
  { value: "Do MMM YYYY", label: " 31st Dec 2025" },
  { value: "MMM Do, YYYY", label: " Dec 31st, 2025" },
  { value: "MMMM Do, YYYY", label: " December 31st, 2025" },
  { value: "ddd, MMM D, YYYY", label: " Wed, Dec 31, 2025" },
  { value: "dddd, MMMM D, YYYY", label: " Wednesday, December 31, 2025" },
  { value: "MM/DD/YY", label: " 12/31/25" },
  { value: "DD/MM/YY", label: " 31/12/25" },
  { value: "YY/MM/DD", label: " 25/12/31" }
];

export default dateFormats;
