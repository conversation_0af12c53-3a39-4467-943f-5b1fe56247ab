import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { TabsContent } from "@/components/ui/tabs";

const GoogleAnalytics = ({ formData, handleInputChange, handleSubmit, loading }) => {
  return (
     <TabsContent value="google-analytics" className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                          <div>
                            <Label htmlFor="googleAnalytics">Google Analytics ID</Label>
                            <Input
                              id="googleAnalytics"
                              value={formData.googleAnalytics || ""}
                              onChange={(e) => handleInputChange('googleAnalytics', e.target.value)}
                              placeholder="Google Analytics ID"
                            />
                          </div>
                          <div>
                            <Label htmlFor="googleClientID">Google Client ID</Label>
                            <Input
                              id="googleClientID"
                              value={formData.googleClientID || ""}
                              onChange={(e) => handleInputChange('googleClientID', e.target.value)}
                              placeholder="Google Client ID"
                            />
                          </div>
                          <div>
                            <Label htmlFor="googleClientSecret">Google Client Secret</Label>
                            <Input
                              id="googleClientSecret"
                              value={formData.googleClientSecret || ""}
                              onChange={(e) => handleInputChange('googleClientSecret', e.target.value)}
                              placeholder="Google Client Secret"
                            />
                          </div>
                          <div>
                            <Label htmlFor="googleRedirectionUrl">Google Redirection URL</Label>
                            <Input
                              id="googleRedirectionUrl"
                              value={formData.googleRedirectionUrl || ""}
                              onChange={(e) => handleInputChange('googleRedirectionUrl', e.target.value)}
                              placeholder="Google Redirection URL"
                            />
                          </div>
                      </div>

                       <div className="flex justify-end">
                          <button
                            onClick={handleSubmit}
                            className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
                            disabled={loading}
                          >
                            {loading ? "Saving..." : "Save Settings"}
                          </button>
                        </div>
                  </TabsContent>
  )};
  export default GoogleAnalytics;
