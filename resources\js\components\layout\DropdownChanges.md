# Changes Made to Sidebar.jsx

## Overview
- Added a dropdown menu titled "Property Requires" that includes "Property Amenities" and "Property Types".

## File: `resources/js/components/layout/Sidebar.jsx`

## Changes:
1. **Line 423-424**: Removed the existing mapping of `propertyManagementNavigation` to allow for the dropdown implementation.
2. **Line 426-455**: Added a new dropdown section for "Property Requires" with toggle functionality.
3. **Line 457-471**: Filtered the `propertyManagementNavigation` to include only "Property Amenities" and "Property Types" under the dropdown.
4. **Line 473-490**: Mapped the remaining `propertyManagementNavigation` items excluding "Property Amenities" and "Property Types".
