import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Eye, Edit2, Trash2, MoreHorizontal, FileText } from "lucide-react";

const InvoiceTable = ({ 
  invoices, 
  loading, 
  onEdit, 
  onDelete, 
  onView, 
  searchTerm, 
  statusFilter, 
  paymentStatuses 
}) => {
  const filteredInvoices = invoices.filter((invoice) => {
    const matchesSearch =
      invoice.invoice_number?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.tenant?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.tenant?.first_name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.tenant?.last_name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus =
      statusFilter === "all"
        ? true
        : invoice.payment_status_id?.toString() === statusFilter;

    return matchesSearch && matchesStatus;
  });

  if (loading) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
          <p className="text-gray-500">Loading invoices...</p>
        </CardContent>
      </Card>
    );
  }

  if (filteredInvoices.length === 0) {
    return (
      <Card>
        <CardContent className="text-center py-12">
          <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No invoices found</h3>
          <p className="text-gray-500">Try adjusting your search or create a new invoice.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full border border-gray-200 rounded-lg">
        <thead className="bg-gray-100">
          <tr>
            <th className="text-left p-3 border-b">Invoice #</th>
            <th className="text-left p-3 border-b">Property</th>
            <th className="text-left p-3 border-b">Unit</th>
            <th className="text-left p-3 border-b">Price</th>
            <th className="text-left p-3 border-b">Tenant / Customer</th>
            <th className="text-left p-3 border-b">Date</th>
            <th className="text-left p-3 border-b">Status</th>
            <th className="text-center p-3 border-b">Actions</th>
          </tr>
        </thead>
        <tbody>
          {filteredInvoices.map((invoice) => (
            <tr key={invoice.id} className="hover:bg-gray-50 !pb-[100px]">
              <td className="p-3 border-b font-semibold">{invoice.invoice_number}</td>
              <td className="p-3 border-b text-sm text-gray-700">
                {invoice.property?.title || "-"}
              </td>
              <td className="p-3 border-b text-sm text-gray-700">
                {invoice.property_items.map((item) => (
                  <div key={item.id}>
                    {item.unit?.unit_number || "-"}
                  </div>
                ))}
              </td>
              <td className="p-3 border-b text-sm text-gray-500">
                ${invoice.grand_total}
              </td>
              <td className="p-3 border-b text-sm text-gray-700">
                {invoice.tenant?.first_name && invoice.tenant?.last_name
                  ? `${invoice.tenant.first_name} ${invoice.tenant.last_name}`
                  : invoice.tenant?.name ||
                    (invoice.customer?.name && invoice.customer.name) ||
                    "No Tenants or Customer"}
              </td>
              <td className="p-3 border-b text-sm text-gray-500">
                {new Date(invoice.invoice_date).toLocaleDateString("en-GB", {
                  day: "2-digit",
                  month: "short",
                  year: "numeric",
                })}
              </td>
              <td className="p-3 border-b">
                <Badge
                  className={
                    invoice.status === "paid"
                      ? "bg-green-500"
                      : invoice.status === "unpaid"
                      ? "bg-yellow-500"
                      : "bg-red-500"
                  }
                >
                  {invoice.paymentStatus?.name || "-"}
                </Badge>
              </td>
              <td className="p-3 border-b text-center">
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuLabel>Actions</DropdownMenuLabel>
                    <DropdownMenuItem onClick={() => onView(invoice)}>
                      <Eye className="mr-2 h-4 w-4" />
                      View
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={() => onEdit(invoice)}>
                      <Edit2 className="mr-2 h-4 w-4" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem
                      onClick={() => onDelete(invoice)}
                      className="text-red-600"
                    >
                      <Trash2 className="mr-2 h-4 w-4" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default InvoiceTable;
