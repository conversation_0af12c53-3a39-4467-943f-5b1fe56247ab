import React, { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

import { MoreHorizontal, Edit2, Trash2, Plus, X } from "lucide-react";
import menuPositionAPI from "../../services/menuPosition";

export default function Menu_Position() {
  const [menuPositions, setMenuPositions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [isAddModalOpen, setIsAddModalOpen] = useState(false);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  
  const itemsPerPage = 15;
  const [selectedItem, setSelectedItem] = useState({
    name: "",
    slug: ""
  });

  const fetchMenuPositions = async () => {
    try {
      setLoading(true);
      const response = await menuPositionAPI.getAll();
      setMenuPositions(response.data || []);
    } catch (error) {
      console.error("Error fetching menu positions:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMenuPositions();
  }, []);

  // Filter by search term
  const filteredItems = menuPositions.filter(
    (item) =>
      item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.slug?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalPages = Math.ceil(filteredItems.length / itemsPerPage);
  const paginatedItems = filteredItems.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const handlePageChange = (page) => {
    if (page < 1 || page > totalPages) return;
    setCurrentPage(page);
  };

  // handle submit
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validation
    if (!selectedItem.name) {
      alert("Name is required!");
      return;
    }

    try {
      const payload = {
        ...selectedItem,
      };

      if (selectedItem.id) {
        // Update existing menu position
        await menuPositionAPI.update(selectedItem.id, payload);
        setIsEditModalOpen(false);
      } else {
        // Create new menu position
        await menuPositionAPI.create(payload);
        setIsAddModalOpen(false);

        // Reset form after adding
        setSelectedItem({
          name: "",
          slug: "",
        });
      }

      // Refresh table
      fetchMenuPositions();
    } catch (error) {
      console.error(
        selectedItem.id ? "Error updating menu position:" : "Error creating menu position:",
        error
      );
    }
  };

  // handle delete
  const handleDelete = async (item) => {
    if (window.confirm(`Delete ${item.name}?`)) {
      try {
        await menuPositionAPI.delete(item.id);
        fetchMenuPositions();
      } catch (error) {
        console.error("Error deleting menu position:", error);
      }
    }
  };

  return (
    <>
      <style>
        {`
          .overflow-auto {
            overflow: unset !important;
          }
        `}
      </style>
      <Card className="mt-6">
      <CardContent className="space-y-4">
        {/* Header + Search + Add Button */}
        <div className="flex justify-between items-center">
          <Input
            placeholder="Search menu positions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-64"
          />
          <Button onClick={() => setIsAddModalOpen(true)}>
            <Plus className="h-4 w-4 mr-2" /> Add Menu Position
          </Button>
        </div>

        {/* Table */}
        <div className="overflow-x-auto">
          <table className="w-full border-collapse table-auto min-w-full">
          <thead>
            <tr className="bg-muted">
              <th className="p-4 text-left">Name</th>
              <th className="p-4 text-left">Slug</th>
              <th className="p-4 text-left">Created At</th>
              <th className="p-4 text-right">Actions</th>
            </tr>
          </thead>
          <tbody>
            {loading ? (
              <tr>
                <td colSpan="4" className="p-4 text-center">
                  Loading...
                </td>
              </tr>
            ) : paginatedItems.length === 0 ? (
              <tr>
                <td colSpan="4" className="p-4 text-center text-muted-foreground">
                  No menu positions found
                </td>
              </tr>
            ) : (
              paginatedItems.map((item) => (
                <tr key={item.id} className="border-b hover:bg-muted/50">
                  <td className="p-4 font-medium">{item.name}</td>
                  <td className="p-4 text-muted-foreground">{item.slug || '-'}</td>
                  <td className="p-4 text-muted-foreground">
                    {new Date(item.created_at).toLocaleDateString()}
                  </td>
                  <td className="p-4 text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="icon">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuLabel>Actions</DropdownMenuLabel>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem
                          onClick={() => {
                            setSelectedItem(item);
                            setIsEditModalOpen(true);
                          }}
                        >
                          <Edit2 className="mr-2 h-4 w-4" /> Edit
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          onClick={() => handleDelete(item)}
                          className="text-red-600"
                        >
                          <Trash2 className="mr-2 h-4 w-4" /> Delete
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
        </div>

        {/* Pagination */}
        {totalPages > 1 && (
          <div className="flex justify-center gap-2 mt-4">
            <Button onClick={() => handlePageChange(currentPage - 1)} disabled={currentPage === 1}>
              Previous
            </Button>
            <span className="flex items-center px-3">
              {currentPage} of {totalPages}
            </span>
            <Button onClick={() => handlePageChange(currentPage + 1)} disabled={currentPage === totalPages}>
              Next
            </Button>
          </div>
        )}
      </CardContent>
    </Card>

    {/* Add Modal */}
    {isAddModalOpen && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-96 max-w-md mx-4">
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Add Menu Position</h3>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsAddModalOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Name *</label>
                <Input
                  type="text"
                  value={selectedItem.name}
                  onChange={(e) => setSelectedItem({ ...selectedItem, name: e.target.value })}
                  placeholder="Enter menu position name"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Slug</label>
                <Input
                  type="text"
                  value={selectedItem.slug}
                  onChange={(e) => setSelectedItem({ ...selectedItem, slug: e.target.value })}
                  placeholder="Enter slug (optional)"
                />
              </div>
              <div className="flex gap-2 justify-end">
                <Button type="button" variant="outline" onClick={() => setIsAddModalOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Add Menu Position</Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    )}

    {/* Edit Modal */}
    {isEditModalOpen && (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <Card className="w-96 max-w-md mx-4">
          <CardContent className="p-6">
            <div className="flex justify-between items-center mb-4">
              <h3 className="text-lg font-semibold">Edit Menu Position</h3>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => setIsEditModalOpen(false)}
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Name *</label>
                <Input
                  type="text"
                  value={selectedItem.name}
                  onChange={(e) => setSelectedItem({ ...selectedItem, name: e.target.value })}
                  placeholder="Enter menu position name"
                  required
                />
              </div>
              <div>
                <label className="block text-sm font-medium mb-2">Slug</label>
                <Input
                  type="text"
                  value={selectedItem.slug}
                  onChange={(e) => setSelectedItem({ ...selectedItem, slug: e.target.value })}
                  placeholder="Enter slug (optional)"
                />
              </div>
              <div className="flex gap-2 justify-end">
                <Button type="button" variant="outline" onClick={() => setIsEditModalOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Update Menu Position</Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    )}
    </>
  );
};
