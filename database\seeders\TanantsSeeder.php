<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class TanantsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('tanants')->insert([
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '01710000000',
                'status' => 'active',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '01720000000',
                'status' => 'active',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '01730000000',
                'status' => 'inactive',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'phone' => '01740000000',
                'status' => 'active',
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ],
        ]);
    }
}
