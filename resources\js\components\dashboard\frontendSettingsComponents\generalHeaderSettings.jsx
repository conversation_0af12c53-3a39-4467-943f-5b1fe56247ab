import React, { useState } from "react";
import { TabsContent } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import frontendSettingsAPI from "../../../services/frontendSettingsAPI.js";

const GeneralHeaderSettings = ({ 
  formData, 
  handleInputChange, 
  logoPreview, 
  handleSubmit, 
  handleLogoFileChange,
  loading 
}) => {
  // Handle file selection and preview - now calls parent handler
  const onLogoFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      handleLogoFileChange(file);
    }
  };

  return (
    <div>
      <TabsContent value="general" className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {/* Logo Upload */}
          <div>
            <Label htmlFor="logo">Logo</Label>
            <div className="space-y-2">
              <Input
                id="logo"
                type="file"
                accept="image/*"
                onChange={onLogoFileChange}
              />
              {logoPreview && (
                <div className="mt-4">
                  <Label className="text-sm font-medium">Preview:</Label>
                  <div className="mt-2 border rounded-lg p-2 bg-gray-50">
                    <img
                      src={logoPreview}
                      alt="Logo preview"
                      className="max-w-full max-h-32 mx-auto rounded"
                    />
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Phone */}
          <div>
            <Label htmlFor="phone">Phone</Label>
            <Input
              id="phone"
              value={formData.phone || ""}
              onChange={(e) => handleInputChange("phone", e.target.value)}
              placeholder="+990-737 621 432"
            />
          </div>

          {/* Add Property Checkbox */}
          <div className="flex items-center space-x-2">
            <label htmlFor="addPropertyButton" className="text-sm font-medium text-gray-700">
              Add Property
            </label>
            <input
              type="checkbox"
              id="addPropertyButton"
              className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-indigo-600"
              checked={formData.addPropertyButton === 1}
              onChange={(e) =>
                handleInputChange("addPropertyButton", e.target.checked ? 1 : 0)
              }
            />
          </div>

          {/* Button URL */}
          <div>
            <Label htmlFor="btnUrl">Button Url</Label>
            <Input
              id="btnUrl"
              value={formData.btnUrl || ""}
              onChange={(e) => handleInputChange("btnUrl", e.target.value)}
              placeholder="/add-property"
            />
          </div>
        </div>
        
        {/* Show Preloader Checkbox */}
        <div className="flex items-center space-x-2">
          <label htmlFor="showPreloader" className="text-sm font-medium text-gray-700">
            Show Preloader
          </label>
          <input
            type="checkbox"
            id="showPreloader"
            className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-indigo-600"
            checked={formData.showPreloader === 1}
            onChange={(e) =>
              handleInputChange("showPreloader", e.target.checked ? 1 : 0)
            }
          />
        </div>


          {/* Show TopBar Checkbox */}
        <div className="flex items-center space-x-2">
          <label htmlFor="showTopBar" className="text-sm font-medium text-gray-700">
            Show TopBar
          </label>
          <input
            type="checkbox"
            id="showTopBar"
            className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-indigo-600"
            checked={formData.showTopBar === 1}
            onChange={(e) =>
              handleInputChange("showTopBar", e.target.checked ? 1 : 0)
            }
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end">
          <button
            onClick={handleSubmit}
            className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
            disabled={loading}
          >
            {loading ? "Saving..." : "Save Settings"}
          </button>
        </div>
      </TabsContent>
    </div>
  );
};

export default GeneralHeaderSettings;