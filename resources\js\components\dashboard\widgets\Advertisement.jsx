import React, { useState, useEffect } from "react";
import RichTextEditor from "../../ui/RichTextEditor";

const Advertisement = ({ widget, handleConfigUpdate, handleSubmit }) => {
  const [imagePreview, setImagePreview] = useState(widget.config?.image || "");

  useEffect(() => {
    setImagePreview(widget.config?.image || "");
  }, [widget.config?.image]);
  const handleImageChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // Create a preview for UI
      setImagePreview(URL.createObjectURL(file));
      // Upload file to server
      const formData = new FormData();
      formData.append('image', file);

      try {
        const response = await fetch('/api/page-contents/upload-image', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          handleConfigUpdate(widget.id, "image", data.data.url); // Store image URL
        } else {
          alert('Image upload failed: ' + data.message);
        }
      } catch (error) {
        alert('Error uploading image: ' + error.message);
      }
    }
  };
    return (

        <form
          onSubmit={handleSubmit}
          className="space-y-4"
        >
            <div className="flex gap-4">
                <div className="flex-1">
                    <label>
                     Title
                    </label>
                    <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                    value={widget.config?.title || ""}
                    onChange={(e) =>
                      handleConfigUpdate(widget.id, "title", e.target.value)
                    }
                    placeholder="Properties By Cities"
                    />
                 </div>
                 <div className="flex-1 gap-4">
                    <label>
                     Sub Title
                    </label>
                    <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                    value={widget.config?.subTitle || ""}
                    onChange={(e) =>
                      handleConfigUpdate(widget.id, "subTitle", e.target.value)
                    }
                    placeholder="Here are some of the featured Apartment in different categories"
                    />
                 </div>
                 <div className="flex-1 gap-4">
                    <label>
                     Button Text
                    </label>
                    <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                    value={widget.config?.buttonText || ""}
                    onChange={(e) =>
                      handleConfigUpdate(widget.id, "buttonText", e.target.value)
                    }
                    placeholder="Button Text"

                    />
                 </div>
                 <div className="flex-1 gap-4">
                    <label>
                     Button Link
                    </label>
                    <input
                    type="text"
                    className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                    value={widget.config?.buttonLink || ""}
                    onChange={(e) =>
                      handleConfigUpdate(widget.id, "buttonLink", e.target.value)
                    }
                    placeholder="Button Link"
                    />
                 </div>
            </div>
            <div>
                <label className="block text-sm font-medium text-gray-700">
                   Details
                </label>
                <RichTextEditor
                    value={widget.config?.details || ""}
                    onChange={(value) =>
                    handleConfigUpdate(widget.id, "details", value)
                    }
                    placeholder="Enter the Details..."
                    height="150px"
                />
              </div>

               <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700">
                   Image
                  </label>
                  <input
                    type="file"
                    accept="image/*"
                    className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                    onChange={handleImageChange}
                  />
                </div>
                {imagePreview && (
                  <div className="flex items-center">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-16 h-16 object-cover rounded-md border"
                    />
                  </div>
                )}
        </form>
    )};
export default Advertisement;
