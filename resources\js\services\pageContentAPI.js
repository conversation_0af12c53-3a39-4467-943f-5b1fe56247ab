import axios from 'axios';

const API_URL = '/api/page-contents';

// Create axios instance with default config
const api = axios.create({
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Page Content API functions
const pageContentAPI = {
  // Get all page contents with optional filters
  getAll: async (params = {}) => {
    try {
      const response = await api.get(API_URL, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching all page contents:', error);
      throw error.response?.data || error;
    }
  },

  // Get page contents by page ID
  getByPageId: async (pageId) => {
    try {
      const response = await api.get(`${API_URL}?page_id=${pageId}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching page contents:', error);
      throw error.response?.data || error;
    }
  },

  // Get single page content by ID
  getById: async (id) => {
    try {
      const response = await api.get(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching page content:', error);
      throw error.response?.data || error;
    }
  },

  // Create/Save page contents (replaces existing contents for the page)
  save: async (pageId, contents) => {
    try {
      const response = await api.post(API_URL, {
        page_id: pageId,
        contents: contents
      });
      return response.data;
    } catch (error) {
      console.error('Error saving page contents:', error);
      throw error.response?.data || error;
    }
  },

  // Create single page content
  create: async (data) => {
    try {
      const response = await api.post(`${API_URL}/single`, data);
      return response.data;
    } catch (error) {
      console.error('Error creating page content:', error);
      throw error.response?.data || error;
    }
  },

  // Create single page content with file upload
  createWithFiles: async (formData) => {
    try {
      const response = await api.post(`${API_URL}/single`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error creating page content with files:', error);
      throw error.response?.data || error;
    }
  },

  // Upload single image
  uploadImage: async (formData) => {
    try {
      const response = await api.post(`${API_URL}/upload-image`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error uploading image:', error);
      throw error.response?.data || error;
    }
  },

  // Update single page content
  update: async (id, data) => {
    try {
      const response = await api.put(`${API_URL}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating page content:', error);
      throw error.response?.data || error;
    }
  },

  // Delete single page content
  delete: async (id) => {
    try {
      const response = await api.delete(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting page content:', error);
      throw error.response?.data || error;
    }
  },

  // Delete all page contents for a specific page
  deleteByPageId: async (pageId) => {
    try {
      const response = await api.delete(`${API_URL}/page/${pageId}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting page contents:', error);
      throw error.response?.data || error;
    }
  },

  // Reorder page contents
  reorder: async (pageId, contentIds) => {
    try {
      const response = await api.post(`${API_URL}/reorder`, {
        page_id: pageId,
        content_ids: contentIds
      });
      return response.data;
    } catch (error) {
      console.error('Error reordering page contents:', error);
      throw error.response?.data || error;
    }
  },
};

export default pageContentAPI;
