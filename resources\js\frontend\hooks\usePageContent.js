import { useState, useEffect } from 'react';
import { fetchPageContent } from '../utils/api';

/**
 * Custom hook to fetch page content data
 * @param {number} pageId - The page ID to fetch content for
 * @param {number|null} widgetId - Optional widget ID to filter by
 * @returns {Object} { data, loading, error, refetch }
 */
export const usePageContent = (pageId, widgetId = null) => {
  const [data, setData] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await fetchPageContent(pageId, widgetId);
      
      if (result.success && result.data) {
        setData(result.data);
      } else {
        setData([]);
      }
    } catch (err) {
      console.error('Error fetching page content:', err);
      setError(err.message);
      setData([]);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (pageId) {
      fetchData();
    }
  }, [pageId, widgetId]);

  const refetch = () => {
    fetchData();
  };

  return { data, loading, error, refetch };
};

/**
 * Custom hook specifically for slider data
 * @returns {Object} { sliders, loading, error, refetch }
 */
export const useSliderData = () => {
  const { data, loading, error, refetch } = usePageContent(1, 1);
  
  const sliders = data.length > 0 && data[0] ? (() => {
    const sliderWidget = data.find(item => item.widget_id === 1);
    
    if (sliderWidget && sliderWidget.pageContent) {
      const config = typeof sliderWidget.pageContent === 'string' 
        ? JSON.parse(sliderWidget.pageContent) 
        : sliderWidget.pageContent;
        
      return config.sliders || [];
    }
    
    return [];
  })() : [];

  return { sliders, loading, error, refetch };
};