<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class UnitType extends Model
{
    //
    protected $fillable = [
        'name',
        'slug',
        'description',
        'is_active',
        'sort_order'
    ];

    
    // Automatically generate slug when creating/updating
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($unitType) {
            if (empty($unitType->slug)) {
                $unitType->slug = Str::slug($unitType->name);
            }
        });

        static::updating(function ($unitType) {
            if ($unitType->isDirty('name') && empty($unitType->slug)) {
                $unitType->slug = Str::slug($unitType->name);
            }
        });
    }

    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
}
