<?php

namespace App\Http\Controllers;

use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;

class CountryController extends Controller
{
    /**
     * Display a listing of the countries.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Country::query();

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('code', 'LIKE', "%{$search}%")
                      ->orWhere('iso_code', 'LIKE', "%{$search}%")
                      ->orWhere('capital', 'LIKE', "%{$search}%")
                      ->orWhere('continent', 'LIKE', "%{$search}%")
                      ->orWhere('currency', 'LIKE', "%{$search}%");
                });
            }

            // Filter by status
            if ($request->has('status') && $request->status) {
                $query->where('status', $request->status);
            }

            // Filter by continent
            if ($request->has('continent') && $request->continent) {
                $query->where('continent', $request->continent);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'name');
            $sortOrder = $request->get('sort_order', 'asc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 10);
            $countries = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $countries->items(),
                'pagination' => [
                    'current_page' => $countries->currentPage(),
                    'last_page' => $countries->lastPage(),
                    'per_page' => $countries->perPage(),
                    'total' => $countries->total()
                ],
                'message' => 'Countries retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving countries: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created country in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'name' => 'required|string|max:255|unique:countries,name',
                'code' => 'required|string|size:2|unique:countries,code',
                'iso_code' => 'required|string|size:3|unique:countries,iso_code',
                'capital' => 'nullable|string|max:255',
                'currency' => 'nullable|string|size:3',
                'phone_code' => 'nullable|string|max:10',
                'continent' => 'nullable|string|in:Asia,Europe,North America,South America,Africa,Australia,Antarctica',
                'population' => 'nullable|string|max:255',
                'status' => 'required|in:active,inactive'
            ]);

            // Convert codes to uppercase
            $validatedData['code'] = strtoupper($validatedData['code']);
            $validatedData['iso_code'] = strtoupper($validatedData['iso_code']);
            if (isset($validatedData['currency'])) {
                $validatedData['currency'] = strtoupper($validatedData['currency']);
            }

            $country = Country::create($validatedData);

            return response()->json([
                'success' => true,
                'data' => $country,
                'message' => 'Country created successfully'
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error creating country: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified country.
     */
    public function show(Country $country): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => $country,
                'message' => 'Country retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving country: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified country in storage.
     */
    public function update(Request $request, Country $country): JsonResponse
    {
        try {
            $validatedData = $request->validate([
                'name' => ['required', 'string', 'max:255', Rule::unique('countries')->ignore($country->id)],
                'code' => ['required', 'string', 'size:2', Rule::unique('countries')->ignore($country->id)],
                'iso_code' => ['required', 'string', 'size:3', Rule::unique('countries')->ignore($country->id)],
                'capital' => 'nullable|string|max:255',
                'currency' => 'nullable|string|size:3',
                'phone_code' => 'nullable|string|max:10',
                'continent' => 'nullable|string|in:Asia,Europe,North America,South America,Africa,Australia,Antarctica',
                'population' => 'nullable|string|max:255',
                'status' => 'required|in:active,inactive'
            ]);

            // Convert codes to uppercase
            $validatedData['code'] = strtoupper($validatedData['code']);
            $validatedData['iso_code'] = strtoupper($validatedData['iso_code']);
            if (isset($validatedData['currency'])) {
                $validatedData['currency'] = strtoupper($validatedData['currency']);
            }

            $country->update($validatedData);

            return response()->json([
                'success' => true,
                'data' => $country->fresh(),
                'message' => 'Country updated successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating country: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified country from storage.
     */
    public function destroy(Country $country): JsonResponse
    {
        try {
            $countryName = $country->name;
            $country->delete();

            return response()->json([
                'success' => true,
                'message' => "Country '{$countryName}' deleted successfully"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting country: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get statistics for countries.
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $statistics = [
                'total_countries' => Country::count(),
                'active_countries' => Country::where('status', 'active')->count(),
                'inactive_countries' => Country::where('status', 'inactive')->count(),
                'continents' => Country::distinct()->pluck('continent')->filter()->values(),
                'countries_by_continent' => Country::select('continent', DB::raw('count(*) as count'))
                    ->whereNotNull('continent')
                    ->groupBy('continent')
                    ->get()
                    ->pluck('count', 'continent')
            ];

            return response()->json([
                'success' => true,
                'data' => $statistics,
                'message' => 'Statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get all continents.
     */
    public function getContinents(): JsonResponse
    {
        try {
            $continents = Country::distinct()
                ->whereNotNull('continent')
                ->pluck('continent')
                ->sort()
                ->values();

            return response()->json([
                'success' => true,
                'data' => $continents,
                'message' => 'Continents retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving continents: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle country status.
     */
    public function toggleStatus(Country $country): JsonResponse
    {
        try {
            $country->status = $country->status === 'active' ? 'inactive' : 'active';
            $country->save();

            return response()->json([
                'success' => true,
                'data' => $country,
                'message' => "Country status updated to {$country->status}"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating country status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get countries for dropdown (public access, no special permissions required)
     */
    public function dropdown(Request $request): JsonResponse
    {
        try {
            $query = Country::select('id', 'name', 'code', 'iso_code', 'capital', 'continent')
                ->where('status', 'active');

            // Search functionality for dropdown
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('code', 'LIKE', "%{$search}%")
                      ->orWhere('iso_code', 'LIKE', "%{$search}%")
                      ->orWhere('capital', 'LIKE', "%{$search}%");
                });
            }

            // Limit results for dropdown - increased to show all countries
            $limit = $request->get('limit', 300);
            $countries = $query->orderBy('name', 'asc')->limit($limit)->get();

            return response()->json([
                'success' => true,
                'data' => $countries,
                'total' => $countries->count(),
                'message' => 'Countries retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving countries: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Public search for countries (no authentication required)
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $query = Country::select('id', 'name', 'code', 'iso_code', 'capital', 'continent', 'currency')
                ->where('status', 'active');

            // Search functionality
            if ($request->has('q') && $request->q) {
                $search = $request->q;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('code', 'LIKE', "%{$search}%")
                      ->orWhere('iso_code', 'LIKE', "%{$search}%")
                      ->orWhere('capital', 'LIKE', "%{$search}%")
                      ->orWhere('continent', 'LIKE', "%{$search}%");
                });
            }

            // Pagination for search results
            $perPage = $request->get('per_page', 20);
            $countries = $query->orderBy('name', 'asc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $countries->items(),
                'pagination' => [
                    'current_page' => $countries->currentPage(),
                    'last_page' => $countries->lastPage(),
                    'per_page' => $countries->perPage(),
                    'total' => $countries->total()
                ],
                'message' => 'Countries search completed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error searching countries: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Simple public endpoint to get all countries (for troubleshooting)
     */
    public function all(Request $request): JsonResponse
    {
        try {
            $countries = Country::select('id', 'name', 'code', 'iso_code', 'status')
                ->where('status', 'active')
                ->orderBy('name', 'asc')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $countries,
                'total' => $countries->count(),
                'message' => 'All countries retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving countries: ' . $e->getMessage(),
                'debug' => [
                    'file' => $e->getFile(),
                    'line' => $e->getLine(),
                    'trace' => $e->getTraceAsString()
                ]
            ], 500);
        }
    }

    /**
     * Public index method for countries (no authentication required)
     */
    public function publicIndex(Request $request): JsonResponse
    {
        try {
            $query = Country::select('id', 'name', 'code', 'iso_code', 'continent', 'currency', 'status', 'created_at', 'updated_at')
                ->where('status', 'active'); // Only show active countries for public access

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('code', 'LIKE', "%{$search}%")
                      ->orWhere('iso_code', 'LIKE', "%{$search}%")
                      ->orWhere('continent', 'LIKE', "%{$search}%")
                      ->orWhere('currency', 'LIKE', "%{$search}%");
                });
            }

            // Filter by continent
            if ($request->has('continent') && $request->continent) {
                $query->where('continent', $request->continent);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'name');
            $sortOrder = $request->get('sort_order', 'asc');
            
            // Only allow sorting by safe columns
            $allowedSortColumns = ['name', 'code', 'iso_code', 'continent', 'currency', 'created_at', 'updated_at'];
            if (!in_array($sortBy, $allowedSortColumns)) {
                $sortBy = 'name';
            }
            
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $perPage = min($perPage, 100); // Limit max per page for public endpoint
            $countries = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $countries->items(),
                'pagination' => [
                    'current_page' => $countries->currentPage(),
                    'last_page' => $countries->lastPage(),
                    'per_page' => $countries->perPage(),
                    'total' => $countries->total()
                ],
                'message' => 'Countries retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving countries: ' . $e->getMessage()
            ], 500);
        }
    }
}
