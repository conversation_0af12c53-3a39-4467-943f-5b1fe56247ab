<?php

namespace App\Http\Controllers;

use App\Models\PageContent;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;

class PageContentController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $pageId = $request->query('page_id');
            $widgetId = $request->query('widget_id');
            $perPage = $request->query('per_page', 15);

            $query = PageContent::query();

            if ($pageId) {
                $query->where('page_id', $pageId);
            }

            if ($widgetId) {
                $query->where('widget_id', $widgetId);
            }

            // Add ordering by created_at or a custom order field if you have one
            $query->orderBy('created_at', 'asc');

            if ($request->has('paginate') && $request->paginate === 'false') {
                $contents = $query->get();
                return response()->json([
                    'success' => true,
                    'data' => $contents,
                    'message' => 'Page contents retrieved successfully'
                ]);
            }

            $contents = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $contents->items(),
                'pagination' => [
                    'current_page' => $contents->currentPage(),
                    'last_page' => $contents->lastPage(),
                    'per_page' => $contents->perPage(),
                    'total' => $contents->total()
                ],
                'message' => 'Page contents retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving page contents: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage (bulk save for page).
     */
   public function store(Request $request): JsonResponse
{
    try {
        $request->validate([
            'page_id' => 'required|integer',
            'contents' => 'required|array',
            'contents.*.widget_id' => 'required|integer',
            'contents.*.pageContent' => 'nullable|array',
        ]);

        $pageId = $request->page_id;

        $destinationPath = public_path('page-contents');
        if (!file_exists($destinationPath)) {
            mkdir($destinationPath, 0755, true);
        }

        DB::beginTransaction();

       
        $oldFiles = [];

        // Delete existing contents for this page
        PageContent::where('page_id', $pageId)->delete();

        $contents = [];

        foreach ($request->contents as $index => $content) {
            $pageContentData = $content['pageContent'] ?? [];

            // Handle file uploads in pageContent
            foreach ($pageContentData as $key => $value) {
                if ($value instanceof \Illuminate\Http\UploadedFile) {
                    $filename = time() . '_' . $value->getClientOriginalName();
                    $value->move($destinationPath, $filename); // move file
                    $pageContentData[$key] = $filename; // save filename in DB
                }

                // Handle nested arrays like sliders, items, images inside them
                if (is_array($value)) {
                    array_walk_recursive($value, function (&$v, $k) use ($destinationPath) {
                        if ($v instanceof \Illuminate\Http\UploadedFile) {
                            $filename = time() . '_' . $v->getClientOriginalName();
                            $v->move($destinationPath, $filename);
                            $v = $filename;
                        }
                    });
                }
            }

            $contents[] = [
                'page_id' => $pageId,
                'widget_id' => $content['widget_id'],
                'pageContent' => !empty($pageContentData) ? json_encode($pageContentData) : null,
                'order_index' => $index,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        // Insert in chunks
        if (!empty($contents)) {
            collect($contents)->chunk(100)->each(function ($chunk) {
                PageContent::insert($chunk->toArray());
            });
        }

        DB::commit();

       

        return response()->json([
            'success' => true,
            'message' => 'Page contents saved successfully'
        ]);

    } catch (\Exception $e) {
      
        return response()->json([
            'success' => false,
            'message' => 'Error saving page contents: ' . $e->getMessage()
        ], 500);
    }
}


    /**
     * Display the specified resource.
     */
    public function show(PageContent $pageContent): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => $pageContent,
                'message' => 'Page content retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving page content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PageContent $pageContent): JsonResponse
    {
      
        
            $request->validate([
                'page_id' => 'sometimes|integer',
                'widget_id' => 'sometimes|integer',
                'pageContent' => 'nullable|array',
                'order_index' => 'nullable|integer',
            ]);

            $pageContent->update($request->only([
                'page_id', 'widget_id', 'pageContent', 'order_index'
            ]));

            return response()->json([
                'success' => true,
                'data' => $pageContent->fresh(),
                'message' => 'Page content updated successfully'
            ]);

       
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PageContent $pageContent): JsonResponse
    {
        try {
            $pageContent->delete();

            return response()->json([
                'success' => true,
                'message' => 'Page content deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting page content: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete all page contents for a specific page.
     */
    public function destroyByPage(int $pageId): JsonResponse
    {
        try {
            $deletedCount = PageContent::where('page_id', $pageId)->delete();

            return response()->json([
                'success' => true,
                'message' => "Deleted {$deletedCount} page content(s) successfully"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting page contents: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload a single image for page content.
     */
    public function uploadSingleImage(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'image' => 'required|image|mimes:jpeg,png,jpg,gif,svg|max:2048', // 2MB max
            ]);

            if ($request->hasFile('image')) {
                $file = $request->file('image');
                $fileName = time() . '_' . uniqid() . '.' . $file->getClientOriginalExtension();
                $destinationPath = public_path('page-contents/images');

                // Ensure directory exists
                if (!file_exists($destinationPath)) {
                    mkdir($destinationPath, 0755, true);
                }

                // Get file size before moving
                $fileSize = $file->getSize();

                $file->move($destinationPath, $fileName);
                $url = asset('page-contents/images/' . $fileName);

                return response()->json([
                    'success' => true,
                    'data' => [
                        'url' => $url,
                        'name' => $file->getClientOriginalName(),
                        'size' => $fileSize,
                    ],
                    'message' => 'Image uploaded successfully'
                ]);
            }

            return response()->json([
                'success' => false,
                'message' => 'No image file provided'
            ], 400);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error uploading image: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Reorder page contents.
     */
    public function reorder(Request $request): JsonResponse
    {
        $startTime = microtime(true);

        try {
            $request->validate([
                'page_id' => 'required|integer',
                'content_ids' => 'required|array',
                'content_ids.*' => 'integer',
            ]);

            $pageId = $request->page_id;

            // Retry logic for database connection issues
            $maxRetries = 3;
            $retryCount = 0;

            while ($retryCount < $maxRetries) {
                try {
                    DB::beginTransaction();

                    foreach ($request->content_ids as $index => $contentId) {
                        PageContent::where('id', $contentId)
                            ->where('page_id', $pageId)
                            ->update(['order_index' => $index]);
                    }

                    DB::commit();

                    $duration = microtime(true) - $startTime;
                    \Log::info("PageContent reorder transaction completed in {$duration} seconds for page_id: {$pageId}");

                    return response()->json([
                        'success' => true,
                        'message' => 'Page contents reordered successfully'
                    ]);

                } catch (\PDOException $e) {
                    DB::rollback();

                    // Check if it's a "MySQL server has gone away" error
                    if ($e->getCode() == 'HY000' && strpos($e->getMessage(), 'MySQL server has gone away') !== false) {
                        $retryCount++;
                        if ($retryCount < $maxRetries) {
                            \Log::warning("MySQL connection lost during reorder, retrying... Attempt {$retryCount}");
                            sleep(1); // Wait 1 second before retry
                            DB::reconnect(); // Reconnect to database
                            continue;
                        }
                    }

                    // If not a connection error or max retries reached, rethrow
                    throw $e;
                }
            }

        } catch (\Exception $e) {
            $duration = microtime(true) - $startTime;
            \Log::error("PageContent reorder failed after {$duration} seconds: " . $e->getMessage());

            return response()->json([
                'success' => false,
                'message' => 'Error reordering page contents: ' . $e->getMessage()
            ], 500);
        }
    }
}
