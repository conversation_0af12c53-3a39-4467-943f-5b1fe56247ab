import React from 'react';
import { Button } from "@/components/ui/button";
import { Plus } from "lucide-react";

const MenuItemsList = ({
  availableMenus = [],
  selectedMenus = [],
  onMenuSelection,
  onSelectAllMenus,
  onDeselectAllMenus,
  onAddSelectedPages,
  selectedMenuPosition,
  loadingPages = false
}) => {
  const isSelectAll = selectedMenus.length === availableMenus.length && availableMenus.length > 0;

  const handleSelectAll = () => {
    console.log('Select All clicked, current isSelectAll:', isSelectAll);
    console.log('Available menus:', availableMenus.length, 'Selected menus:', selectedMenus.length);
    if (isSelectAll) {
      onDeselectAllMenus();
    } else {
      onSelectAllMenus();
    }
  };

  return (
    <div className="space-y-4">
      <div className="text-sm text-muted-foreground">
        Select menu items to add to your menu structure
      </div>

      {/* Select All Checkbox */}
      {availableMenus.length > 0 && (
        <div className="flex items-center space-x-3 p-3 border rounded-lg bg-muted/50">
          <input
            type="checkbox"
            id="select-all"
            checked={isSelectAll}
            onChange={handleSelectAll}
            className="h-4 w-4 text-black"
          />
          <label
            htmlFor="select-all"
            className="flex-1 cursor-pointer font-medium"
          >
            Select All ({availableMenus.length} items)
          </label>
        </div>
      )}
      
      <div className="space-y-2 max-h-60 overflow-y-auto">
        {loadingPages ? (
          <div className="text-center py-4 text-muted-foreground">
            Loading menus...
          </div>
        ) : availableMenus.length === 0 ? (
          <div className="text-center py-4 text-muted-foreground">
            No menus found
          </div>
        ) : (
          availableMenus.map((page) => (
            <div
              key={page.id}
              className="flex items-center space-x-3 p-3 border rounded-lg hover:bg-muted/50"
            >
              <input
                type="checkbox"
                id={`page-${page.id}`}
                checked={selectedMenus.includes(page.id)}
                onChange={(e) => {
                  console.log('Checkbox clicked for page:', page.id, 'checked:', e.target.checked);
                  onMenuSelection(page.id);
                }}
                className="h-4 w-4 text-black"
              />
              <label
                htmlFor={`page-${page.id}`}
                className="flex-1 cursor-pointer"
              >
                <div className="font-medium">{page.name}</div>
                <div className="text-sm text-muted-foreground">
                  Position: {page.position || 'N/A'}
                </div>
              </label>
              <span className={`text-xs px-2 py-1 rounded-full ${
                page.is_active == 1
                  ? 'bg-green-100 text-green-800'
                  : 'bg-red-100 text-red-800'
              }`}>
                {page.is_active == 1 ? 'active' : 'inactive'}
              </span>
            </div>
          ))
        )}
      </div>
      
      <Button
        onClick={onAddSelectedPages}
        disabled={selectedMenus.length === 0 || loadingPages || !selectedMenuPosition}
        size="sm"
        className="ml-auto"
      >
        <Plus className="h-4 w-4 mr-2" />
        Add Selected Pages ({selectedMenus.length})
      </Button>
    </div>
  );
};

export default MenuItemsList;