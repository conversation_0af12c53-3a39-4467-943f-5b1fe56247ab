
import React, { useEffect, useState } from 'react';
import ReactD<PERSON> from 'react-dom/client';
import { BrowserRouter, Routes, Route, useLocation } from 'react-router-dom';
import Header from './components/Header';
import Home from './pages/home';


const App = () => {
  

    return (
        <BrowserRouter>
            <Header/>
            <Routes>
                <Route
                path="/"
                element={<Home />}
                />
            </Routes>
        </BrowserRouter>
    );
};

const root = ReactDOM.createRoot(document.getElementById('react-root'));
root.render(<App />);

