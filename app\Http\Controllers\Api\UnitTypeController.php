<?php


namespace App\Http\Controllers\Api;
use App\Models\UnitType;
use Illuminate\Support\Str;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class UnitTypeController extends Controller
{
    /**  
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        //
        try{
             $query=UnitType::query();
           
             // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhere('description', 'LIKE', "%{$search}%")
                      ->orWhere('icon', 'LIKE', "%{$search}%")
                      ->orWhere('color', 'LIKE', "%{$search}%");
                });
            }
            //sorting 
            $sortBy = $request->get('sort_by', 'sort_order');
            $sortDirection = $request->get('sort_direction', 'asc');
            if (in_array($sortBy, ['name', 'description', 'icon', 'color', 'sort_order', 'created_at'])) {
                $query->orderBy($sortBy, $sortDirection);
            }

            // Pagination
            $perPage = $request->get('per_page', 15);
            $unitTypes = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $unitTypes,
                'message' => 'Unit types retrieved successfully'
            ]);

        }catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve unit types',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        try {
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:unit_types,name',
                'description' => 'nullable|string',
                'icon' => 'nullable|string|max:255',
                'color' => 'nullable|string|max:7',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            // The slug will be automatically generated by the model's boot method
            $unitType = UnitType::create($validated);
            return response()->json([
                'success' => true,
                'data' => $unitType,
                'message' => 'Unit type created successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create unit type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id):JsonResponse
    {
        //
        try{
            $unitType = UnitType::findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $unitType,
                'message' => 'Unit type retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve Unit type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id):JsonResponse
    {
        //
        try{
            $unitType = UnitType::findOrFail($id);
            $validated = $request->validate([
                'name' => 'required|string|max:255|unique:unit_types,name,' . $id,
                'description' => 'nullable|string',
                'icon' => 'nullable|string|max:255',
                'color' => 'nullable|string|max:7',
                'is_active' => 'boolean',
                'sort_order' => 'integer|min:0'
            ]);
            $unitType->update($validated);
            return response()->json([
                'success' => true,
                'data' => $unitType->fresh(),
                'message' => 'Unit type updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update Unit type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id):JsonResponse
    {
        //
        try{
            $unitType = UnitType::findOrFail($id);
            $unitType->delete();
            return response()->json([
                'success' => true,
                'message' => 'Unit type deleted successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete Unit type',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function toggleStatus(string $id):JsonResponse
    {
        try{    
            $unitType = UnitType::findOrFail($id);
            $unitType->is_active = !$unitType->is_active;
            $unitType->save();
            return response()->json([
                'success' => true,
                'data' => $unitType,
                'message' => 'Unit type status updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update Unit type status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function getDropdown():JsonResponse
    {
        try{
            $unitTypes = UnitType::where('is_active', true)
                ->orderBy('sort_order')
                ->orderBy('name')
                ->select('id', 'name', 'slug', 'icon', 'color')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $unitTypes,
                'message' => 'Unit types for dropdown retrieved successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve Unit types for dropdown',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function bulkStatusUpdate(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'ids' => 'required|array',
                'ids.*' => 'integer|exists:unit_types,id',
                'is_active' => 'required|boolean'
            ]);

            $updated = UnitType::whereIn('id', $validated['ids'])
                ->update(['is_active' => $validated['is_active']]);

            return response()->json([
                'success' => true,
                'message' => "Successfully updated {$updated} unit type(s)",
                'updated_count' => $updated
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update unit types status',
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function reorder(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'items' => 'required|array',
                'items.*.id' => 'required|integer|exists:unit_types,id',
                'items.*.sort_order' => 'required|integer|min:0'
            ]);

            foreach ($validated['items'] as $item) {
                UnitType::where('id', $item['id'])
                    ->update(['sort_order' => $item['sort_order']]);
            }

            return response()->json([
                'success' => true,
                'message' => 'Unit types reordered successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to reorder unit types',
                'error' => $e->getMessage()
            ], 500);
        }
    }
}
