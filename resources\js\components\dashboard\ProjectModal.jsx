import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Checkbox } from '@/components/ui/checkbox';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import RichTextEditor from '../ui/CompatibleRichTextEditor';
import { showAlert } from '../../utils/sweetAlert';
import { Loader2, Plus, Trash2, Upload, Building2, X, Video, FileText as ImageIcon, MapPin } from 'lucide-react';
import projectAPI from '@/services/projectAPI';
import manageTypesAPI from '../../services/manageTypesAPI';
import { countryAPI } from '../../services/countryAPI';
import { stateAPI } from '../../services/stateAPI';
import { locationAPI } from '../../services/locationAPI';
const ProjectModal = ({ isOpen, onClose, project, mode, onSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [loadingStages, setLoadingStages] = useState(false);
  const [activeTab, setActiveTab] = useState('property');
  // Form state
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    property_type_id: '',
    property_status_id: '',
    property_stage_id: '',
    location: '',
    address: '',
    location_id: '',
    state_id: '',
    country_id: '',
    postal_code: '',
    latitude: '',
    longitude: '',
    area_sqft: '',
    price_per_area_sqft: '',
    total_price: '',
    bedrooms: '',
    bathrooms: '',
    floors: '',
    parking_spaces: '',
    year_built: '',
    amenities: [],
    features: [],
    is_featured: false,
    is_available: true
  });

  // Dropdown data
  const [countries, setCountries] = useState([]);
  const [states, setStates] = useState([]);
  const [locations, setLocations] = useState([]);
  const [propertyTypes, setPropertyTypes] = useState([]);
  const [propertyStatuses, setPropertyStatuses] = useState([]);
  const [propertyStageOptions, setPropertyStageOptions] = useState([]);
  const [propertyServices, setPropertyServices] = useState([]);
  const [unitTypes, setUnitTypes] = useState([]);
  const [rentTypes, setRentTypes] = useState([]);
  const [leaseTypes, setLeaseTypes] = useState([]);

  // Units, images, videos, documents
  const [units, setUnits] = useState([]);
  const [images, setImages] = useState([]);
  const [videos, setVideos] = useState([]);
  const [documents, setDocuments] = useState([]);
  const [imageFiles, setImageFiles] = useState([]);
  const [videoFiles, setVideoFiles] = useState([]);
  const [documentFiles, setDocumentFiles] = useState([]);
  const [modifyPriceStates, setModifyPriceStates] = useState({});

  // Helper functions
  const isValidImage = (image) => {
    if (!image) return false;
    // Check for various image source properties
    return !!(
      image.image_path || 
      image.path || 
      image.url || 
      image.file_path || 
      image.src ||
      image.image_url ||
      image.full_path ||
      (image instanceof File)
    );
  };

  const getImageSrc = (image) => {
    if (!image) return '';
    
    // First, try to use the image_url attribute provided by the backend
    if (image.image_url) {
      return image.image_url;
    }
    
    // Fallback to manual path construction for backward compatibility
    if (image.image_path) {
      // Handle both relative and absolute paths
      return image.image_path.startsWith('http') ? image.image_path : `/${image.image_path}`;
    }
    if (image.path) {
      return image.path.startsWith('http') ? image.path : `/${image.path}`;
    }
    if (image.url) return image.url;
    if (image.file_path) {
      return image.file_path.startsWith('http') ? image.file_path : `/${image.file_path}`;
    }
    if (image.src) return image.src;
    if (image.full_path) return image.full_path;
    
    // If it's a File object, create object URL
    if (image instanceof File) {
      try {
        return URL.createObjectURL(image);
      } catch (error) {
        console.error('Error creating object URL:', error);
        return '';
      }
    }
    
    return '';
  };

  const safeCreateObjectURL = (file) => {
    if (!file || !(file instanceof File)) return '';
    try {
      return URL.createObjectURL(file);
    } catch (error) {
      console.error('Error creating object URL:', error);
      return '';
    }
  };

  

  // Unit helper functions
  const shouldShowPriceField = (service, type) => {
    return service === type;
  };

  const isPriceFieldDisabled = (index, type, isReadOnly) => {
    if (isReadOnly) return true;
    const key = `${index}_${type}`;
    return !modifyPriceStates[key];
  };

  const toggleModifyPrice = (index, type) => {
    const key = `${index}_${type}`;
    setModifyPriceStates(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  // Handle body scroll lock when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    // Cleanup on unmount
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isOpen]);

  // Ensure images are properly initialized when modal opens
  useEffect(() => {
    if (isOpen && project && mode !== 'create') {
      // Re-validate existing images when modal opens
      const validImages = (project.images || []).filter(image => {
        const hasValidSource = isValidImage(image);
        if (!hasValidSource) {
          console.warn('Invalid image found:', image);
        }
        return hasValidSource;
      });
      
      if (validImages.length !== (project.images || []).length) {
        console.log(`Filtered ${(project.images || []).length - validImages.length} invalid images`);
        setImages(validImages);
      }
    }
  }, [isOpen, project, mode]);

  // Initialize form data when project changes
  useEffect(() => {
    if (project && (mode === 'edit' || mode === 'view')) {
      setFormData({
        title: project.title || '',
        description: project.description || '',
        property_type_id: project.property_type_id || '',
        property_status_id: project.property_status_id || '',
        property_stage_id: project.property_stage_id ? project.property_stage_id.toString() : '',
        location: project.location || '',
        address: project.address || '',
        location_id: project.location_id ? project.location_id.toString() : '',
        state_id: project.state_id ? project.state_id.toString() : '',
        country_id: project.country_id ? project.country_id.toString() : '',
        postal_code: project.postal_code || '',
        latitude: project.latitude || '',
        longitude: project.longitude || '',
        area_sqft: project.area_sqft || '',
        price_per_area_sqft: project.price_per_area_sqft || '',
        total_price: project.total_price || '',
        bedrooms: project.bedrooms || '',
        bathrooms: project.bathrooms || '',
        floors: project.floors || '',
        parking_spaces: project.parking_spaces || '',
        year_built: project.year_built || '',
        amenities: project.amenities || [],
        features: project.features || [],
        is_featured: project.is_featured || false,
        is_available: project.is_available !== undefined ? project.is_available : true
      });
      setUnits(project.units || []);
      
      // Handle images more carefully - preserve existing images
      const existingImages = project.images || [];
      console.log('Loading existing images:', existingImages); // Debug log
      console.log('Project object:', project); // Debug log
      
      // Check each image for valid sources
      existingImages.forEach((img, index) => {
        console.log(`Image ${index}:`, {
          id: img.id,
          image_path: img.image_path,
          path: img.path,
          url: img.url,
          file_path: img.file_path,
          src: img.src,
          image_url: img.image_url,
          full_path: img.full_path,
          allProps: Object.keys(img)
        });
      });
      
      setImages(existingImages);
      
      setVideos(project.videos || []);
      setDocuments(project.documents || []);

      // Load dependent data
      if (project.country_id) {
        loadStatesForCountry(project.country_id);
      }
      if (project.state_id) {
        loadLocationsForState(project.state_id);
      }
    } else {
      // Reset form for create mode and initialize with one default unit
      setFormData({
        title: '',
        description: '',
        property_type_id: '',
        property_status_id: '',
        property_stage_id: '',
        location: '',
        address: '',
        location_id: '',
        state_id: '',
        country_id: '',
        postal_code: '',
        latitude: '',
        longitude: '',
        area_sqft: '',
        price_per_area_sqft: '',
        total_price: '',
        bedrooms: '',
        bathrooms: '',
        floors: '',
        parking_spaces: '',
        year_built: '',
        amenities: [],
        features: [],
        is_featured: false,
        is_available: true
      });
      
      // Initialize with one default unit for create mode
      const defaultUnit = {
        id: Date.now(),
        unit_number: '',
        unit_size: '',
        unit_type_id: '',
        propertyService: '',
        floor_number: '',
        area_sqft: '',
        bedrooms: '',
        bathrooms: '',
        rent_price: '',
        sell_price: '',
        lease_price: '',
        currency: 'USD',
        status: 'available',
        description: '',
        features: [],
        is_available: true
      };
      
      setUnits([defaultUnit]);
      setImages([]);
      setVideos([]);
      setDocuments([]);
      setImageFiles([]);
      setVideoFiles([]);
      setDocumentFiles([]);
      setStates([]);
      setLocations([]);
    }
  }, [project, mode]);

// Fetch property types
  const fetchPropertyTypes = async () => {
    try {
      const response = await manageTypesAPI.propertyTypeDropDown();
      // Assuming the API returns data in response.data
      setPropertyTypes(response.data);
    } catch (error) {
      console.error("Failed to fetch property types", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPropertyTypes();
  }, []);
// Property Status dropdown
  const fetchPropertyStatuses = async () => {
    try {
      const response = await manageTypesAPI.propertyStatusDropDown();
      setPropertyStatuses(response.data);
    } catch (error) {
      console.error("Failed to fetch property statuses", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchPropertyStatuses();
  }, []);

  // Property Stage dropdown
  const fetchPropertyStages = async () => {
    try {
      const response = await manageTypesAPI.propertyStageDropDown();
      setPropertyStageOptions(response.data);
    } catch (error) {
      console.error("Failed to fetch property stages", error);
    } finally {
      setLoadingStages(false);
    }
  };
  useEffect(() => {
    fetchPropertyStages();
  }, []);

  // Countries Dropdown
  const fetchCountries = async () => {
    try {
      const response = await countryAPI.getCountriesDropdown();
      setCountries(response.data);
    } catch (error) {
      console.error("Failed to fetch countries", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchCountries();
  }, []);


  // unit Type
  const fetchUnitTypes = async () => {
    try {
      const response = await manageTypesAPI.unitTypeDropDown();
      setUnitTypes(response.data);
    } catch (error) {
      console.error("Failed to fetch unit types", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchUnitTypes();
  }, []);

  // PropertyService dropdown
  const fetchPropertyServices = async () => {
    try {
      const response = await manageTypesAPI.propertyServiceDropDown();
      setPropertyServices(response.data);
    } catch (error) {
      console.error("Failed to fetch property services", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchPropertyServices();
  }, []);

  // rentType Dropdown
  const fetchRentTypes = async () => {
    try {
      const response = await manageTypesAPI.rentTypeDropDown();
      setRentTypes(response.data);
    } catch (error) {
      console.error("Failed to fetch rent types", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchRentTypes();
  }, []);

  // leaseType Dropdown
  const fetchLeaseTypes = async () => {
    try {
      const response = await manageTypesAPI.leaseTypeDropDown();
      setLeaseTypes(response.data);
    } catch (error) {
      console.error("Failed to fetch lease types", error);
    } finally {
      setLoading(false);
    }
  };
  useEffect(() => {
    fetchLeaseTypes();
  }, []);

  // Load dependent data
  const loadStatesForCountry = async (countryId) => {
    if (!countryId) return;
    try {
      const result = await stateAPI.getStatesByCountry(countryId, { per_page: 250 });
      setStates(result?.data || []);
    } catch (error) {
      console.error('Error fetching states:', error);
      setStates([]);
    }
  };

  const loadLocationsForState = async (stateId) => {
    if (!stateId) return;
    try {
      const result = await locationAPI.public.getLocationsByState(stateId, { per_page: 250 });
      setLocations(result?.data || []);
    } catch (error) {
      console.error('Error fetching locations:', error);
      setLocations([]);
    }
  };

  // Handle form input changes
  const handleInputChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  // Handle location dropdown change
  const handleLocationChange = (locationId) => {
    const selectedLocation = locations.find(loc => loc.id === parseInt(locationId));
    setFormData(prev => ({
      ...prev,
      location_id: locationId,
      location: selectedLocation ? selectedLocation.name : ''
    }));
  };

  // Auto-calculate total price
  useEffect(() => {
    const area = parseFloat(formData.area_sqft) || 0;
    const pricePerSqft = parseFloat(formData.price_per_area_sqft) || 0;

    if (area > 0 && pricePerSqft > 0) {
      const totalPrice = area * pricePerSqft;
      setFormData(prev => ({
        ...prev,
        total_price: totalPrice.toFixed(2)
      }));
    }
  }, [formData.area_sqft, formData.price_per_area_sqft]);

  // Auto-calculate unit prices based on unit_size and price_per_area_sqft
  useEffect(() => {
    const pricePerSqft = parseFloat(formData.price_per_area_sqft) || 0;

    if (pricePerSqft > 0) {
      setUnits(prevUnits =>
        prevUnits.map((unit, index) => {
          const unitSize = parseFloat(unit.unit_size) || 0;
          let updatedUnit = { ...unit };

          // Auto-calculate rent_price if modify checkbox is unchecked
          if (unitSize > 0 && !modifyPriceStates[`${index}_rent`]) {
            updatedUnit.rent_price = (unitSize * pricePerSqft).toFixed(2);
          }

          // Auto-calculate sell_price if modify checkbox is unchecked
          if (unitSize > 0 && !modifyPriceStates[`${index}_sell`]) {
            updatedUnit.sell_price = (unitSize * pricePerSqft).toFixed(2);
          }

          // Auto-calculate lease_price if modify checkbox is unchecked
          if (unitSize > 0 && !modifyPriceStates[`${index}_lease`]) {
            updatedUnit.lease_price = (unitSize * pricePerSqft).toFixed(2);
          }

          return updatedUnit;
        })
      );
    }
  }, [formData.price_per_area_sqft, units.map(unit => unit.unit_size).join(','), units.length, modifyPriceStates]);

  // Fetch states when country changes
  useEffect(() => {
    if (formData.country_id) {
      loadStatesForCountry(formData.country_id);
      setFormData(prev => ({ ...prev, state_id: '', location_id: '' }));
    } else {
      setStates([]);
      setFormData(prev => ({ ...prev, state_id: '', location_id: '' }));
    }
  }, [formData.country_id]);

  // Fetch locations when state changes
  useEffect(() => {
    if (formData.state_id) {
      loadLocationsForState(formData.state_id);
      setFormData(prev => ({ ...prev, location_id: '' }));
    } else {
      setLocations([]);
      setFormData(prev => ({ ...prev, location_id: '' }));
    }
  }, [formData.state_id]);

  // Unit management functions
  const addUnit = () => {
    const newUnit = {
      id: Date.now(),
      unit_number: '',
      unit_size: '',
      unit_type_id: '',
      propertyService: '',
      floor_number: '',
      area_sqft: '',
      bedrooms: '',
      bathrooms: '',
      rent_price: '',
      sell_price: '',
      lease_price: '',
      currency: 'USD',
      status: 'available',
      description: '',
      features: [],
      is_available: true
    };
    setUnits(prev => [...prev, newUnit]);
    // Switch to units tab if not already there
    if (activeTab !== 'units') {
      setActiveTab('units');
    }
  };

  const removeUnit = (index) => {
    setUnits(prev => prev.filter((_, i) => i !== index));
  };

  const updateUnit = (index, field, value) => {
    setUnits(prev => prev.map((unit, i) =>
      i === index ? { ...unit, [field]: value } : unit
    ));
  };

  // Image management functions
  const handleImageUpload = (e) => {
    const files = Array.from(e.target.files);
    const filesWithMetadata = files.map(file => ({
      file,
      is_featured: false
    }));
    setImageFiles(prev => [...prev, ...filesWithMetadata]);
  };

  const removeImageFile = (index) => {
    setImageFiles(prev => prev.filter((_, i) => i !== index));
  };

  const removeExistingImage = async (imageId) => {
    if (!project?.id || !imageId) {
      console.error('Project ID or Image ID missing');
      return;
    }

    try {
      // Use the projectAPI service for consistency
      const response = await projectAPI.deleteImage(project.id, imageId);
      
      if (response.success) {
        // Remove from local state only if server deletion was successful
        setImages(prev => prev.filter(image => image.id !== imageId));
        
        // Show success message
        await showAlert('success', 'Success', response.message || 'Image deleted successfully');
      } else {
        throw new Error(response.message || 'Failed to delete image');
      }
    } catch (error) {
      console.error('Error deleting image:', error);
      await showAlert('error', 'Error', error.response?.data?.message || error.message || 'Failed to delete image. Please try again.');
    }
  };

  // Featured image management functions
  const updateExistingImageFeatured = (imageId, isFeatured) => {
    setImages(prev => prev.map(image => ({
      ...image,
      is_featured: image.id === imageId ? isFeatured : false // Only one can be featured
    })));
  };

  const updateImageFileFeatured = (index, isFeatured) => {
    setImageFiles(prev => prev.map((item, i) => ({
      ...item,
      is_featured: i === index ? isFeatured : false // Only one can be featured
    })));
  };

  const getFeaturedImage = () => {
    // Check existing images first
    const featuredExisting = images.find(img => img.is_featured);
    if (featuredExisting) {
      return getImageSrc(featuredExisting);
    }

    // Check new image files
    const featuredNew = imageFiles.find(item => item.is_featured);
    if (featuredNew) {
      return safeCreateObjectURL(featuredNew.file);
    }

    // Default to first image if no featured image is set
    if (images.length > 0) {
      return getImageSrc(images[0]);
    }
    if (imageFiles.length > 0) {
      return safeCreateObjectURL(imageFiles[0].file || imageFiles[0]);
    }

    return null;
  };

  // Video management functions
  const addVideo = () => {
    setVideos(prev => [
      {
        id: Date.now(),
        video_type: 'youtube',
        title: '',
        description: '',
        youtube_url: '',
        video_url: '',
        is_featured: false
      },
      ...prev
    ]);
  };

  const removeVideo = (index) => {
    setVideos(prev => prev.filter((_, i) => i !== index));
  };

  const updateVideo = (index, field, value) => {
    setVideos(prev => prev.map((video, i) =>
      i === index ? { ...video, [field]: value } : video
    ));
  };

  const handleVideoFileUpload = (e) => {
    const files = Array.from(e.target.files);
    setVideoFiles(prev => [...prev, ...files]);
  };

  const removeVideoFile = (index) => {
    setVideoFiles(prev => prev.filter((_, i) => i !== index));
  };

  // Document management functions
  const addDocument = () => {
    setDocuments(prev => [
      { id: Date.now(), title: '', show_on_frontend: false, file: null },
      ...prev
    ]);
  };

  const removeDocument = (index) => {
    setDocuments(prev => prev.filter((_, i) => i !== index));
    setDocumentFiles(prev => prev.filter((_, i) => i !== index));
  };

  const updateDocument = (index, field, value) => {
    setDocuments(prev => prev.map((doc, i) =>
      i === index ? { ...doc, [field]: value } : doc
    ));
  };

  const handleDocumentFileUpload = (index, file) => {
    setDocumentFiles(prev => {
      const newFiles = [...prev];
      newFiles[index] = file;
      return newFiles;
    });
  };

  // Preview helper functions
  const getPropertyTypeName = (id) => {
    if (!id) return 'Property Type';
    const type = propertyTypes.find(pt => pt.id.toString() === id.toString());
    return type ? type.name : 'Property Type';
  };

  const getPropertyStatusName = (id) => {
    if (!id) return 'Property Status';
    const status = propertyStatuses.find(ps => ps.id.toString() === id.toString());
    return status ? status.name : 'Property Status';
  };

  const getPropertyStatusColor = (id) => {
    if (!id) return '#6b7280';
    const status = propertyStatuses.find(ps => ps.id.toString() === id.toString());
    return status ? status.color : '#6b7280';
  };

  const propertyStage = {};
  propertyStageOptions.forEach(stage => {
    propertyStage[stage.id.toString()] = stage.name;
  });

  // Submit form
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (mode === 'view') return;

    setLoading(true);
    try {
      const submitData = new FormData();

      // Add form data
      Object.keys(formData).forEach(key => {
        if (formData[key] !== null && formData[key] !== undefined && formData[key] !== '') {
          if (Array.isArray(formData[key])) {
            submitData.append(key, JSON.stringify(formData[key]));
          } else {
            submitData.append(key, formData[key].toString());
          }
        }
      });

      // Add units data
      if (units.length > 0) {
        submitData.append('units', JSON.stringify(units));
      }

      // Add image files with featured information
      imageFiles.forEach((item, index) => {
        const file = item.file || item; // Handle both structures
        submitData.append(`images[${index}]`, file);
        submitData.append(`image_featured[${index}]`, item.is_featured ? 'true' : 'false');
      });

      // Add existing images featured status
      const existingImagesFeatured = images.map(img => ({
        id: img.id,
        is_featured: img.is_featured || false
      }));
      submitData.append('existing_images_featured', JSON.stringify(existingImagesFeatured));

      // Add video data
      if (videos.length > 0) {
        submitData.append('videos', JSON.stringify(videos));
      }

      // Add video files
      videoFiles.forEach((file, index) => {
        submitData.append(`video_files[${index}]`, file);
      });

      // Add documents
      if (documents.length > 0) {
        submitData.append('documents', JSON.stringify(documents));
      }

      // Add document files
      documentFiles.forEach((file, index) => {
        submitData.append(`document_files[${index}]`, file);
      });

      // Add method spoofing for Laravel PUT requests in edit mode
      if (mode === 'edit') {
        submitData.append('_method', 'PUT');
      }

      let response;
      if (mode === 'create') {
        response = await projectAPI.create(submitData);
      } else {
        response = await projectAPI.update(project.id, submitData);
      }

      await showAlert('success', 'Success', `Project ${mode === 'create' ? 'created' : 'updated'} successfully!`);
      onSuccess();
    } catch (error) {
      console.error('Error saving project:', error);
      await showAlert('error', 'Error', error.response?.data?.message || `Failed to ${mode} project`);
    } finally {
      setLoading(false);
    }
  };

  const isReadOnly = mode === 'view';

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col overflow-hidden">
        {/* Modal Header */}
        <div className="bg-gradient-to-r from-black to-gray-900 px-6 py-4 flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            <Building2 className="h-6 w-6" />
            <div>
              <h3 className="text-lg font-semibold">
                {mode === 'create' ? 'Add New Property' :
                 mode === 'edit' ? 'Edit Property' : 'View Property'}
              </h3>
              <p className="text-gray-100 text-sm">
                {mode === 'create' ? 'Create a new property listing' :
                 mode === 'edit' ? 'Update property information' : 'View property details'}
              </p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-white hover:text-gray-200 transition-colors p-1 rounded-full hover:bg-white/10"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Modal Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <form onSubmit={handleSubmit} className="flex-1 flex flex-col overflow-hidden">
            <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1 flex flex-col overflow-hidden">
              {/* Tab Header */}
              <div className="flex-shrink-0 sticky top-0 z-10 bg-white border-b border-gray-200 px-6 py-4">
                <TabsList className="grid w-full grid-cols-5">
                  <TabsTrigger
                    value="property"
                    className="data-[state=active]:bg-black data-[state=active]:text-white data-[state=active]:border-black transition-colors"
                  >
                    Property
                  </TabsTrigger>
                  <TabsTrigger
                    value="units"
                    className="data-[state=active]:bg-black data-[state=active]:text-white data-[state=active]:border-black transition-colors"
                  >
                    Units
                  </TabsTrigger>
                  <TabsTrigger
                    value="images"
                    className="data-[state=active]:bg-black data-[state=active]:text-white data-[state=active]:border-black transition-colors"
                  >
                    Images
                  </TabsTrigger>
                  <TabsTrigger
                    value="videos"
                    className="data-[state=active]:bg-black data-[state=active]:text-white data-[state=active]:border-black transition-colors"
                  >
                    Videos
                  </TabsTrigger>
                  <TabsTrigger
                    value="documents"
                    className="data-[state=active]:bg-black data-[state=active]:text-white data-[state=active]:border-black transition-colors"
                  >
                    Documents
                  </TabsTrigger>
                </TabsList>
              </div>

              {/* Tab Content */}
              <div className="flex-1 overflow-y-auto px-6 py-4 min-h-0">

            {/* Property Tab */}
            <TabsContent value="property" className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Property Title *</Label>
                  <Input
                    id="title"
                    value={formData.title}
                    onChange={(e) => handleInputChange('title', e.target.value)}
                    placeholder="Enter property title"
                    required
                    disabled={isReadOnly}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="property_type_id">Property Type *</Label>
                  <Select 
                    value={formData.property_type_id} 
                    onValueChange={(value) => handleInputChange('property_type_id', value)}
                    disabled={isReadOnly}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select property type" />
                    </SelectTrigger>
                    <SelectContent>
                      {propertyTypes.map((propertyType) => (
                        <SelectItem key={propertyType.id} value={propertyType.id.toString()}>
                          {propertyType.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="property_status_id">Property Status *</Label>
                  <Select 
                    value={formData.property_status_id} 
                    onValueChange={(value) => handleInputChange('property_status_id', value)}
                    disabled={isReadOnly}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select property status" />
                    </SelectTrigger>
                    <SelectContent>
                      {propertyStatuses.map((propertyStatus) => (
                        <SelectItem key={propertyStatus.id} value={propertyStatus.id.toString()}>
                          <div className="flex items-center gap-2">
                            <div 
                              className="w-3 h-3 rounded-full" 
                              style={{ backgroundColor: propertyStatus.color }}
                            ></div>
                            {propertyStatus.name}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="property_stage_id">Property Stage *</Label>
                  <Select 
                    value={formData.property_stage_id} 
                    onValueChange={(value) => handleInputChange('property_stage_id', value)}
                    disabled={isReadOnly}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select property stage" />
                    </SelectTrigger>
                   <SelectContent>
                      {propertyStageOptions.length === 0 && !loadingStages && (
                        <div className="px-4 py-2 text-gray-500">No Stages found</div>
                      )}
                      {propertyStageOptions.map((stage) => (
                        <SelectItem key={stage.id} value={stage.id.toString()}>
                          {stage.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

               
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <RichTextEditor
                  value={formData.description}
                  onChange={(value) => handleInputChange('description', value)}
                  placeholder="Enter property description"
                  disabled={isReadOnly}
                  height="200px"
                />
                </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="country_id">Country</Label>
                    <Select
                    value={formData.country_id}
                    onValueChange={(value) => {
                 
                      handleInputChange('country_id', value);
                    }}
                    disabled={isReadOnly}
                    >
                    <SelectTrigger>
                        <SelectValue placeholder="Select country" />
                    </SelectTrigger>
                    <SelectContent>
                        <SelectItem value="NoCountrySelected">No country selected</SelectItem>
                        {countries.map(country => (
                        <SelectItem key={country.id} value={country.id.toString()}>
                            {country.name}
                        </SelectItem>
                        ))}
                    </SelectContent>
                    </Select>
                   
                </div>

                <div className="space-y-2">
                    <Label htmlFor="state_id">State/Province *</Label>
                    <Select
                      value={formData.state_id}
                      onValueChange={(value) => handleInputChange('state_id', value)}
                      disabled={isReadOnly || !formData.country_id || formData.country_id === 'NoCountrySelected'}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder={
                          !formData.country_id || formData.country_id === 'NoCountrySelected' 
                            ? "Please select a country first" 
                            : "Select state/province"
                        } />
                      </SelectTrigger>
                      <SelectContent>
                        {states.map(state => (
                          <SelectItem key={state.id} value={state.id.toString()}>
                            {state.name}
                          </SelectItem>
                        ))}
                        {states.length === 0 && formData.country_id && formData.country_id !== 'NoCountrySelected' && (
                          <SelectItem value="Not Selected" disabled>No states available</SelectItem>
                        )}
                      </SelectContent>
                    </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="location_id">Location *</Label>
                  <Select
                    value={formData.location_id}
                    onValueChange={handleLocationChange}
                    disabled={isReadOnly || !formData.state_id || formData.state_id === 'NoStateSelected'}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder={
                        !formData.state_id || formData.state_id === 'NoStateSelected' 
                          ? "Please select a state first" 
                          : "Select location"
                      } />
                    </SelectTrigger>
                    <SelectContent>
                      {locations.map(location => (
                        <SelectItem key={location.id} value={location.id.toString()}>
                          {location.name}
                        </SelectItem>
                      ))}
                      {locations.length === 0 && formData.state_id && formData.state_id !== 'NoStateSelected' && (
                        <SelectItem value="Not Selected" disabled>No locations available</SelectItem>
                      )}
                    </SelectContent>
                  </Select>
                  {/* Debug info for development */}
                 
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-1 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="address">Address *</Label>
                  <RichTextEditor
                    id="address"
                    value={formData.address}
                    onChange={(value) => handleInputChange('address', value)}
                    placeholder="Enter full address"
                    rows={2}
                    required
                    disabled={isReadOnly}
                  />
                </div>
              
               
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="area_sqft">Area (sq ft)</Label>
                  <Input
                    id="area_sqft"
                    type="number"
                    value={formData.area_sqft}
                    onChange={(e) => handleInputChange('area_sqft', e.target.value)}
                    placeholder="Area"
                    disabled={isReadOnly}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="price_per_area_sqft">Price (sq ft)</Label>
                  <Input
                    id="price_per_area_sqft"
                    type="number"
                    value={formData.price_per_area_sqft}
                    onChange={(e) => handleInputChange('price_per_area_sqft', e.target.value)}
                    placeholder="Price Per Square Feet"
                    disabled={isReadOnly}
                  />
                </div>
                 <div className="space-y-2">
                  <Label htmlFor="total_price">Total Price (Auto-calculated)</Label>
                  <Input
                    id="total_price"
                    type="number"
                    value={formData.total_price}
                    onChange={(e) => handleInputChange('total_price', e.target.value)}
                    placeholder="Total Price"
                    disabled={isReadOnly}
                    className="bg-gray-50 border-gray-200"
                  />
                 
                </div>
              </div>
            <h3>For frontend View</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                
                 <div className="space-y-2">
                  <Label htmlFor="floors">Floors</Label>
                  <Input
                    id="floors"
                    type="number"
                    value={formData.floors}
                    onChange={(e) => handleInputChange('floors', e.target.value)}
                    placeholder="Floors"
                    disabled={isReadOnly}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bedrooms">Bedrooms</Label>
                  <Input
                    id="bedrooms"
                    type="number"
                    value={formData.bedrooms}
                    onChange={(e) => handleInputChange('bedrooms', e.target.value)}
                    placeholder="Number of bedrooms"
                    disabled={isReadOnly}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="bathrooms">Bathrooms</Label>
                  <Input
                    id="bathrooms"
                    type="number"
                    value={formData.bathrooms}
                    onChange={(e) => handleInputChange('bathrooms', e.target.value)}
                    placeholder="Number of bathrooms"
                    disabled={isReadOnly}
                  />
                </div>
              </div>

              <div className="flex items-center space-x-4 pb-[10px]">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_featured"
                    checked={formData.is_featured}
                    onCheckedChange={(checked) => handleInputChange('is_featured', checked)}
                    disabled={isReadOnly}
                  />
                  <Label htmlFor="is_featured">Featured Property</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="is_available"
                    checked={formData.is_available}
                    onCheckedChange={(checked) => handleInputChange('is_available', checked)}
                    disabled={isReadOnly}
                  />
                  <Label htmlFor="is_available">Available</Label>
                </div>
              </div>
            </TabsContent>

            {/* Units Tab */}
            <TabsContent value="units" className="space-y-4">
              <div className="flex justify-between items-center">
                <div>
                  <h3 className="text-lg font-semibold">Unit Management</h3>
                  <p className="text-sm text-gray-600">Add and manage property units</p>
                </div>
                {!isReadOnly && (
                  <Button type="button" onClick={addUnit} variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Unit
                  </Button>
                )}
              </div>

              <div className="space-y-4">
                {units.map((unit, index) => (
                  <Card key={unit.id || index} className="border-gray-200 shadow-sm">
                    <CardHeader className="bg-gray-50 border-b pb-3">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-sm flex items-center">
                          <Building2 className="h-4 w-4 mr-2 text-black" />
                          Unit {index + 1}
                          {unit.unit_number && (
                            <Badge variant="outline" className="ml-2">
                              {unit.unit_number}
                            </Badge>
                          )}
                        </CardTitle>
                        {!isReadOnly && units.length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeUnit(index)}
                            className="text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4 p-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Unit Number *</Label>
                          <Input
                            value={unit.unit_number}
                            onChange={(e) => updateUnit(index, 'unit_number', e.target.value)}
                            placeholder="e.g., A-101"
                            disabled={isReadOnly}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Unit Type *</Label>
                          <Select
                            value={unit.unit_type_id || ''}
                            onValueChange={(value) => updateUnit(index, 'unit_type_id', value)}
                            disabled={isReadOnly}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select unit type" />
                            </SelectTrigger>
                            <SelectContent>
                              {unitTypes.map((unitType) => (
                                <SelectItem key={unitType.id} value={unitType.id.toString()}>
                                  {unitType.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>

                        <div className="space-y-2">
                          <Label>Unit size *</Label>
                          <Input
                            value={unit.unit_size}
                            onChange={(e) => updateUnit(index, 'unit_size', e.target.value)}
                            placeholder="e.g., 1200 sq ft"
                            disabled={isReadOnly}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Floor</Label>
                          <Input
                            type="number"
                            value={unit.floor_number}
                            onChange={(e) => updateUnit(index, 'floor_number', e.target.value)}
                            placeholder="Floor number"
                            disabled={isReadOnly}
                          />
                        </div>

                        <div className="space-y-2">
                          <Label>Property Service *</Label>
                          <Select
                            value={unit.propertyService}
                            onValueChange={(value) => updateUnit(index, 'propertyService', value)}
                            disabled={isReadOnly}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select service type" />
                            </SelectTrigger>
                            <SelectContent>
                              {propertyServices.map((service) => (
                                <SelectItem key={service.id} value={service.name.toLowerCase()}>
                                  {service.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Rent Price Field - Show only when propertyService includes rent */}
                        {shouldShowPriceField(unit.propertyService, 'rent') && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label>Rent Price ($)</Label>
                              {unit.unit_size && formData.price_per_area_sqft && (
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`modify-rent-${index}`}
                                    checked={modifyPriceStates[`${index}_rent`] || false}
                                    onCheckedChange={() => toggleModifyPrice(index, 'rent')}
                                    disabled={isReadOnly}
                                  />
                                  <Label 
                                    htmlFor={`modify-rent-${index}`} 
                                    className="text-xs text-gray-600"
                                  >
                                    Modify Price
                                  </Label>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center space-x-2">
                              <Input
                                type="number"
                                step="0.01"
                                value={unit.rent_price}
                                onChange={(e) => updateUnit(index, 'rent_price', e.target.value)}
                                placeholder="0.00"
                                disabled={isPriceFieldDisabled(index, 'rent', isReadOnly)}
                                className={!modifyPriceStates[`${index}_rent`] && unit.rent_price ? 'bg-gray-50 border-gray-200' : ''}
                              />
                              <Select
                                value={unit.rent_type_id || ''}
                                onValueChange={(value) => updateUnit(index, 'rent_type_id', value)}
                                disabled={isReadOnly}
                                className="min-w-[120px]"
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select rent type" />
                                </SelectTrigger>
                                <SelectContent>
                                  {rentTypes && rentTypes.length > 0 ? (
                                    rentTypes.map(rt => (
                                      <SelectItem key={rt.id} value={rt.id.toString()}>
                                        {rt.name}
                                      </SelectItem>
                                    ))
                                  ) : (
                                    <SelectItem value="none" disabled>No rent types</SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        )}
                       
                        {/* Sell Price Field - Show only when propertyService includes sale */}
                        {shouldShowPriceField(unit.propertyService, 'sell') && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label>Sell Price ($)</Label>
                              {unit.unit_size && formData.price_per_area_sqft && (
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`modify-sell-${index}`}
                                    checked={modifyPriceStates[`${index}_sell`] || false}
                                    onCheckedChange={() => toggleModifyPrice(index, 'sell')}
                                    disabled={isReadOnly}
                                  />
                                  <Label 
                                    htmlFor={`modify-sell-${index}`} 
                                    className="text-xs text-gray-600"
                                  >
                                    Modify Price
                                  </Label>
                                </div>
                              )}
                            </div>
                            <Input
                              type="number"
                              step="0.01"
                              value={unit.sell_price}
                              onChange={(e) => updateUnit(index, 'sell_price', e.target.value)}
                              placeholder="0.00"
                              disabled={isPriceFieldDisabled(index, 'sell', isReadOnly)}
                              className={!modifyPriceStates[`${index}_sell`] && unit.sell_price ? 'bg-gray-50 border-gray-200' : ''}
                            />
                          </div>
                        )}

                        {/* Lease Price Field - Show only when propertyService includes lease */}
                        {shouldShowPriceField(unit.propertyService, 'lease') && (
                          <div className="space-y-2">
                            <div className="flex items-center justify-between">
                              <Label>Lease Price ($)</Label>
                              {unit.unit_size && formData.price_per_area_sqft && (
                                <div className="flex items-center space-x-2">
                                  <Checkbox
                                    id={`modify-lease-${index}`}
                                    checked={modifyPriceStates[`${index}_lease`] || false}
                                    onCheckedChange={() => toggleModifyPrice(index, 'lease')}
                                    disabled={isReadOnly}
                                  />
                                  <Label 
                                    htmlFor={`modify-lease-${index}`} 
                                    className="text-xs text-gray-600"
                                  >
                                    Modify Price
                                  </Label>
                                </div>
                              )}
                            </div>
                            <div className="flex items-center space-x-2">
                              <Input
                                type="number"
                                step="0.01"
                                value={unit.lease_price}
                                onChange={(e) => updateUnit(index, 'lease_price', e.target.value)}
                                placeholder="0.00"
                                disabled={isPriceFieldDisabled(index, 'lease', isReadOnly)}
                                className={!modifyPriceStates[`${index}_lease`] && unit.lease_price ? 'bg-gray-50 border-gray-200' : ''}
                              />
                              <Select
                                value={unit.lease_type_id || ''}
                                onValueChange={(value) => updateUnit(index, 'lease_type_id', value)}
                                disabled={isReadOnly}
                                className="min-w-[120px]"
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Lease type" />
                                </SelectTrigger>
                                <SelectContent>
                                  {leaseTypes && leaseTypes.length > 0 ? (
                                    leaseTypes.map(lt => (
                                      <SelectItem key={lt.id} value={lt.id.toString()}>
                                        {lt.name}
                                      </SelectItem>
                                    ))
                                  ) : (
                                    <SelectItem value="none" disabled>No lease types</SelectItem>
                                  )}
                                </SelectContent>
                              </Select>
                            </div>
                          </div>
                        )}

                        {/* Message when no propertyService is selected */}
                        {!unit.propertyService && (
                          <div className="col-span-3 text-center py-4 text-gray-500 bg-gray-50 rounded border-2 border-dashed border-gray-300">
                            Please select a Property Service to see relevant price fields
                          </div>
                        )}
                      </div>

                      <div className="space-y-2">
                        <Label>Description</Label>
                        <RichTextEditor
                          value={unit.description}
                          onChange={(value) => updateUnit(index, 'description', value)}
                          placeholder="Unit description"
                          disabled={isReadOnly}
                          height="120px"
                        />
                      </div>
                    </CardContent>
                  </Card>
                ))}

               
              </div>
            </TabsContent>

            {/* Images Tab */}
            <TabsContent value="images" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Property Images</h3>
                {!isReadOnly && (
                  <div className="space-x-2">
                    <input
                      type="file"
                      accept="image/*"
                      multiple
                      onChange={handleImageUpload}
                      className="hidden"
                      id="image-upload"
                    />
                    <Button 
                      type="button" 
                      variant="outline"
                      onClick={() => document.getElementById('image-upload').click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Images
                    </Button>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {/* Debug information */}
                {images.length === 0 && imageFiles.length === 0 && (
                  <div className="col-span-full">
                    <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg border-2 border-dashed">
                      <p>No images found</p>
                      <p className="text-xs mt-2">Images array length: {images.length}</p>
                      <p className="text-xs">Image files array length: {imageFiles.length}</p>
                    </div>
                  </div>
                )}

                {/* Existing images */}
                {images.map((image, index) => {
                  const imageSrc = getImageSrc(image);
                  console.log(`Image ${index}:`, { image, imageSrc }); // Debug log
                  
                  return (
                    <Card key={image.id || `existing-${index}`} className="border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="relative">
                          {imageSrc ? (
                            <img
                              src={imageSrc}
                              alt={image.title || image.alt_text || `Image ${index + 1}`}
                              className="w-full h-32 object-cover rounded"
                              onLoad={() => console.log(`Image loaded successfully: ${imageSrc}`)}
                              onError={(e) => {
                               
                               
                                e.target.alt = 'Image not found';
                              }}
                            />
                          ) : (
                            <div className="w-full h-32 bg-gray-100 rounded flex items-center justify-center">
                              <div className="text-center text-gray-500">
                                <p className="text-xs">No image source</p>
                                <p className="text-xs mt-1">ID: {image.id || 'No ID'}</p>
                              </div>
                            </div>
                          )}
                          
                          {!isReadOnly && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="absolute top-2 right-2 bg-white/80 hover:bg-white"
                              onClick={() => removeExistingImage(image.id)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {/* Featured badge */}
                          {image.is_featured && (
                            <div className="absolute top-2 left-2 bg-black text-white text-xs px-2 py-1 rounded">
                              Featured
                            </div>
                          )}
                        </div>
                        
                        {/* Featured checkbox */}
                        {!isReadOnly && (
                          <div className="mt-3 flex items-center space-x-2">
                            <Checkbox
                              id={`featured-${image.id}`}
                              checked={image.is_featured || false}
                              onCheckedChange={(checked) => updateExistingImageFeatured(image.id, checked)}
                              className="h-4 w-4 rounded border-2 border-gray-400 data-[state=checked]:bg-black data-[state=checked]:border-black data-[state=checked]:text-white focus:ring-2 focus:ring-black focus:ring-offset-2"
                            />
                            <Label htmlFor={`featured-${image.id}`} className="text-sm text-gray-700 cursor-pointer">
                              Set as featured image
                            </Label>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}

                {/* New image files */}
                {imageFiles.map((item, index) => {
                  // Handle both structures: new (with .file) and old (direct file)
                  const file = item.file || item;
                  const objectUrl = safeCreateObjectURL(file);
                  const isFeatured = item.is_featured || false;
                  
                  return (
                    <Card key={`new-${index}`} className="border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                      <CardContent className="p-4">
                        <div className="relative">
                          {objectUrl ? (
                            <img
                              src={objectUrl}
                              alt={`New image ${index + 1}`}
                              className="w-full h-32 object-cover rounded"
                              onError={(e) => {
                                console.error('New image failed to load:', file.name);
                                e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjEyOCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjZjNmNGY2Ii8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzljYTNhZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPkZpbGUgRXJyb3I8L3RleHQ+PC9zdmc+';
                              }}
                            />
                          ) : (
                            <div className="w-full h-32 bg-gray-100 rounded flex items-center justify-center">
                              <div className="text-center text-gray-500">
                                <p className="text-xs">Cannot create preview</p>
                                <p className="text-xs mt-1">{file.name}</p>
                              </div>
                            </div>
                          )}
                          
                          {!isReadOnly && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              className="absolute top-2 right-2"
                              onClick={() => removeImageFile(index)}
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          )}
                          
                          {/* Featured badge */}
                          {isFeatured && (
                            <div className="absolute top-2 left-2 bg-black text-white text-xs px-2 py-1 rounded">
                              Featured
                            </div>
                          )}
                        </div>
                        
                        <div className="mt-2 text-xs text-gray-500">
                          <p>File: {file.name}</p>
                          <p>Size: {(file.size / 1024 / 1024).toFixed(2)} MB</p>
                          <p>Type: {file.type}</p>
                        </div>
                        
                        {/* Featured checkbox */}
                        {!isReadOnly && (
                          <div className="mt-3 flex items-center space-x-2">
                            <Checkbox
                              id={`featured-new-${index}`}
                              checked={isFeatured}
                              onCheckedChange={(checked) => updateImageFileFeatured(index, checked)}
                              className="h-4 w-4 rounded border-2 border-gray-400 data-[state=checked]:bg-black data-[state=checked]:border-black data-[state=checked]:text-white focus:ring-2 focus:ring-black focus:ring-offset-2"
                            />
                            <Label htmlFor={`featured-new-${index}`} className="text-sm text-gray-700 cursor-pointer">
                              Set as featured image
                            </Label>
                          </div>
                        )}
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            </TabsContent>

            {/* Videos Tab */}
            <TabsContent value="videos" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Property Videos</h3>
                {!isReadOnly && (
                  <div className="space-x-2">
                    <Button type="button" onClick={addVideo} variant="outline">
                      <Plus className="h-4 w-4 mr-2" />
                      Add Video Link
                    </Button>
                    <input
                      type="file"
                      accept="video/*"
                      multiple
                      onChange={handleVideoFileUpload}
                      className="hidden"
                      id="video-upload"
                    />
                    <Button 
                      type="button" 
                      variant="outline"
                      onClick={() => document.getElementById('video-upload').click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      Upload Videos
                    </Button>
                  </div>
                )}
              </div>

              <div className="space-y-4">
                {/* Video links */}
                {videos.map((video, index) => (
                  <Card key={video.id || index} className="border-gray-200 shadow-sm">
                    <CardHeader className="bg-gray-50 border-b pb-3">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-sm flex items-center">
                          <Video className="h-4 w-4 mr-2 text-black" />
                          Video {index + 1}
                        </CardTitle>
                        {!isReadOnly && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeVideo(index)}
                            className="text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4 p-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <Label>Video Type</Label>
                          <Select 
                            value={video.video_type} 
                            onValueChange={(value) => updateVideo(index, 'video_type', value)}
                            disabled={isReadOnly}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="youtube">YouTube</SelectItem>
                              <SelectItem value="url">Video URL</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div className="space-y-2">
                          <Label>Title *</Label>
                          <Input
                            value={video.title}
                            onChange={(e) => updateVideo(index, 'title', e.target.value)}
                            placeholder="Video title"
                            disabled={isReadOnly}
                          />
                        </div>
                      </div>

                      {video.video_type === 'youtube' && (
                        <div className="space-y-2">
                          <Label>YouTube URL</Label>
                          <Input
                            value={video.youtube_url}
                            onChange={(e) => updateVideo(index, 'youtube_url', e.target.value)}
                            placeholder="https://www.youtube.com/watch?v=..."
                            disabled={isReadOnly}
                          />
                        </div>
                      )}

                      {video.video_type === 'url' && (
                        <div className="space-y-2">
                          <Label>Video URL</Label>
                          <Input
                            value={video.video_url}
                            onChange={(e) => updateVideo(index, 'video_url', e.target.value)}
                            placeholder="https://example.com/video.mp4"
                            disabled={isReadOnly}
                          />
                        </div>
                      )}

                      <div className="space-y-2">
                        <Label>Description</Label>
                        <RichTextEditor
                          value={video.description}
                          onChange={(value) => updateVideo(index, 'description', value)}
                          placeholder="Video description"
                          disabled={isReadOnly}
                          height="120px"
                        />
                      </div>
                    </CardContent>
                  </Card>
                ))}

                {/* Video files */}
                {videoFiles.map((file, index) => (
                  <Card key={`video-file-${index}`} className="border-gray-200 shadow-sm">
                    <CardContent className="p-6">
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-3">
                          <Video className="h-8 w-8 text-gray-400" />
                          <div>
                            <p className="text-sm font-medium">{file.name}</p>
                            <p className="text-xs text-gray-500">
                              {(file.size / 1024 / 1024).toFixed(2)} MB
                            </p>
                          </div>
                        </div>
                        {!isReadOnly && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeVideoFile(index)}
                            className="text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              {videos.length === 0 && videoFiles.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No videos added yet. Add video links or upload video files.
                </div>
              )}
            </TabsContent>

            {/* Documents Tab */}
            <TabsContent value="documents" className="space-y-4">
              <div className="flex justify-between items-center">
                <h3 className="text-lg font-semibold">Project Documents</h3>
                {!isReadOnly && (
                  <Button type="button" onClick={addDocument} variant="outline">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Document
                  </Button>
                )}
              </div>
              <div className="space-y-4">
                {documents.map((doc, index) => (
                  <Card key={doc.id || index} className="border-gray-200 shadow-sm">
                    <CardHeader className="bg-gray-50 border-b pb-3">
                      <div className="flex justify-between items-center">
                        <CardTitle className="text-sm flex items-center">
                          <ImageIcon className="h-4 w-4 mr-2 text-black" />
                          Document {index + 1}
                        </CardTitle>
                        {!isReadOnly && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeDocument(index)}
                            className="text-red-600 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </CardHeader>
                    <CardContent className="space-y-4 p-6">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Title *</Label>
                          <Input
                            value={doc.title}
                            onChange={e => updateDocument(index, 'title', e.target.value)}
                            placeholder="Document title"
                            disabled={isReadOnly}
                          />
                        </div>
                        <div className="space-y-2">
                          <Label>Document (Image or PDF)</Label>
                          <Input
                            type="file"
                            accept="image/*,application/pdf"
                            onChange={e => handleDocumentFileUpload(index, e.target.files[0])}
                            disabled={isReadOnly}
                          />
                          {doc.file && (
                            <span className="text-xs text-gray-500">{doc.file.name}</span>
                          )}
                        </div>
                        <div className="flex items-center space-x-2 mt-6">
                          <Checkbox
                            id={`show-on-frontend-${index}`}
                            checked={doc.show_on_frontend}
                            onCheckedChange={checked => updateDocument(index, 'show_on_frontend', checked)}
                            disabled={isReadOnly}
                          />
                          <Label htmlFor={`show-on-frontend-${index}`}>Show on frontend</Label>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
              {documents.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No documents added yet. Click "Add Document" to get started.
                </div>
              )}
            </TabsContent>
            <TabsContent value="preview" className="space-y-4">
              <div className="space-y-6">
                <Card>
                  {/* Featured Image - Show above the title */}
                  {getFeaturedImage() && (
                    <div className="relative">
                      <img
                        src={getFeaturedImage()}
                        alt="Featured Property Image"
                        className="w-full h-48 object-cover rounded-t-lg"
                        onError={(e) => {
                          console.error('Featured image failed to load');
                          e.target.style.display = 'none';
                        }}
                      />
                      <div className="absolute top-2 right-2 bg-black text-white text-xs px-2 py-1 rounded">
                        Featured
                      </div>
                    </div>
                  )}
                  
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Building2 className="h-5 w-5" />
                      {formData.title || 'Property Title'}
                    </CardTitle>
                    <div className="flex gap-2">
                      <Badge>{getPropertyTypeName(formData.property_type_id)}</Badge>
                      <Badge 
                        variant="outline" 
                        style={{ 
                          borderColor: getPropertyStatusColor(formData.property_status_id),
                          color: getPropertyStatusColor(formData.property_status_id)
                        }}
                      >
                        <div className="flex items-center gap-1">
                          <div 
                            className="w-2 h-2 rounded-full" 
                            style={{ backgroundColor: getPropertyStatusColor(formData.property_status_id) }}
                          ></div>
                          {getPropertyStatusName(formData.property_status_id)}
                        </div>
                      </Badge>
                       <Badge variant="outline">{propertyStage[formData.property_stage] || 'Property Stage'}</Badge>
                      {formData.is_featured && <Badge variant="secondary">Featured</Badge>}
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <p className="text-gray-700">{formData.description || 'No description provided'}</p>
                    <div className="flex items-center gap-2 text-sm text-gray-600">
                      <MapPin className="h-4 w-4" />
                      {formData.location || 'Location not specified'}
                    </div>
                  </CardContent>
                </Card>

                {units.length > 0 && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Units ({units.length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid gap-2">
                        {units.map((unit, index) => (
                          <div key={index} className="flex justify-between items-center p-2 border rounded">
                            <div>
                              <span className="font-medium">{unit.unit_number}</span>
                              <span className="text-sm text-gray-500 ml-2">
                                {unitTypes.find(ut => ut.id?.toString() === unit.unit_type_id?.toString())?.name || unit.unit_type_id}
                              </span>
                              <span className="text-sm text-gray-500 ml-2">{unit.propertyService}</span>
                              <span className="font-medium">{unit.unit_size}</span>
                            </div>
                            <div className="text-sm text-gray-600">
                              {unit.rent_price && `Rent: $${unit.rent_price}`}
                              {unit.sell_price && ` | Sell: $${unit.sell_price}`}
                              {unit.lease_price && ` | Lease: $${unit.lease_price}`}
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}

                {(images.length > 0 || imageFiles.length > 0) && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Images ({images.length + imageFiles.length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
                        {/* Show existing images first */}
                        {images.slice(0, 4).map((image, index) => {
                          const imageSrc = getImageSrc(image);
                          if (!imageSrc) return null;
                          
                          return (
                            <img
                              key={`existing-preview-${index}`}
                              src={imageSrc}
                              alt={`Preview ${index}`}
                              className="w-full h-20 object-cover rounded"
                              onError={(e) => {
                                console.error('Preview image failed to load:', imageSrc);
                                e.target.style.display = 'none';
                              }}
                            />
                          );
                        })}
                        
                        {/* Show new image files if we have space */}
                        {imageFiles.slice(0, Math.max(0, 4 - images.length)).map((file, index) => {
                          const objectUrl = safeCreateObjectURL(file);
                          if (!objectUrl) return null;
                          
                          return (
                            <img
                              key={`new-preview-${index}`}
                              src={objectUrl}
                              alt={`New Preview ${index}`}
                              className="w-full h-20 object-cover rounded"
                              onError={(e) => {
                                console.error('New preview image failed to load:', file.name);
                                e.target.style.display = 'none';
                              }}
                            />
                          );
                        })}
                      </div>
                      {(images.length + imageFiles.length) > 4 && (
                        <p className="text-xs text-gray-500 mt-2">
                          Showing first 4 of {images.length + imageFiles.length} images
                        </p>
                      )}
                    </CardContent>
                  </Card>
                )}

                {(videos.length > 0 || videoFiles.length > 0) && (
                  <Card>
                    <CardHeader>
                      <CardTitle>Videos ({videos.length + videoFiles.length})</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <div className="space-y-2">
                        {videos.map((video, index) => (
                          <div key={index} className="flex items-center gap-3 p-2 border rounded">
                            <Video className="h-5 w-5 text-gray-400" />
                            <div>
                              <p className="text-sm font-medium">{video.title}</p>
                              <p className="text-xs text-gray-500">
                                {video.video_type === 'youtube' ? 'YouTube' : 'Video URL'}
                              </p>
                            </div>
                          </div>
                        ))}
                        {videoFiles.map((file, index) => (
                          <div key={`file-${index}`} className="flex items-center gap-3 p-2 border rounded">
                            <Video className="h-5 w-5 text-gray-400" />
                            <div>
                              <p className="text-sm font-medium">{file.name}</p>
                              <p className="text-xs text-gray-500">
                                {(file.size / 1024 / 1024).toFixed(2)} MB
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                )}
              </div>
            </TabsContent>
            
            </div> {/* Close scrollable content div */}
            </Tabs>

            {/* Modal Actions - Fixed at bottom */}
            <div className="flex-shrink-0 flex justify-end space-x-2 p-6 pt-4 border-t bg-white">
              <Button 
                type="button" 
                variant="outline" 
                onClick={onClose}
                disabled={loading}
                className="hover:bg-gray-50 transition-colors"
              >
                {mode === 'view' ? 'Close' : 'Cancel'}
              </Button>
              {mode !== 'view' && (
                <Button 
                  type="submit" 
                  disabled={loading}
                  className="bg-black hover:bg-gray-900 transition-colors"
                >
                  {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  {mode === 'create' ? 'Create Property' : 'Update Property'}
                </Button>
              )}
            </div>
          </form>
        </div>
      </div>
    </div>
  );
};

export default ProjectModal;
