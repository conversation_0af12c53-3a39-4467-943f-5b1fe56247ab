<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\Role;

class AddPropertyTypePermissions extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'permissions:add-property-type';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Add property-type module permissions to roles';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        // Get all roles
        $roles = Role::all();

        if ($roles->isEmpty()) {
            $this->error('No roles found!');
            return 1;
        }

        foreach ($roles as $role) {
            $this->info("Processing role: {$role->name}");

            // Add property-type to accessible modules if not present
            $accessibleModules = $role->accessible_modules ?? [];
            if (!in_array('property-type', $accessibleModules)) {
                $accessibleModules[] = 'property-type';
                $role->accessible_modules = $accessibleModules;
                $this->info("Added 'property-type' to accessible modules for {$role->name}");
            }

            // Add property-type permissions if not present
            $modulePermissions = $role->module_permissions ?? [];
            if (!isset($modulePermissions['property-type'])) {
                $modulePermissions['property-type'] = ['read', 'create', 'update', 'delete'];
                $role->module_permissions = $modulePermissions;
                $this->info("Added 'property-type' permissions for {$role->name}");
            }

            // Save the role
            $role->save();
        }

        $this->info('Property type permissions added successfully!');
        return 0;
    }
}
