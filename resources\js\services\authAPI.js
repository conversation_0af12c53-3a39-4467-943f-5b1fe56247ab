// Authentication API service
import { API_BASE_URL } from '../config/api.js';

class AuthAPI {
  constructor() {
    this.token = localStorage.getItem('auth_token');
  }

  // Set token for authenticated requests
  setToken(token) {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  // Remove token
  removeToken() {
    this.token = null;
    localStorage.removeItem('auth_token');
  }

  // Get auth headers
  getAuthHeaders() {
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    return headers;
  }

  // Register new user
  async register(userData) {
    try {
  const response = await fetch(`${API_BASE_URL}/v1/auth/register`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(userData),
      });

      const result = await response.json();

      if (result.success && result.data.token) {
        this.setToken(result.data.token);
      }

      return result;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  // Login user
  async login(credentials) {
    try {
      // Always use the full API base URL for login
  const response = await fetch(`${API_BASE_URL}/v1/auth/login`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(credentials),
      });

      const result = await response.json();

      if (result.success && result.data.token) {
        this.setToken(result.data.token);
      }

      return result;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  // Logout user
  async logout() {
    try {
  const response = await fetch(`${API_BASE_URL}/v1/auth/logout`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
      });

      const result = await response.json();
      this.removeToken();
      return result;
    } catch (error) {
      console.error('Logout error:', error);
      this.removeToken(); // Remove token even if request fails
      throw error;
    }
  }

  // Logout from all devices
  async logoutAll() {
    try {
  const response = await fetch(`${API_BASE_URL}/v1/auth/logout-all`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
      });

      const result = await response.json();
      this.removeToken();
      return result;
    } catch (error) {
      console.error('Logout all error:', error);
      this.removeToken();
      throw error;
    }
  }

  // Get user profile
  async getProfile() {
    try {
  const response = await fetch(`${API_BASE_URL}/v1/auth/profile`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Get profile error:', error);
      throw error;
    }
  }

  // Update user profile
  async updateProfile(profileData) {
    try {
  const response = await fetch(`${API_BASE_URL}/v1/auth/profile`, {
        method: 'PUT',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(profileData),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Update profile error:', error);
      throw error;
    }
  }

  // Change password
  async changePassword(passwordData) {
    try {
  const response = await fetch(`${API_BASE_URL}/v1/auth/change-password`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
        body: JSON.stringify(passwordData),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Change password error:', error);
      throw error;
    }
  }

  // Get user permissions
  async getPermissions() {
    try {
  const response = await fetch(`${API_BASE_URL}/v1/auth/permissions`, {
        method: 'GET',
        headers: this.getAuthHeaders(),
      });

      const result = await response.json();
      return result;
    } catch (error) {
      console.error('Get permissions error:', error);
      throw error;
    }
  }

  // Refresh token
  async refreshToken() {
    try {
  const response = await fetch(`${API_BASE_URL}/v1/auth/refresh-token`, {
        method: 'POST',
        headers: this.getAuthHeaders(),
      });

      const result = await response.json();

      if (result.success && result.data.token) {
        this.setToken(result.data.token);
      }

      return result;
    } catch (error) {
      console.error('Refresh token error:', error);
      throw error;
    }
  }

  // Check if user is authenticated
  isAuthenticated() {
    return !!this.token;
  }

  // Get stored token
  getToken() {
    return this.token;
  }
}

// Create and export singleton instance
export const authAPI = new AuthAPI();
export default authAPI;
