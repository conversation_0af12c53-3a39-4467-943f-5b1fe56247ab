import React, { useState, useEffect } from 'react';

const HowItWorks = () => {
  const [howItWorksData, setHowItWorksData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchHowItWorksData = async () => {
      try {
        setLoading(true);
        console.log('Fetching How It Works data from database...');
        
        // Fetch page content data with page_id=1 and widget_id=9
        const response = await fetch('/api/frontend/page-contents?page_id=1&widget_id=9&paginate=false');
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        console.log('How It Works API Response:', result);
        
        if (result.success && result.data && result.data.length > 0) {
          // Find the widget with widget_id = 9 (how it works widget)
          const howItWorksWidget = result.data.find(item => item.widget_id === 9);
          
          if (howItWorksWidget && howItWorksWidget.pageContent) {
            const config = typeof howItWorksWidget.pageContent === 'string' 
              ? JSON.parse(howItWorksWidget.pageContent) 
              : howItWorksWidget.pageContent;
              
            console.log('Parsed How It Works config:', config);
            
            // Map the API response structure to our component structure
            const mappedHowItWorksData = {
              title: config.shoulder || "How Does It Work",
              subtitle: config.underText || "Here are some of the featured Apartment in different categories",
              videoUrl: config.buttonUrl || "https://www.youtube.com/watch?v=MLpWrANjFbI&ab_channel=eidelchteinadvogados",
              videoText: config.buttonName || "Watch video",
              steps: config.items ? config.items.map(item => ({
                title: item.title || "Step Title",
                description: item.text || "Step description goes here...",
                icon: item.image || "/Frontend/assets/img/home2/icon/loaction.svg"
              })) : []
            };
            
            setHowItWorksData(mappedHowItWorksData);
            console.log('How It Works data set from database:', mappedHowItWorksData);
          } else {
            console.log('No widget found with widget_id 9');
          }
        } else {
          console.log('No data found in API response');
        } 
      } catch (err) {
        console.error('Error fetching how it works data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchHowItWorksData();
  }, []);



  if (loading) {
    return (
      <div className="how-it-work-section mb-100">
        <div className="container">
          <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
            <div className="spinner-border" role="status">
              <span className="sr-only">Loading How It Works...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if there's an error or no data
  if (error || !howItWorksData) {
    return (
      <div className="how-it-work-section mb-100">
        <div className="container">
          <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
            <div className="text-center">
              <h3>Unable to load How It Works data</h3>
              <p className="text-muted">
                {error ? `Error: ${error}` : 'No data found in database'}
              </p>
              <p className="text-muted">Please check the database for page_id=1 and widget_id=9</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
  return (
    <>
      <div className="how-it-work-section mb-100">
        <div className="container">
          <div className="row mb-50 wow fadeInUp" data-wow-delay="200ms">
            <div className="col-lg-12 d-flex align-items-end justify-content-between gap-3 flex-wrap">
              <div className="section-title-2">
                <h2>{howItWorksData.title || "How Does It Work"}</h2>
                <p>{howItWorksData.subtitle || "Here are some of the featured Apartment in different categories"}</p>
              </div>
              <div className="video-btn">
                <a
                  data-fancybox="gallery"
                  href={howItWorksData.videoUrl || "https://www.youtube.com/watch?v=MLpWrANjFbI&ab_channel=eidelchteinadvogados"}
                >
                  <i className="bi bi-play-circle"></i> {howItWorksData.videoText || "Watch video"}
                </a>
              </div>
            </div>
          </div>

          <div className="row wow fadeInUp" data-wow-delay="300ms">
            <div className="col-lg-12">
              <div className="work-process-group">
                <div className="row justify-content-center g-lg-0 gy-5">
                  
                  {howItWorksData.steps && howItWorksData.steps.length > 0 && (
                    howItWorksData.steps.map((step, index) => (
                      <div key={index} className="col-lg-3 col-sm-6">
                        <div className="single-work-process text-center">
                          <div className="step">
                            <span>{String(index + 1).padStart(2, '0')}</span>
                          </div>
                          <div className="icon">
                            <img 
                              src={step.icon || "/Frontend/assets/img/home2/icon/loaction.svg"} 
                              alt={step.title || "Step"} 
                            />
                          </div>
                          <div className="content">
                            <h6>{step.title || "Step Title"}</h6>
                            <p>{step.description || "Step description goes here..."}</p>
                          </div>
                        </div>
                      </div>
                    ))
                  )}

                </div>
              </div>
            </div>
          </div>

          <div className="row wow fadeInUp" data-wow-delay="400ms">
            <div className="col-lg-12 d-flex justify-content-center">
              <div className="trustpilot-review">
                {howItWorksData.reviewText ? (
                  <div dangerouslySetInnerHTML={{ __html: howItWorksData.reviewText }} />
                ) : (
                  <>
                    <strong>Excellent!</strong>
                    <img src="/Frontend/assets/img/home1/icon/trustpilot-star2.svg" alt="" />
                    <p>
                      5.0 Rating out of <strong>5.0</strong> based on{" "}
                      <a href="#">
                        <strong>245354</strong> reviews
                      </a>
                    </p>
                  </>
                )}
                <img src="/Frontend/assets/img/home1/icon/trustpilot-star2.svg" alt="" />
                <img src="/Frontend/assets/img/home1/icon/trustpilot-logo.svg" alt="" />
              </div>
            </div>
          </div>

        </div>
      </div>
    </>
  );
};

export default HowItWorks;
