import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import { Search, MoreHorizontal, Edit, Trash2, Eye, Star, Plus, Filter, RefreshCw } from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import BlogModal from './BlogModal';
import blogAPI from '../../services/blogAPI';
import manageTypesAPI from '../../services/manageTypesAPI';

const Blog = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingBlog, setEditingBlog] = useState(null);
  const [blogs, setBlogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [categories, setCategories] = useState([]);
  const [statistics, setStatistics] = useState({});
  
  // Filters and pagination
  const [filters, setFilters] = useState({
    search: '',
    status: 'all',
    category_id: 'all',
    page: 1,
    per_page: 10
  });
  
  const [pagination, setPagination] = useState({
    current_page: 1,
    last_page: 1,
    total: 0,
    per_page: 10
  });

  // Selection for bulk operations
  const [selectedBlogs, setSelectedBlogs] = useState([]);
  const [selectAll, setSelectAll] = useState(false);
  
  // Delete confirmation
  const [deleteConfirm, setDeleteConfirm] = useState({
    open: false,
    blogId: null,
    blogTitle: ''
  });
  
  const [bulkDeleteConfirm, setBulkDeleteConfirm] = useState(false);

  useEffect(() => {
    fetchBlogs();
    fetchCategories();
    fetchStatistics();
  }, [filters]);

  const fetchBlogs = async () => {
    try {
      setLoading(true);
      setError(null);
      const apiFilters = {
        ...filters,
        category_id: filters.category_id === 'all' ? '' : filters.category_id,
        status: filters.status === 'all' ? '' : filters.status,
      };
      const response = await blogAPI.getBlogs(apiFilters);

      if (response.data.success) {
        setBlogs(response.data.data);
        setPagination(response.data.meta);
      } else {
        setError('Failed to fetch blogs');
      }
    } catch (err) {
      console.error('Error fetching blogs:', err);
      setError('Failed to fetch blogs');
    } finally {
      setLoading(false);
    }
  };

  const fetchCategories = async () => {
    try {
      const response = await manageTypesAPI.propertyTypeDropDown();
      if (response.success && Array.isArray(response.data)) {
        setCategories(response.data);
      }
    } catch (err) {
      console.error('Error fetching categories:', err);
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await blogAPI.getStatistics();
      if (response.data.success) {
        setStatistics(response.data.data);
      }
    } catch (err) {
      console.error('Error fetching statistics:', err);
    }
  };

  const handleCreateBlog = () => {
    setEditingBlog(null);
    setIsModalOpen(true);
  };

  const handleEditBlog = (blog) => {
    setEditingBlog(blog);
    setIsModalOpen(true);
  };

  const handleSaveBlog = async (blogData) => {
    // This function is called after the BlogModal has already saved the blog
    // We just need to refresh the data and close the modal
    try {
      fetchBlogs();
      fetchStatistics();
      setIsModalOpen(false);
      setEditingBlog(null);
    } catch (err) {
      console.error('Error refreshing blog data:', err);
    }
  };

  const handleDeleteBlog = async (blogId) => {
    try {
      const response = await blogAPI.deleteBlog(blogId);
      if (response.data.success) {
        fetchBlogs();
        fetchStatistics();
        setDeleteConfirm({ open: false, blogId: null, blogTitle: '' });
      }
    } catch (err) {
      console.error('Error deleting blog:', err);
    }
  };

  const handleBulkDelete = async () => {
    try {
      const response = await blogAPI.bulkDeleteBlogs(selectedBlogs);
      if (response.data.success) {
        fetchBlogs();
        fetchStatistics();
        setSelectedBlogs([]);
        setSelectAll(false);
        setBulkDeleteConfirm(false);
      }
    } catch (err) {
      console.error('Error bulk deleting blogs:', err);
    }
  };

  const handleStatusChange = async (blogId, newStatus) => {
    try {
      const response = await blogAPI.updateStatus(blogId, newStatus);
      if (response.data.success) {
        fetchBlogs();
        fetchStatistics();
      }
    } catch (err) {
      console.error('Error updating status:', err);
    }
  };

  const handleToggleFeatured = async (blogId) => {
    try {
      const response = await blogAPI.toggleFeatured(blogId);
      if (response.data.success) {
        fetchBlogs();
        fetchStatistics();
      }
    } catch (err) {
      console.error('Error toggling featured:', err);
    }
  };

  const handleFilterChange = (key, value) => {
    setFilters(prev => ({
      ...prev,
      [key]: value,
      page: 1 // Reset to first page when filtering
    }));
  };

  const handlePageChange = (newPage) => {
    setFilters(prev => ({ ...prev, page: newPage }));
  };

  const handleSelectBlog = (blogId) => {
    setSelectedBlogs(prev => 
      prev.includes(blogId) 
        ? prev.filter(id => id !== blogId)
        : [...prev, blogId]
    );
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedBlogs([]);
    } else {
      setSelectedBlogs(blogs.map(blog => blog.id));
    }
    setSelectAll(!selectAll);
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const getStatusBadge = (status) => {
    const variants = {
      published: 'default',
      draft: 'secondary',
      scheduled: 'outline'
    };
    return (
      <Badge variant={variants[status] || 'secondary'}>
        {status?.charAt(0)?.toUpperCase() + status?.slice(1)}
      </Badge>
    );
  };

  return (
    <div className="mx-auto p-6 space-y-6">
      {/* Header with Statistics */}
      <div className="space-y-6">
        {/* Header */}
        <div className="flex justify-between items-start">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Blog Management</h1>
            <p className="text-muted-foreground">Manage your blog posts and content</p>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchBlogs} disabled={loading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button onClick={handleCreateBlog}>
              <Plus className="h-4 w-4 mr-2" />
              Add Blog
            </Button>
          </div>
        </div>

        {/* Statistics - Full Width */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 w-full">
          <div className="bg-white p-4 rounded-lg border shadow-sm">
            <div className="text-2xl font-bold text-blue-600">{statistics.total || 0}</div>
            <div className="text-sm text-gray-600">Total Blogs</div>
          </div>
          <div className="bg-white p-4 rounded-lg border shadow-sm">
            <div className="text-2xl font-bold text-green-600">{statistics.published || 0}</div>
            <div className="text-sm text-gray-600">Published</div>
          </div>
          <div className="bg-white p-4 rounded-lg border shadow-sm">
            <div className="text-2xl font-bold text-yellow-600">{statistics.draft || 0}</div>
            <div className="text-sm text-gray-600">Drafts</div>
          </div>
          <div className="bg-white p-4 rounded-lg border shadow-sm">
            <div className="text-2xl font-bold text-purple-600">{statistics.featured || 0}</div>
            <div className="text-sm text-gray-600">Featured</div>
          </div>
        </div>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input 
                placeholder="Search blogs..." 
                className="pl-8" 
                value={filters.search}
                onChange={(e) => handleFilterChange('search', e.target.value)}
              />
            </div>
            
            <Select value={filters.category_id} onValueChange={(value) => handleFilterChange('category_id', value)}>
              <SelectTrigger className="w-48">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map(category => (
                  <SelectItem key={category.id} value={category.id.toString()}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Select value={filters.status} onValueChange={(value) => handleFilterChange('status', value)}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="published">Published</SelectItem>
                <SelectItem value="draft">Draft</SelectItem>
                <SelectItem value="scheduled">Scheduled</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Bulk Actions */}
          {selectedBlogs.length > 0 && (
            <div className="mt-4 p-3 bg-blue-50 rounded-lg flex items-center justify-between">
              <span className="text-sm text-blue-700">
                {selectedBlogs.length} blog(s) selected
              </span>
              <Button 
                variant="destructive" 
                size="sm"
                onClick={() => setBulkDeleteConfirm(true)}
              >
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Selected
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Blog List Table */}
      <Card>
        <CardContent className="p-0">
          {loading ? (
            <div className="flex justify-center items-center py-12">
              <RefreshCw className="h-8 w-8 animate-spin text-gray-400" />
              <span className="ml-2 text-gray-600">Loading blogs...</span>
            </div>
          ) : error ? (
            <div className="text-center py-12">
              <div className="text-red-600 mb-4">{error}</div>
              <Button onClick={fetchBlogs}>Try Again</Button>
            </div>
          ) : blogs.length === 0 ? (
            <div className="text-center py-12">
              <div className="text-gray-500 mb-4">No blogs found</div>
              <Button onClick={handleCreateBlog}>Create your first blog</Button>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b bg-gray-50">
                  <tr>
                    <th className="text-left p-4 font-medium">
                      <Checkbox
                        checked={selectAll}
                        onCheckedChange={handleSelectAll}
                      />
                    </th>
                    <th className="text-left p-4 font-medium">Title</th>
                    <th className="text-left p-4 font-medium">Author</th>
                    <th className="text-left p-4 font-medium">Category</th>
                    <th className="text-left p-4 font-medium">Status</th>
                    <th className="text-left p-4 font-medium">Views</th>
                    <th className="text-left p-4 font-medium">Published</th>
                    <th className="text-right p-4 font-medium">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {blogs.map((blog) => (
                    <tr key={blog.id} className="border-b hover:bg-muted/50">
                      <td className="p-4">
                        <Checkbox
                          checked={selectedBlogs.includes(blog.id)}
                          onCheckedChange={() => handleSelectBlog(blog.id)}
                        />
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-2">
                          <div>
                            <div className="font-medium">{blog.title}</div>
                            <div className="text-sm text-gray-500 line-clamp-1">
                              {blog.excerpt || 'No excerpt'}
                            </div>
                          </div>
                          {blog.is_featured && (
                            <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div>
                          <div className="font-medium">{blog.author_name}</div>
                          {blog.author_email && (
                            <div className="text-sm text-gray-500">{blog.author_email}</div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        {blog.category ? blog.category.name : 'No Category'}
                      </td>
                      <td className="p-4">
                        {getStatusBadge(blog.status)}
                      </td>
                      <td className="p-4">
                        <div className="flex items-center gap-1">
                          <Eye className="h-4 w-4 text-gray-400" />
                          {blog.views_count || 0}
                        </div>
                      </td>
                      <td className="p-4">
                        {blog.published_at ? formatDate(blog.published_at) : '-'}
                      </td>
                      <td className="p-4 text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="icon">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditBlog(blog)}>
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </DropdownMenuItem>
                            
                            <DropdownMenuSeparator />
                            
                            <DropdownMenuItem onClick={() => handleToggleFeatured(blog.id)}>
                              <Star className="h-4 w-4 mr-2" />
                              {blog.is_featured ? 'Remove Featured' : 'Make Featured'}
                            </DropdownMenuItem>
                            
                            {blog.status !== 'published' && (
                              <DropdownMenuItem onClick={() => handleStatusChange(blog.id, 'published')}>
                                <Eye className="h-4 w-4 mr-2" />
                                Publish
                              </DropdownMenuItem>
                            )}
                            
                            {blog.status !== 'draft' && (
                              <DropdownMenuItem onClick={() => handleStatusChange(blog.id, 'draft')}>
                                <Edit className="h-4 w-4 mr-2" />
                                Move to Draft
                              </DropdownMenuItem>
                            )}
                            
                            <DropdownMenuSeparator />
                            
                            <DropdownMenuItem 
                              className="text-destructive"
                              onClick={() => setDeleteConfirm({
                                open: true,
                                blogId: blog.id,
                                blogTitle: blog.title
                              })}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Pagination */}
      {pagination.last_page > 1 && (
        <div className="flex justify-center items-center gap-2">
          <Button 
            variant="outline" 
            size="sm" 
            disabled={pagination.current_page === 1}
            onClick={() => handlePageChange(pagination.current_page - 1)}
          >
            Previous
          </Button>
          
          <div className="flex items-center gap-1">
            {[...Array(Math.min(5, pagination.last_page))].map((_, index) => {
              const pageNumber = Math.max(1, pagination.current_page - 2) + index;
              if (pageNumber > pagination.last_page) return null;
              
              return (
                <Button
                  key={pageNumber}
                  variant={pagination.current_page === pageNumber ? "default" : "outline"}
                  size="sm"
                  onClick={() => handlePageChange(pageNumber)}
                >
                  {pageNumber}
                </Button>
              );
            })}
          </div>
          
          <Button 
            variant="outline" 
            size="sm" 
            disabled={pagination.current_page === pagination.last_page}
            onClick={() => handlePageChange(pagination.current_page + 1)}
          >
            Next
          </Button>
          
          <span className="text-sm text-muted-foreground ml-4">
            Page {pagination.current_page} of {pagination.last_page} 
            ({pagination.total} total)
          </span>
        </div>
      )}

      {/* Blog Modal */}
      <BlogModal 
        isOpen={isModalOpen} 
        onClose={() => {
          setIsModalOpen(false);
          setEditingBlog(null);
        }} 
        onSave={handleSaveBlog}
        editData={editingBlog}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteConfirm.open} onOpenChange={(open) => 
        setDeleteConfirm({ open, blogId: null, blogTitle: '' })
      }>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the blog "{deleteConfirm.blogTitle}". 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={() => handleDeleteBlog(deleteConfirm.blogId)}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>

      {/* Bulk Delete Confirmation Dialog */}
      <AlertDialog open={bulkDeleteConfirm} onOpenChange={setBulkDeleteConfirm}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Multiple Blogs</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete {selectedBlogs.length} selected blog(s). 
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleBulkDelete}
              className="bg-red-600 hover:bg-red-700"
            >
              Delete {selectedBlogs.length} Blog(s)
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default Blog;
