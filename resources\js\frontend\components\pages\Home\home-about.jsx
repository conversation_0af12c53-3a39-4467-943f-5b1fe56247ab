import React, { useState, useEffect } from "react";

const HomeAbout = () => {
  const [aboutData, setAboutData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchAboutData = async () => {
      try {
        setLoading(false);
        console.log('Fetching About Us data from database...');
        
        // Fetch page content data with page_id=1 and widget_id=4
        const response = await fetch('/api/frontend/page-contents?page_id=1&widget_id=4&paginate=false');
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        console.log('API Response:', result);
        
        if (result.success && result.data && result.data.length > 0) {
          // Find the widget with widget_id = 4 (about widget)
          const aboutWidget = result.data.find(item => item.widget_id === 4);
          
          if (aboutWidget && aboutWidget.pageContent) {
            const config = typeof aboutWidget.pageContent === 'string' 
              ? JSON.parse(aboutWidget.pageContent) 
              : aboutWidget.pageContent;
              
            console.log('Parsed config:', config);
            
            // Map the API response structure to our component structure
            const mappedAboutData = {
              serviceFrom: config.serviceFrom || "1994",
              title: config.title || "Get To Know About Neckle",
              subtitle: config.subtitle || "Welcome to our Neckle!",
              description: config.aboutText ? config.aboutText.replace(/<[^>]*>/g, '') : "Default description", // Strip HTML tags
              authorName: "Natrison Mongla", // These might need to be added to API later
              authorPosition: "CEO & Founder",
              stats: [
                { 
                  number: config.projectCompleted || "600", 
                  suffix: "K+", 
                  label: "Project Completed", 
                  icon: "/Frontend/assets/img/home2/icon/home1.svg" 
                },
                { 
                  number: config.projectSold || "200", 
                  suffix: "K+", 
                  label: "Project Sold", 
                  icon: "/Frontend/assets/img/home2/icon/home1.svg" 
                },
                { 
                  number: config.yearOfExperience || "25", 
                  suffix: "+", 
                  label: "Years Experience", 
                  icon: "/Frontend/assets/img/home2/icon/home1.svg" 
                }
              ]
            };
            
            setAboutData(mappedAboutData);
            console.log('About data set from database:', mappedAboutData);
          } else {
            console.log('No widget found with widget_id 4');
          }
        } else {
          console.log('No data found in API response');
        } 
      } catch (err) {
        console.error('Error fetching about data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchAboutData();
  }, []);

  // Add debugging info
  console.log('About data from database:', aboutData);
  
  
  
  
  if (loading) {
    return (
      <div className="home2-about-area pt-100 mb-100">
        <div className="container">
          <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
            <div className="spinner-border" role="status">
              <span className="sr-only">Loading About Us...</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if there's an error or no data
  if (error || !aboutData) {
    return (
      <div className="home2-about-area pt-100 mb-100">
        <div className="container">
          <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
            <div className="text-center">
              <h3>Unable to load About Us data</h3>
              <p className="text-muted">
                {error ? `Error: ${error}` : 'No data found in database'}
              </p>
              <p className="text-muted">Please check the database for page_id=1 and widget_id=4</p>
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <>
   
      {/* Start Home2 About section */}
      <div className="home2-about-area pt-100 mb-100">
        <div className="container">
          <div className="row g-lg-4 gy-5">
            <div className="col-xl-4">
              <div
                className="about-img wow fadeInUp"
                data-wow-delay="200ms"
              >
                <img
                  src="/Frontend/assets/img/home2/home2-about-img1.png"
                  alt="about"
                />
              </div>
              <div
                className="activetis wow fadeInUp"
                data-wow-delay="300ms"
              >
                {aboutData.stats && aboutData.stats.map((stat, index) => (
                  <div key={index} className="single-activiti">
                    <div className="icon">
                      <img
                        src={stat.icon || "/Frontend/assets/img/home2/icon/home1.svg"}
                        alt="icon"
                      />
                    </div>
                    <div className="content">
                      <div className="number">
                        <h5 className="counter">{stat.number || "0"}</h5>
                        <span>{stat.suffix || ""}</span>
                      </div>
                      <p>{stat.label || ""}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            <div className="col-xl-8">
              <div
                className="about-content-wrap wow fadeInUp"
                data-wow-delay="400ms"
              >
                <div className="section-title1">
                  <span>(Since-{aboutData.serviceFrom})</span>
                  <h2>{aboutData.title}</h2>
                </div>
                <h6>{aboutData.subtitle}</h6>
                <p>
                  {aboutData.description}
                </p>
                <div className="author-and-img">
                  <div className="author-area">
                    <img
                      src="/Frontend/assets/img/home2/icon/author-signature.svg"
                      alt="author signature"
                    />
                    <div className="author-name-deg">
                      <h6>{aboutData.authorName}</h6>
                      <span>({aboutData.authorPosition})</span>
                    </div>
                  </div>
                  <div className="about-img">
                    <img
                      src="/Frontend/assets/img/home2/home2-about-img2.png"
                      alt="about"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div> 
      {/* End Home2 About section */}
    </>
  );
};

export default HomeAbout;