import React, { useEffect, useState } from "react";

const HomeAbout = ({ pageContents = [] }) => {
  const [aboutData, setAboutData] = useState(null);

  useEffect(() => {
    if (!pageContents || pageContents.length === 0) return;

    // Find the about widget (widget_id = 4)
    const aboutWidget = pageContents.find(item => item.widget_id === 4);
    if (!aboutWidget) return;

    try {
      const config =
        typeof aboutWidget.pageContent === "string"
          ? JSON.parse(aboutWidget.pageContent)
          : aboutWidget.pageContent;
      setAboutData(config);
    } catch (error) {
      console.error("Error parsing about widget data:", error);
    }
  }, [pageContents]);

  // Show loading state
  if (!aboutData) {
    return (
      <div className="home2-about-area pt-100 mb-100">
        <div className="container text-center">
          <h4>Loading About Us section...</h4>
        </div>
      </div>
    );
  }

  // Render About Us section
  return (
    <>
      {/* Start Home2 About section */}
      <div className="home2-about-area pt-100 mb-100">
        <div className="container">
          <div className="row g-lg-4 gy-5">
            {/* Left Side - Image and Stats */}
            <div className="col-xl-4">
              <div className="about-img wow fadeInUp" data-wow-delay="200ms">
                <img
                  src={aboutData.image1 || "/Frontend/assets/img/home2/home2-about-img1.png"}
                  alt="about"
                />
              </div>

              <div className="activetis wow fadeInUp" data-wow-delay="300ms">
                {(aboutData.stats || []).map((stat, index) => (
                  <div key={index} className="single-activiti">
                    <div className="icon">
                      <img
                        src={stat.icon || "/Frontend/assets/img/home2/icon/home1.svg"}
                        alt="icon"
                      />
                    </div>
                    <div className="content">
                      <div className="number">
                        <h5 className="counter">{stat.number}</h5>
                        <span>{stat.suffix}</span>
                      </div>
                      <p>{stat.label}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Right Side - Content */}
            <div className="col-xl-8">
              <div
                className="about-content-wrap wow fadeInUp"
                data-wow-delay="400ms"
              >
                <div className="section-title1">
                  <span>(Since-{aboutData.serviceFrom})</span>
                  <h2>{aboutData.title}</h2>
                </div>
                <h6>{aboutData.subtitle}</h6>
                {aboutData.aboutText && aboutData.aboutText.replace(/<[^>]+>/g, "")}

                <div className="author-and-img">
                  <div className="author-area">
                    <img
                      src={aboutData.ceoSignature || "/Frontend/assets/img/home2/icon/author-signature.svg"}
                      alt="author signature"
                    />
                    <div className="author-name-deg">
                      <h6>{aboutData.authorName || "Natrison Mongla"}</h6>
                      <span>({aboutData.authorPosition || "CEO & Founder"})</span>
                    </div> 
                  </div>

                  <div className="about-img">
                    <img
                      src={aboutData.image2 || "/Frontend/assets/img/home2/home2-about-img2.png"}
                      alt="about"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div> 
      {/* End Home2 About section */}
    </>
  );
};

export default HomeAbout;
