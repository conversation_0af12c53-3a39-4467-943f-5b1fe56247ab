
import React, { useState, useEffect } from 'react';
import Swal from 'sweetalert2';
import * as landOwnerAPI from '../../services/landOwnerAPI';
import { Button } from '@/components/ui/button';
import { useTranslation } from '@/hooks/useTranslation';
import ImageModal from '@/components/ui/ImageModal';
import LandOwnersStatistics from './LandOwnersStatistics';
import LandOwnersSearch from './LandOwnersSearch';
import LandOwnersTable from './LandOwnersTable';
import LandOwnerModal from './LandOwnersModal';
import { Plus } from 'lucide-react';

const LandOwnersPage = () => {
  const { t } = useTranslation();
  
  // State management
  const [landOwners, setLandOwners] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingOwner, setEditingOwner] = useState(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalRecords, setTotalRecords] = useState(0);
  const [perPage] = useState(10);
  const [statistics, setStatistics] = useState({
    total_owners: 0,
    active_owners: 0,
    inactive_owners: 0,
    recent_registrations: 0
  });

  const [formData, setFormData] = useState({
    first_name: '',
    last_name: '',
    father_name: '',
    mother_name: '',
    address: '',
    phone: '',
    nid_number: '',
    email: '',
    photo: '',
    document_type: 'nid',
    nid_front: '',
    nid_back: '',
    passport_photo: ''
  });

  const [editFormData, setEditFormData] = useState({
    first_name: '',
    last_name: '',
    father_name: '',
    mother_name: '',
    address: '',
    phone: '',
    nid_number: '',
    email: '',
    photo: '',
    document_type: 'nid',
    nid_front: '',
    nid_back: '',
    passport_photo: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isUpdating, setIsUpdating] = useState(false);
  
  // Image modal state
  const [showImageModal, setShowImageModal] = useState(false);
  const [selectedImage, setSelectedImage] = useState({ url: '', ownerName: '', title: '' });

  // Sweet Alert helper
  const showAlert = {
    success: (title, text) => {
      Swal.fire({
        icon: 'success',
        title: title,
        text: text,
        confirmButtonColor: '#10b981',
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        }
      });
    },
    error: (title, text) => {
      Swal.fire({
        icon: 'error',
        title: title,
        text: text,
        confirmButtonColor: '#ef4444',
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        }
      });
    },
    warning: (title, text) => {
      Swal.fire({
        icon: 'warning',
        title: title,
        text: text,
        confirmButtonColor: '#f59e0b',
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        }
      });
    },
    loading: (title, text) => {
      Swal.fire({
        title: title,
        text: text,
        allowOutsideClick: false,
        customClass: {
          popup: 'swal2-z-index-high',
          backdrop: 'swal2-backdrop-z-index-high'
        },
        didOpen: () => {
          Swal.showLoading();
        }
      });
    }
  };

  // API functions
  const fetchLandOwners = async (search = '', page = 1, limit = 10) => {
    try {
      setLoading(true);
      const params = {
        page: page.toString(),
        per_page: limit.toString(),
        ...(search && { search })
      };

      const result = await landOwnerAPI.getAll(params);

      if (result.success) {
        setLandOwners(result.data.data || []);
        setCurrentPage(result.data.current_page || 1);
        setTotalPages(result.data.last_page || 1);
        setTotalRecords(result.data.total || 0);
        
        // Calculate statistics
        const owners = result.data.data || [];
        const total = result.data.total || 0;
        const active = owners.filter(owner => owner.status === 'active').length;
        const inactive = owners.filter(owner => owner.status === 'inactive').length;
        const recent = owners.filter(owner => {
          const created = new Date(owner.created_at);
          const thirtyDaysAgo = new Date();
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
          return created > thirtyDaysAgo;
        }).length;
        
        setStatistics({
          total_owners: total,
          active_owners: active,
          inactive_owners: inactive,
          recent_registrations: recent
        });
      }
    } catch (error) {
      console.error('Error fetching land owners:', error);
      showAlert.error('Error!', 'Failed to fetch land owners. Please try again.');
    } finally {
      setLoading(false);
    }
  };

    const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchLandOwners(searchTerm, 1, perPage);
  };

  // Helper function to get full image URL
  const getImageUrl = (photoPath) => {
    // Check if photoPath is null, undefined, or not a string
    if (!photoPath || typeof photoPath !== 'string') return null;
    
    // If it's already a full URL, return as is
    if (photoPath.startsWith('http://') || photoPath.startsWith('https://')) {
      return photoPath;
    }
    
    // If it starts with /landowners, use the base URL directly
    if (photoPath.startsWith('/landowners/')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it starts with /storage, use the base URL
    if (photoPath.startsWith('/storage')) {
      return `${window.location.protocol}//${window.location.host}${photoPath}`;
    }
    
    // If it's just a filename or relative path, assume it's a photo in landowners/photo
    return `${window.location.protocol}//${window.location.host}/landowners/photo/${photoPath}`;
  };

  // Handle opening image modal
  const handleImageClick = (imageUrl, ownerName) => {
    const fullImageUrl = getImageUrl(imageUrl);
    if (fullImageUrl) {
      setSelectedImage({
        url: fullImageUrl,
        ownerName: ownerName,
        title: `${ownerName}'s Photo`
      });
      setShowImageModal(true);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();

    // Validate form data - Image is NOT mandatory
    if (!formData.first_name || !formData.last_name || !formData.father_name || !formData.address) {
      showAlert.warning('Validation Error!', 'Please fill in all required fields: First Name, Last Name, Father\'s Name, and Address.');
      return;
    }

    // Optional: Validate email format if provided
    if (formData.email && !/\S+@\S+\.\S+/.test(formData.email)) {
      showAlert.warning('Validation Error!', 'Please enter a valid email address.');
      return;
    }

    setIsSubmitting(true);

    // Show loading alert
    showAlert.loading(
      'Creating Land Owner...',
      'Please wait while we create the land owner record.'
    );

    try {
      console.log('📝 Form data before submission:', {
        ...formData,
        photo: formData.photo instanceof File ? `File(${formData.photo.name}, ${formData.photo.size} bytes)` : formData.photo,
        nid_front: formData.nid_front instanceof File ? `File(${formData.nid_front.name}, ${formData.nid_front.size} bytes)` : formData.nid_front,
        nid_back: formData.nid_back instanceof File ? `File(${formData.nid_back.name}, ${formData.nid_back.size} bytes)` : formData.nid_back,
        passport_photo: formData.passport_photo instanceof File ? `File(${formData.passport_photo.name}, ${formData.passport_photo.size} bytes)` : formData.passport_photo,
      });

      const result = await landOwnerAPI.create(formData);
      console.log('✅ Create API Response:', result);

      if (result.success) {
        // Reset form
        setFormData({
          first_name: '',
          last_name: '',
          father_name: '',
          mother_name: '',
          address: '',
          phone: '',
          nid_number: '',
          email: '',
          photo: '',
          document_type: 'nid',
          nid_front: '',
          nid_back: '',
          passport_photo: ''
        });

        setShowAddModal(false);

        // Refresh data
        fetchLandOwners(searchTerm, currentPage, perPage);

        // Success alert
        showAlert.success(
          'Success!',
          'Land owner created successfully!'
        );
      } else {
        // Handle validation errors or other API errors
        let errorMessage = result.message || 'Unknown error occurred';
        
        if (result.errors) {
          // If there are validation errors, show them
          const errorList = Object.values(result.errors).flat().join('\n');
          errorMessage = `Validation errors:\n${errorList}`;
        }
        
        showAlert.error(
          'Error!',
          errorMessage
        );
      }
    } catch (error) {
      console.error('❌ Error creating land owner:', error);
      
      let errorMessage = 'Error creating land owner. Please try again.';
      
      if (error.response && error.response.data) {
        if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.errors) {
          const errorList = Object.values(error.response.data.errors).flat().join('\n');
          errorMessage = `Validation errors:\n${errorList}`;
        }
      }
      
      showAlert.error(
        'Error!',
        errorMessage
      );
    } finally {
      setIsSubmitting(false);
    }
  };



  const handlePageChange = (newPage) => {
    if (newPage >= 1 && newPage <= totalPages) {
      setCurrentPage(newPage);
      fetchLandOwners(searchTerm, newPage, perPage);
    }
  };

  const handleEditOwner = (owner) => {
    setEditingOwner(owner);
    setEditFormData({
      first_name: owner.first_name || '',
      last_name: owner.last_name || '',
      father_name: owner.father_name || '',
      mother_name: owner.mother_name || '',
      address: owner.address || '',
      phone: owner.phone || '',
      nid_number: owner.nid_number || '',
      email: owner.email || '',
      photo: owner.photo || '',
      document_type: owner.document_type || 'nid',
      nid_front: owner.nid_front || '',
      nid_back: owner.nid_back || '',
      passport_photo: owner.passport_photo || ''
    });
    setShowEditModal(true);
  };

  const handleDeleteOwner = async (owner) => {
    const result = await Swal.fire({
      title: 'Are you sure?',
      text: `Do you want to delete ${owner.first_name} ${owner.last_name}? This action cannot be undone!`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#ef4444',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, delete it!',
      customClass: {
        popup: 'swal2-z-index-high',
        backdrop: 'swal2-backdrop-z-index-high'
      }
    });

    if (result.isConfirmed) {
      try {
        const deleteResult = await landOwnerAPI.delete_(owner.id);
        console.log('Delete result:', deleteResult);

        if (deleteResult.success) {
          // Refresh data
          fetchLandOwners(searchTerm, currentPage, perPage);

          showAlert.success(
            'Deleted!',
            'Land owner has been deleted successfully.'
          );
        } else {
          showAlert.error(
            'Error!',
            deleteResult.message || 'Failed to delete land owner.'
          );
        }
      } catch (error) {
        console.error('Error deleting land owner:', error);
        showAlert.error(
          'Error!',
          'Error deleting land owner. Please try again.'
        );
      }
    }
  };

  // Handle activate land owner
  const handleActivateOwner = async (owner) => {
    const result = await Swal.fire({
      title: 'Activate Land Owner',
      text: `Do you want to activate ${owner.first_name} ${owner.last_name}?`,
      icon: 'question',
      showCancelButton: true,
      confirmButtonColor: '#10b981',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, activate!',
      customClass: {
        popup: 'swal2-z-index-high',
        backdrop: 'swal2-backdrop-z-index-high'
      }
    });

    if (result.isConfirmed) {
      try {
        const activateResult = await landOwnerAPI.activate(owner.id);
        console.log('Activate result:', activateResult);

        if (activateResult.success) {
          // Refresh data
          fetchLandOwners(searchTerm, currentPage, perPage);

          showAlert.success(
            'Activated!',
            'Land owner has been activated successfully.'
          );
        } else {
          showAlert.error(
            'Error!',
            activateResult.message || 'Failed to activate land owner.'
          );
        }
      } catch (error) {
        console.error('Error activating land owner:', error);
        showAlert.error(
          'Error!',
          'Error activating land owner. Please try again.'
        );
      }
    }
  };

  // Handle deactivate land owner
  const handleDeactivateOwner = async (owner) => {
    const result = await Swal.fire({
      title: 'Deactivate Land Owner',
      text: `Do you want to deactivate ${owner.first_name} ${owner.last_name}?`,
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#f59e0b',
      cancelButtonColor: '#6b7280',
      confirmButtonText: 'Yes, deactivate!',
      customClass: {
        popup: 'swal2-z-index-high',
        backdrop: 'swal2-backdrop-z-index-high'
      }
    });

    if (result.isConfirmed) {
      try {
        const deactivateResult = await landOwnerAPI.deactivate(owner.id);
        console.log('Deactivate result:', deactivateResult);

        if (deactivateResult.success) {
          // Refresh data
          fetchLandOwners(searchTerm, currentPage, perPage);

          showAlert.success(
            'Deactivated!',
            'Land owner has been deactivated successfully.'
          );
        } else {
          showAlert.error(
            'Error!',
            deactivateResult.message || 'Failed to deactivate land owner.'
          );
        }
      } catch (error) {
        console.error('Error deactivating land owner:', error);
        showAlert.error(
          'Error!',
          'Error deactivating land owner. Please try again.'
        );
      }
    }
  };

  const handleUpdateSubmit = async (e) => {
    e.preventDefault();

    // Validate form data - Image is NOT mandatory
    if (!editFormData.first_name || !editFormData.last_name || !editFormData.father_name || !editFormData.address) {
      showAlert.warning('Validation Error!', 'Please fill in all required fields: First Name, Last Name, Father\'s Name, and Address.');
      return;
    }

    // Optional: Validate email format if provided
    if (editFormData.email && !/\S+@\S+\.\S+/.test(editFormData.email)) {
      showAlert.warning('Validation Error!', 'Please enter a valid email address.');
      return;
    }

    setIsUpdating(true);

    // Show loading alert
    showAlert.loading(
      'Updating Land Owner...',
      'Please wait while we update the land owner record.'
    );

    try {
      const result = await landOwnerAPI.update(editingOwner.id, editFormData);

      if (result.success) {
        // Reset form
        setEditFormData({
          first_name: '',
          last_name: '',
          father_name: '',
          mother_name: '',
          address: '',
          phone: '',
          nid_number: '',
          email: '',
          photo: '',
          document_type: 'nid',
          nid_front: '',
          nid_back: '',
          passport_photo: ''
        });

        setShowEditModal(false);
        setEditingOwner(null);

        // Refresh data
        fetchLandOwners(searchTerm, currentPage, perPage);

        // Success alert
        showAlert.success(
          'Success!',
          'Land owner updated successfully!'
        );
      } else {
        // Handle validation errors or other API errors
        let errorMessage = result.message || 'Unknown error occurred';
        
        if (result.errors) {
          // If there are validation errors, show them
          const errorList = Object.values(result.errors).flat().join('\n');
          errorMessage = `Validation errors:\n${errorList}`;
        }
        
        showAlert.error(
          'Error!',
          errorMessage
        );
      }
    } catch (error) {
      
      let errorMessage = 'Error updating land owner. Please try again.';
      
      if (error.response && error.response.data) {
        if (error.response.data.message) {
          errorMessage = error.response.data.message;
        } else if (error.response.data.errors) {
          const errorList = Object.values(error.response.data.errors).flat().join('\n');
          errorMessage = `Validation errors:\n${errorList}`;
        }
      }
      
      showAlert.error(
        'Error!',
        errorMessage
      );
    } finally {
      setIsUpdating(false);
    }
  };

  // Load data on component mount
  useEffect(() => {
    fetchLandOwners('', 1, perPage);
  }, [perPage]);


  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">{t('landOwners.header.title')}</h1>
          <p className="text-muted-foreground">
            {t('landOwners.header.description')}
          </p>
        </div>
        <Button onClick={() => setShowAddModal(true)}>
          <Plus className="mr-2 h-4 w-4" />
          {t('landOwners.addOwner')}
        </Button>
      </div>

      {/* Statistics Cards */}
      <LandOwnersStatistics statistics={statistics} />

      {/* Search and Filters */}
      <LandOwnersSearch 
        searchTerm={searchTerm} 
        setSearchTerm={setSearchTerm} 
        handleSearch={handleSearch} 
      />
      {/* Land Owners Table */}
      <LandOwnersTable
        landOwners={landOwners}
        loading={loading}
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalRecords}
        handleEditOwner={handleEditOwner}
        handleDeleteOwner={handleDeleteOwner}
        handleActivateOwner={handleActivateOwner}
        handleDeactivateOwner={handleDeactivateOwner}
        handleImageClick={handleImageClick}
  
        getImageUrl={getImageUrl}
        searchTerm={searchTerm}
        perPage={perPage}
        handlePageChange={handlePageChange}
      />

      {/* Add Owner Modal */}
      <LandOwnerModal
        isOpen={showAddModal}
        onClose={() => setShowAddModal(false)}
        onSubmit={handleSubmit}
        formData={formData}
        setFormData={setFormData}
        isSubmitting={isSubmitting}
        mode="add"
      />

      {/* Edit Owner Modal */}
      <LandOwnerModal
        isOpen={showEditModal}
        onClose={() => setShowEditModal(false)}
        onSubmit={handleUpdateSubmit}
        formData={editFormData}
        setFormData={setEditFormData}
        isSubmitting={isUpdating}
        mode="edit"
      />


      {/* Image Modal */}
      <ImageModal
        isOpen={showImageModal}
        onClose={() => setShowImageModal(false)}
        imageUrl={selectedImage.url}
        title={selectedImage.title}
        ownerName={selectedImage.ownerName}
      />

     

    </div>
  );
};

export default LandOwnersPage;
