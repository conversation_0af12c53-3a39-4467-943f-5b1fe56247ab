import React from "react";
import { Link } from "react-router-dom";

const HeaderMenu = ({ frontendSettings }) => {
  const [topBarMenus, setTopBarMenus] = React.useState([]);
  const [mainMenus, setMainMenus] = React.useState([]);

  React.useEffect(() => {
    fetch('/api/menu-manages?menu_position_id=1')
      .then(res => res.json())
      .then(data => {
        if (data.success && Array.isArray(data.data)) {
          setTopBarMenus(data.data);
        }
      });

    fetch('/api/menu-manages?menu_position_id=2')
      .then(res => res.json())
      .then(data => {
        if (data.success && Array.isArray(data.data)) {
          setMainMenus(data.data);
        }
      });
  }, []);

  return (
    <div className="topbar-header">
      {/* Top Bar */}
      {frontendSettings.showTopBar === 1 && (
      <div className="top-bar style-2">
         <div className="content text-white">
          <div className="icon"><svg width="28" height="28" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M2.5 3H13.5C14.3284 3 15 3.67157 15 4.5V11.5C15 12.3284 14.3284 13 13.5 13H2.5C1.67157 13 1 12.3284 1 11.5V4.5C1 3.67157 1.67157 3 2.5 3Z" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/><path d="M15 4.5L8 8.5L1 4.5" stroke="white" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/></svg>
          <span>Get In Touch</span>
          <h6><a href={`mailto:${frontendSettings?.footerEmail}`} className="text-white">{frontendSettings?.footerEmail}</a></h6>
          </div>
          
        </div>
        <div className="top-bar-items">
          <ul>
            {topBarMenus.map(menu => (
              <li key={menu.id}>
                <Link to={menu.slug === 'home' ? '/' : `/${menu.slug}`}>{menu.title}</Link>
              </li>
            ))}
          </ul>
        </div>
        <div className="topbar-right">
          <div className="hotline-area d-md-flex d-none">
            <div className="icon">
              <svg
                width="28"
                height="28"
                viewBox="0 0 28 28"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M27.2653 21.5995L21.598 17.8201C20.8788 17.3443 19.9147 17.5009 19.383 18.1798L17.7322 20.3024C17.6296 20.4377 17.4816 20.5314 17.3154 20.5664C17.1492 20.6014 16.9759 20.5752 16.8275 20.4928L16.5134 20.3196C15.4725 19.7522 14.1772 19.0458 11.5675 16.4352C8.95784 13.8246 8.25001 12.5284 7.6826 11.4893L7.51042 11.1753C7.42683 11.0269 7.39968 10.8532 7.43398 10.6864C7.46827 10.5195 7.56169 10.3707 7.69704 10.2673L9.81816 8.61693C10.4968 8.08517 10.6536 7.1214 10.1784 6.40198L6.39895 0.734676C5.91192 0.00208106 4.9348 -0.21784 4.18082 0.235398L1.81096 1.65898C1.06634 2.09672 0.520053 2.80571 0.286612 3.63733C-0.56677 6.74673 0.0752209 12.1131 7.98033 20.0191C14.2687 26.307 18.9501 27.9979 22.1677 27.9979C22.9083 28.0011 23.6459 27.9048 24.3608 27.7115C25.1925 27.4783 25.9016 26.932 26.3391 26.1871L27.7641 23.8187C28.218 23.0645 27.9982 22.0868 27.2653 21.5995Z"
                />
              </svg>
            </div>
            <div className="content">
              <span>To More Inquiry</span>
              <h6>
                <a href="tel:{ frontendSettings?.Phone  }">{ frontendSettings.phone  }</a>
              </h6>
            </div>
          </div>
        </div>
      </div>
      )}
      {/* Header Section */}
      <header className="header-area style-2">
        <div className="header-logo d-lg-none d-flex">
          <Link to="/">
            <img
              alt="logo"
              className="img-fluid"
              src={frontendSettings.logo}
            />
          </Link>
        </div>
        <div className="company-logo d-lg-block d-none">
          <Link to="/">
            <img src={frontendSettings?.logo || '/default-logo.png'} alt="logo" />
          </Link>
        </div>
        <div className="menu-button sidebar-button mobile-menu-btn d-lg-none d-flex">
          <svg width="15" height="12" viewBox="0 0 15 12" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M0 0.75C0 0.551088 0.0790176 0.360322 0.21967 0.21967C0.360322 0.0790178 0.551088 0 0.75 0H10.5C10.6989 0 10.8897 0.0790178 11.0303 0.21967C11.171 0.360322 11.25 0.551088 11.25 0.75C11.25 0.948912 11.171 1.13968 11.0303 1.28033C10.8897 1.42098 10.6989 1.5 10.5 1.5H0.75C0.551088 1.5 0.360322 1.42098 0.21967 1.28033C0.0790176 1.13968 0 0.948912 0 0.75ZM14.25 5.25H0.75C0.551088 5.25 0.360322 5.32902 0.21967 5.46967C0.0790176 5.61032 0 5.80109 0 6C0 6.19891 0.0790176 6.38968 0.21967 6.53033C0.360322 6.67098 0.551088 6.75 0.75 6.75H14.25C14.4489 6.75 14.6397 6.67098 14.7803 6.53033C14.921 6.38968 15 6.19891 15 6C15 5.80109 14.921 5.61032 14.7803 5.46967C14.6397 5.32902 14.4489 5.25 14.25 5.25ZM7.5 10.5H0.75C0.551088 10.5 0.360322 10.579 0.21967 10.7197C0.0790176 10.8603 0 11.0511 0 11.25C0 11.4489 0.0790176 11.6397 0.21967 11.7803C0.360322 11.921 0.551088 12 0.75 12H7.5C7.69891 12 7.88968 11.921 8.03033 11.7803C8.17098 11.6397 8.25 11.4489 8.25 11.25C8.25 11.0511 8.17098 10.8603 8.03033 10.7197C7.88968 10.579 7.69891 10.5 7.5 10.5Z"
            />
          </svg>
          <span>MENU</span>
       </div>

        <div className="main-menu">
          <div className="mobile-logo-area d-lg-none d-flex justify-content-between align-items-center">
            <div className="mobile-logo-wrap">
              <Link to="/">
                <img
                  alt="logo"
                  src={frontendSettings.logo}
                />
              </Link>
            </div>
          </div>

          <ul className="menu-list">
            {mainMenus.filter(menu => !menu.parent_id).map(menu => {
              const children = mainMenus.filter(child => child.parent_id === menu.id);
              if (children.length > 0) {
                return (
                  <li key={menu.id} className="position-inherit menu-parent" style={{position: 'relative'}}>
                    <a href={menu.slug === 'home' ? '/' : `/${menu.slug}`} className="drop-down">{menu.title}</a>
                    <i className="bi bi-plus dropdown-icon d-lg-none d-block"></i>
                    <ul className="dropdown-menu" style={{
                      display: 'none',
                      position: 'absolute',
                      left: 0,
                      top: '100%',
                      zIndex: 1000,
                      background: '#fff',
                      color: '#222',
                      minWidth: '180px',
                      boxShadow: '0 2px 8px rgba(0,0,0,0.12)',
                      padding: '8px 0',
                      borderRadius: '4px',
                      border: '1px solid #eee'
                    }}>
                      {children.map(child => (
                        <li key={child.id} style={{padding: '6px 18px'}}>
                          <Link to={child.slug === 'home' ? '/' : `/${child.slug}`} style={{color: '#222', textDecoration: 'none', display: 'block'}}>{child.title}</Link>
                        </li>
                      ))}
                    </ul>
                  </li>
                );
              } else {
                return (
                  <li key={menu.id}>
                    <Link to={menu.slug && menu.slug.toLowerCase() === 'home' ? '/' : `/${menu.slug}`}>{menu.title}</Link>
                  </li>
                );
              }
            })}
            {/* <li>
              <Link to="/contact" className="drop-down">CONTACT US</Link>
            </li> */}
          </ul>
          <style>{`
            .menu-parent:hover > .dropdown-menu {
              display: block !important;
            }
          `}</style>
        </div>

        <div className="nav-right d-lg-flex d-none jsutify-content-end align-items-center">
          <div className="search-btn d-lg-flex d-none">
            <a>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 12 12"
              >
                <path d="M8.80684 7.75632C9.53304 6.76537 9.85829 5.53677 9.71754 4.31631C9.57678 3.09585 8.9804 1.97354 8.04769 1.17391C7.11499 0.374287 5.91476 -0.0436852 4.68712 0.00361793C3.45949 0.050921 2.29498 0.560011 1.42657 1.42904C0.558166 2.29806 0.0499094 3.46294 0.00348477 4.69061C-0.0429399 5.91828 0.375891 7.11821 1.17619 8.05034C1.97648 8.98247 3.09922 9.57805 4.31978 9.71794C5.54034 9.85782 6.76871 9.53168 7.75913 8.80478H7.75838C7.78088 8.83478 7.80488 8.86328 7.83188 8.89103L10.7193 11.7784C10.8599 11.9191 11.0507 11.9982 11.2496 11.9983C11.4486 11.9984 11.6394 11.9194 11.7801 11.7788C11.9208 11.6382 11.9999 11.4474 12 11.2485C12.0001 11.0495 11.9211 10.8587 11.7805 10.718L8.89309 7.83057C8.86628 7.80342 8.83744 7.77835 8.80684 7.75557V7.75632Z" />
              </svg>
            </a>
          </div>

          <button
            type="button"
            className="modal-btn header-user-btn d-lg-flex d-none"
            data-bs-toggle="modal"
            data-bs-target="#logInModal01"
          >
            <svg width="16" height="16" viewBox="0 0 16 16" xmlns="http://www.w3.org/2000/svg">
              <path
                fillRule="evenodd"
                clipRule="evenodd"
                d="M14.4311 12.759C15.417 11.4291 16 9.78265 16 8C16 3.58169 12.4182 0 8 0C3.58169 0 0 3.58169 0 8C0 12.4182 3.58169 16 8 16C10.3181 16 12.4058 15.0141 13.867 13.4387C14.0673 13.2226 14.2556 12.9957 14.4311 12.759ZM13.9875 12C14.7533 10.8559 15.1999 9.48009 15.1999 8C15.1999 4.02355 11.9764 0.799983 7.99991 0.799983C4.02355 0.799983 0.799983 4.02355 0.799983 8C0.799983 9.48017 1.24658 10.8559 2.01245 12C2.97866 10.5566 4.45301 9.48194 6.17961 9.03214C5.34594 8.45444 4.79998 7.49102 4.79998 6.39995C4.79998 4.63266 6.23271 3.19993 8 3.19993C9.76729 3.19993 11.2 4.63266 11.2 6.39995C11.2 7.49093 10.654 8.45444 9.82039 9.03206C11.5469 9.48194 13.0213 10.5565 13.9875 12ZM13.4722 12.6793C12.3495 10.8331 10.3188 9.59997 8.00008 9.59997C5.68126 9.59997 3.65049 10.8331 2.52776 12.6794C3.84829 14.2222 5.80992 15.2 8 15.2C10.1901 15.2 12.1517 14.2222 13.4722 12.6793ZM8 8.79998C9.32551 8.79998 10.4 7.72554 10.4 6.39995C10.4 5.07444 9.32559 3.99992 8 3.99992C6.6744 3.99992 5.59997 5.07452 5.59997 6.40003C5.59997 7.72554 6.67449 8.79998 8 8.79998Z"
              />
            </svg>
          </button>
          {frontendSettings.addPropertyButton===1 && (
          <Link className="primary-btn3 d-lg-flex d-none" to="/dashboard-add-property">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 14 14">
              <g clipPath="url(#clip0_485_1365)">
                <path d="M7 13.125C5.37555 13.125 3.81763 12.4797 2.66897 11.331C1.52031 10.1824 0.875 8.62445 0.875 7C0.875 5.37555 1.52031 3.81763 2.66897 2.66897C3.81763 1.52031 5.37555 0.875 7 0.875C8.62445 0.875 10.1824 1.52031 11.331 2.66897C12.4797 3.81763 13.125 5.37555 13.125 7C13.125 8.62445 12.4797 10.1824 11.331 11.331C10.1824 12.4797 8.62445 13.125 7 13.125ZM7 14C8.85652 14 10.637 13.2625 11.9497 11.9497C13.2625 10.637 14 8.85652 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0C5.14348 0 3.36301 0.737498 2.05025 2.05025C0.737498 3.36301 0 5.14348 0 7C0 8.85652 0.737498 10.637 2.05025 11.9497C3.36301 13.2625 5.14348 14 7 14Z"></path>
                <path d="M7 3.5C7.11603 3.5 7.22731 3.54609 7.30936 3.62814C7.39141 3.71019 7.4375 3.82147 7.4375 3.9375V6.5625H10.0625C10.1785 6.5625 10.2898 6.60859 10.3719 6.69064C10.4539 6.77269 10.5 6.88397 10.5 7C10.5 7.11603 10.4539 7.22731 10.3719 7.30936C10.2898 7.39141 10.1785 7.4375 10.0625 7.4375H7.4375V10.0625C7.4375 10.1785 7.39141 10.2898 7.30936 10.3719C7.22731 10.4539 7.11603 10.5 7 10.5C6.88397 10.5 6.77269 10.4539 6.69064 10.3719C6.60859 10.2898 6.5625 10.1785 6.5625 10.0625V7.4375H3.9375C3.82147 7.4375 3.71019 7.39141 3.62814 7.30936C3.54609 7.22731 3.5 7.11603 3.5 7C3.5 6.88397 3.54609 6.77269 3.62814 6.69064C3.71019 6.60859 3.82147 6.5625 3.9375 6.5625H6.5625V3.9375C6.5625 3.82147 6.60859 3.71019 6.69064 3.62814C6.77269 3.54609 6.88397 3.5 7 3.5Z"></path>
              </g>
            </svg>
            ADD PROPERTY
          </Link>
          )}
        </div>
      </header>
    </div>
  );
};

export default HeaderMenu;
