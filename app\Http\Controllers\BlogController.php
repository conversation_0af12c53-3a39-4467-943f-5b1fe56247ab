<?php

namespace App\Http\Controllers;

use App\Models\Blog;
use App\Models\PropertyType;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class BlogController extends Controller
{
    /**
     * Display a listing of blogs (admin)
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = Blog::with(['category:id,name']);

            // Search functionality
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'LIKE', "%{$search}%")
                      ->orWhere('excerpt', 'LIKE', "%{$search}%")
                      ->orWhere('content', 'LIKE', "%{$search}%")
                      ->orWhere('author_name', 'LIKE', "%{$search}%");
                });
            }

            // Filter by status
            if ($request->filled('status')) {
                $query->where('status', $request->status);
            }

            // Filter by category
            if ($request->filled('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            // Filter by featured
            if ($request->filled('is_featured')) {
                $query->where('is_featured', $request->boolean('is_featured'));
            }

            // Filter by author
            if ($request->filled('author')) {
                $query->where('author_name', 'LIKE', "%{$request->author}%");
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'created_at');
            $sortOrder = $request->get('sort_order', 'desc');
            
            $allowedSortFields = ['title', 'status', 'is_featured', 'published_at', 'created_at', 'views_count'];
            if (in_array($sortBy, $allowedSortFields)) {
                $query->orderBy($sortBy, $sortOrder);
            }

            // Pagination
            $perPage = min($request->get('per_page', 10), 100);
            $blogs = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $blogs->items(),
                'meta' => [
                    'current_page' => $blogs->currentPage(),
                    'last_page' => $blogs->lastPage(),
                    'per_page' => $blogs->perPage(),
                    'total' => $blogs->total(),
                    'from' => $blogs->firstItem(),
                    'to' => $blogs->lastItem(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch blogs',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Store a newly created blog
     */
    public function store(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'excerpt' => 'nullable|string|max:500',
                'category_id' => 'nullable|exists:property_types,id',
                'status' => 'required|in:draft,published,scheduled',
                'is_featured' => 'boolean',
                'published_at' => 'nullable|date',
                'author_name' => 'required|string|max:255',
                'author_email' => 'nullable|email|max:255',
                'author_bio' => 'nullable|string|max:1000',
                'tags' => 'nullable|array',
                'tags.*' => 'string|max:50',
                'seo_title' => 'nullable|string|max:255',
                'seo_description' => 'nullable|string|max:500',
                'seo_keywords' => 'nullable|array',
                'seo_keywords.*' => 'string|max:50',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Generate slug
            $baseSlug = Str::slug($data['title']);
            $slug = $this->generateUniqueSlug($baseSlug);
            $data['slug'] = $slug;

            // Handle featured image upload
            if ($request->hasFile('featured_image')) {
                $image = $request->file('featured_image');
                $imageName = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();

                // Save to public/blog directory
                $destinationPath = public_path('blog');
                $image->move($destinationPath, $imageName);
                $data['featured_image'] = '/blog/' . $imageName;
            }

            // Generate excerpt if not provided
            if (empty($data['excerpt'])) {
                $data['excerpt'] = Str::limit(strip_tags($data['content']), 200);
            }

            // Set published_at based on status
            if ($data['status'] === 'published' && empty($data['published_at'])) {
                $data['published_at'] = now();
            }

            // Convert arrays to JSON
            if (isset($data['tags'])) {
                $data['tags'] = json_encode($data['tags']);
            }
            if (isset($data['seo_keywords'])) {
                $data['seo_keywords'] = json_encode($data['seo_keywords']);
            }

            $blog = Blog::create($data);
            $blog->load('category:id,name');

            return response()->json([
                'success' => true,
                'message' => 'Blog created successfully',
                'data' => $blog
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create blog',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Display the specified blog
     */
    public function show($id): JsonResponse
    {
        try {
            $blog = Blog::with(['category:id,name'])->findOrFail($id);

            return response()->json([
                'success' => true,
                'data' => $blog
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Blog not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch blog',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Update the specified blog
     */
    public function update(Request $request, $id): JsonResponse
    {
        try {
            $blog = Blog::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'title' => 'required|string|max:255',
                'content' => 'required|string',
                'excerpt' => 'nullable|string|max:500',
                'category_id' => 'nullable|exists:property_types,id',
                'featured_image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:2048',
                'status' => 'required|in:draft,published,scheduled',
                'is_featured' => 'boolean',
                'published_at' => 'nullable|date',
                'author_name' => 'required|string|max:255',
                'author_email' => 'nullable|email|max:255',
                'author_bio' => 'nullable|string|max:1000',
                'tags' => 'nullable|array',
                'tags.*' => 'string|max:50',
                'seo_title' => 'nullable|string|max:255',
                'seo_description' => 'nullable|string|max:500',
                'seo_keywords' => 'nullable|array',
                'seo_keywords.*' => 'string|max:50',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $data = $validator->validated();

            // Generate new slug if title changed
            if ($data['title'] !== $blog->title) {
                $baseSlug = Str::slug($data['title']);
                $slug = $this->generateUniqueSlug($baseSlug, $blog->id);
                $data['slug'] = $slug;
            }

            // Handle featured image upload
            if ($request->hasFile('featured_image')) {
                // Delete old image using helper method
                if ($blog->featured_image) {
                    $this->deleteImage($blog->featured_image);
                }

                $image = $request->file('featured_image');
                $imageName = time() . '_' . Str::random(10) . '.' . $image->getClientOriginalExtension();
                
                // Save to public/blog directory
                $destinationPath = public_path('blog');
                $image->move($destinationPath, $imageName);
                $data['featured_image'] = 'blog/' . $imageName;
            }

            // Generate excerpt if not provided
            if (empty($data['excerpt'])) {
                $data['excerpt'] = Str::limit(strip_tags($data['content']), 200);
            }

            // Set published_at based on status
            if ($data['status'] === 'published' && $blog->status !== 'published' && empty($data['published_at'])) {
                $data['published_at'] = now();
            }

            // Convert arrays to JSON
            if (isset($data['tags'])) {
                $data['tags'] = json_encode($data['tags']);
            }
            if (isset($data['seo_keywords'])) {
                $data['seo_keywords'] = json_encode($data['seo_keywords']);
            }

            $blog->update($data);
            $blog->load('category:id,name');

            return response()->json([
                'success' => true,
                'message' => 'Blog updated successfully',
                'data' => $blog
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Blog not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update blog',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Remove the specified blog
     */
    public function destroy($id): JsonResponse
    {
        try {
            $blog = Blog::findOrFail($id);

            // Delete featured image from public/blog directory
            if ($blog->featured_image) {
                $this->deleteImage($blog->featured_image);
            }

            $blog->delete();

            return response()->json([
                'success' => true,
                'message' => 'Blog deleted successfully'
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Blog not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete blog',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Bulk delete blogs
     */
    public function bulkDelete(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'ids' => 'required|array|min:1',
                'ids.*' => 'integer|exists:blogs,id',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $blogs = Blog::whereIn('id', $request->ids)->get();

            // Delete featured images from public/blog directory
            foreach ($blogs as $blog) {
                if ($blog->featured_image) {
                    $this->deleteImage($blog->featured_image);
                }
            }

            Blog::whereIn('id', $request->ids)->delete();

            return response()->json([
                'success' => true,
                'message' => count($request->ids) . ' blogs deleted successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete blogs',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Update blog status
     */
    public function updateStatus(Request $request, $id): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'status' => 'required|in:draft,published,scheduled',
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422);
            }

            $blog = Blog::findOrFail($id);
            
            $updateData = ['status' => $request->status];

            // Set published_at when publishing
            if ($request->status === 'published' && $blog->status !== 'published') {
                $updateData['published_at'] = now();
            }

            $blog->update($updateData);

            return response()->json([
                'success' => true,
                'message' => 'Blog status updated successfully',
                'data' => $blog
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Blog not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update blog status',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Toggle featured status
     */
    public function toggleFeatured($id): JsonResponse
    {
        try {
            $blog = Blog::findOrFail($id);
            $blog->update(['is_featured' => !$blog->is_featured]);

            return response()->json([
                'success' => true,
                'message' => 'Blog featured status updated successfully',
                'data' => $blog
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Blog not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update featured status',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get blog statistics
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $stats = [
                'total' => Blog::count(),
                'published' => Blog::published()->count(),
                'draft' => Blog::where('status', 'draft')->count(),
                'scheduled' => Blog::where('status', 'scheduled')->count(),
                'featured' => Blog::where('is_featured', true)->count(),
                'total_views' => Blog::sum('views_count'),
                'this_month' => Blog::whereMonth('created_at', now()->month)
                                  ->whereYear('created_at', now()->year)
                                  ->count(),
            ];

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get published blogs for frontend
     */
    public function getPublished(Request $request): JsonResponse
    {
        try {
            $query = Blog::published()->with(['category:id,name']);

            // Search functionality
            if ($request->filled('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('title', 'LIKE', "%{$search}%")
                      ->orWhere('excerpt', 'LIKE', "%{$search}%")
                      ->orWhere('content', 'LIKE', "%{$search}%");
                });
            }

            // Filter by category
            if ($request->filled('category_id')) {
                $query->where('category_id', $request->category_id);
            }

            // Filter featured only
            if ($request->boolean('featured_only')) {
                $query->featured();
            }

            // Order by published date
            $query->orderBy('published_at', 'desc');

            // Pagination
            $perPage = min($request->get('per_page', 10), 50);
            $blogs = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $blogs->items(),
                'meta' => [
                    'current_page' => $blogs->currentPage(),
                    'last_page' => $blogs->lastPage(),
                    'per_page' => $blogs->perPage(),
                    'total' => $blogs->total(),
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch blogs',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get featured blogs
     */
    public function getFeatured(Request $request): JsonResponse
    {
        try {
            $limit = min($request->get('limit', 5), 20);
            
            $blogs = Blog::published()
                        ->featured()
                        ->with(['category:id,name'])
                        ->orderBy('published_at', 'desc')
                        ->limit($limit)
                        ->get();

            return response()->json([
                'success' => true,
                'data' => $blogs
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch featured blogs',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get blog by slug
     */
    public function getBySlug($slug): JsonResponse
    {
        try {
            $blog = Blog::published()
                       ->with(['category:id,name'])
                       ->where('slug', $slug)
                       ->firstOrFail();

            return response()->json([
                'success' => true,
                'data' => $blog
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Blog not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch blog',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Get related blogs
     */
    public function getRelated($id, Request $request): JsonResponse
    {
        try {
            $blog = Blog::published()->findOrFail($id);
            $limit = min($request->get('limit', 3), 10);

            $relatedBlogs = Blog::published()
                               ->where('id', '!=', $blog->id)
                               ->where(function ($query) use ($blog) {
                                   // Same category or similar tags
                                   if ($blog->category_id) {
                                       $query->where('category_id', $blog->category_id);
                                   }
                                   // Add tag-based similarity if needed
                               })
                               ->with(['category:id,name'])
                               ->orderBy('published_at', 'desc')
                               ->limit($limit)
                               ->get();

            return response()->json([
                'success' => true,
                'data' => $relatedBlogs
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Blog not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch related blogs',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Increment blog views
     */
    public function incrementViews($id): JsonResponse
    {
        try {
            $blog = Blog::published()->findOrFail($id);
            $blog->increment('views_count');

            return response()->json([
                'success' => true,
                'views_count' => $blog->views_count
            ]);

        } catch (ModelNotFoundException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Blog not found'
            ], 404);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to increment views',
                'error' => config('app.debug') ? $e->getMessage() : 'Internal server error'
            ], 500);
        }
    }

    /**
     * Generate unique slug
     */
    /**
     * Generate unique slug for blog
     */
    private function generateUniqueSlug(string $baseSlug, $excludeId = null): string
    {
        $slug = $baseSlug;
        $counter = 1;

        while (true) {
            $query = Blog::where('slug', $slug);
            
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }

            if (!$query->exists()) {
                break;
            }

            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Delete featured image from public directory
     */
    private function deleteImage($imagePath): bool
    {
        if ($imagePath) {
            $fullPath = public_path($imagePath);
            if (file_exists($fullPath)) {
                return unlink($fullPath);
            }
        }
        return false;
    }
}
