<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Modules\LandOwners\Models\LandOwner;

class LandAcquisition extends Model
{
    use HasFactory;

    protected $fillable = [
        'dag_number',
        'khatian_number',
        'mauza',
        'land_size',
        'acquisition_price',
        'cs_khatian',
        'rs_khatian',
        'bs_khatian',
        'sa_khatian',
        'land_owner_id',
    ];

    protected $casts = [
        'land_size' => 'decimal:2',
        'acquisition_price' => 'decimal:2',
        'land_owner_id' => 'integer',
    ];



    /**
     * Get the formatted acquisition price
     */
    protected function formattedAcquisitionPrice(): Attribute
    {
        return Attribute::make(
            get: fn () => '৳' . number_format($this->acquisition_price, 2)
        );
    }

    /**
     * Get the formatted land size
     */
    protected function formattedLandSize(): Attribute
    {
        return Attribute::make(
            get: fn () => number_format($this->land_size, 2) . ' decimal'
        );
    }

    /**
     * Relationship with LandOwner
     */
    public function landOwner(): BelongsTo
    {
        return $this->belongsTo(LandOwner::class, 'land_owner_id');
    }

    /**
     * Relationship with LandDocuments
     */
    public function landDocuments(): HasMany
    {
        return $this->hasMany(LandDocument::class);
    }

    /**
     * Relationship with LandAddress
     */
    public function landAddress(): HasOne
    {
        return $this->hasOne(LandAddress::class);
    }

    /**
     * Get the projects associated with this land acquisition
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class, 'land_id');
    }

    /**
     * Scope for filtering by mauza
     */
    public function scopeByMauza($query, $mauza)
    {
        return $query->where('mauza', 'like', "%{$mauza}%");
    }

    /**
     * Scope for filtering by khatian
     */
    public function scopeByKhatian($query, $khatian)
    {
        return $query->where('khatian_number', $khatian);
    }

    /**
     * Scope for filtering by record DAG
     */
    public function scopeByRecordDag($query, $recordDag)
    {
        return $query->where('dag_number', $recordDag);
    }

    /**
     * Scope for filtering by price range
     */
    public function scopeByPriceRange($query, $min, $max)
    {
        return $query->whereBetween('acquisition_price', [$min, $max]);
    }

    /**
     * Scope for filtering by land size range
     */
    public function scopeByLandSizeRange($query, $min, $max)
    {
        return $query->whereBetween('land_size', [$min, $max]);
    }

    /**
     * Get total statistics
     */
    public static function getStatistics(): array
    {
        $totalLandSize = self::sum('land_size') ?: 0;
        $totalAcquisitionValue = self::sum('acquisition_price') ?: 0;

        return [
            'total_records' => self::count(),
            'total_land_size' => number_format($totalLandSize, 2),
            'total_acquisition_value' => $totalAcquisitionValue,
            'average_price_per_decimal' => $totalLandSize > 0 ? ($totalAcquisitionValue / $totalLandSize) : 0,
        ];
    }
}
