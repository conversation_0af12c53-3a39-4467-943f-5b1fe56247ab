<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;

class UnitTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $unitTypes = [
            [
                'name' => 'Studio Apartment',
                'slug' => Str::slug('Studio Apartment'),
                'description' => 'Compact living space combining bedroom, living room, and kitchen.',
                'icon' => 'fa-home', // FontAwesome example
                'color' => '#f39c12', // Orange
                'is_active' => true,
                'sort_order' => 1,
                'created_by' => 1,
            ],
            [
                'name' => '1 Bedroom Apartment',
                'slug' => Str::slug('1 Bedroom Apartment'),
                'description' => 'Apartment with one separate bedroom.',
                'icon' => 'fa-bed',
                'color' => '#27ae60', // Green
                'is_active' => true,
                'sort_order' => 2,
                'created_by' => 1,
            ],
            [
                'name' => '2 Bedroom Apartment',
                'slug' => Str::slug('2 Bedroom Apartment'),
                'description' => 'Apartment with two separate bedrooms.',
                'icon' => 'fa-building',
                'color' => '#2980b9', // Blue
                'is_active' => true,
                'sort_order' => 3,
                'created_by' => 1,
            ],
            [
                'name' => 'Penthouse',
                'slug' => Str::slug('Penthouse'),
                'description' => 'Luxurious top-floor apartment with premium amenities.',
                'icon' => 'fa-crown',
                'color' => '#8e44ad', // Purple
                'is_active' => true,
                'sort_order' => 4,
                'created_by' => 1,
            ],
            [
                'name' => 'Villa',
                'slug' => Str::slug('Villa'),
                'description' => 'Spacious stand-alone house with garden.',
                'icon' => 'fa-warehouse',
                'color' => '#e74c3c', // Red
                'is_active' => true,
                'sort_order' => 5,
                'created_by' => 1,
            ],
        ];

        DB::table('unit_types')->insert($unitTypes);
    }
}
    