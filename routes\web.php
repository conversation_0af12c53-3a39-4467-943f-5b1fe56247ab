
<?php

use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('frontend/index');
})->name('home');
// Admin login page
Route::get('/admin/login', function () {
    return view('admin-login');
})->name('admin.login');
 
// Catch-all route for React Router - this should handle all admin routes
Route::get('/admin/{any}', function () {
    return view('react');
})->where('any', '.*'); // Match all /admin/* routes
