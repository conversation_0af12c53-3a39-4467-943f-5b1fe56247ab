import React from "react";
const SearchItem = ({ widget, handleConfigUpdate }) => {

return(
     <div className="border p-4 rounded-md space-y-4">
                        
                            <div className="flex items-center space-x-2">
                              <label htmlFor={`search-switch-${widget.id}`} className="text-sm font-medium text-gray-700">
                                Enable Property Type Search
                              </label>
                              <input
                                id={`search-switch-${widget.id}`}
                                type="checkbox"
                                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-black"
                                checked={widget.config?.searchEnabled || false}
                                onChange={(e) => handleConfigUpdate(widget.id, "searchEnabled", e.target.checked)}
                              />
                              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1 checked:translate-x-6"></span>
                            </div>

                            <div className="flex items-center space-x-2">
                              <label htmlFor={`search-switch-${widget.id}`} className="text-sm font-medium text-gray-700">
                                Enable Location Search
                              </label>
                              <input
                                id={`search-switch-${widget.id}`}
                                type="checkbox"
                                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-black"
                                checked={widget.config?.locationsearchEnabled || false}
                                onChange={(e) => handleConfigUpdate(widget.id, "locationsearchEnabled", e.target.checked)}
                              />
                              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1 checked:translate-x-6"></span>
                            </div>

                            <div className="flex items-center space-x-2">
                              <label htmlFor={`search-switch-${widget.id}`} className="text-sm font-medium text-gray-700">
                                Enable BedRoom Search
                              </label>
                              <input
                                id={`search-switch-${widget.id}`}
                                type="checkbox"
                                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-black"
                                checked={widget.config?.bedRoomsearchEnabled || false}
                                onChange={(e) => handleConfigUpdate(widget.id, "bedRoomsearchEnabled", e.target.checked)}
                              />
                              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1 checked:translate-x-6"></span>
                            </div>

                            <div className="flex items-center space-x-2">
                              <label htmlFor={`search-switch-${widget.id}`} className="text-sm font-medium text-gray-700">
                                Enable Price Search
                              </label>
                              <input
                                id={`search-switch-${widget.id}`}
                                type="checkbox"
                                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-black"
                                checked={widget.config?.pricesearchEnabled || false}
                                onChange={(e) => handleConfigUpdate(widget.id, "pricesearchEnabled", e.target.checked)}
                              />
                              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1 checked:translate-x-6"></span>
                            </div>
                          </div>
)};


export default SearchItem;
