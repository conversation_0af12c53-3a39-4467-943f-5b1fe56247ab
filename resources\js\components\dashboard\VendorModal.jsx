import React from 'react';
import { X, Plus, Edit2, Eye, Package, Phone, Mail, MapPin, UserCheck, UserX } from 'lucide-react';
import { showAlert } from '../../utils/sweetAlert';

const VendorModal = ({
  showModal,
  closeModal,
  modalMode,
  selectedVendor,
  formData,
  handleInputChange,
  handleSubmit,
  submitting,
  vendorTypes
}) => {
  if (!showModal) return null;

  return (
    <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center p-4 z-50 animate-fadeIn">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-lg max-h-[90vh] overflow-hidden transform transition-all duration-300 animate-slideIn">
        {/* Modal Header */}
        <div className="relative bg-gradient-to-r from-black to-gray-900 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-white/20 rounded-lg backdrop-blur-sm">
                {modalMode === 'create' ? (
                  <Plus className="w-5 h-5 text-white" />
                ) : modalMode === 'edit' ? (
                  <Edit2 className="w-5 h-5 text-white" />
                ) : (
                  <Eye className="w-5 h-5 text-white" />
                )}
              </div>
              <h3 className="text-lg font-semibold text-white">
                {modalMode === 'create' ? 'Add New Vendor' : 
                 modalMode === 'edit' ? 'Edit Vendor' : 'Vendor Details'}
              </h3>
            </div>
            <button
              onClick={closeModal}
              className="p-2 hover:bg-white/20 rounded-lg transition-colors duration-200 group"
            >
              <X className="w-5 h-5 text-white group-hover:rotate-90 transition-transform duration-200" />
            </button>
          </div>
          <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-blue-300 to-blue-400"></div>
        </div>

        {/* Modal Body */}
        <div className="p-6 max-h-[calc(90vh-120px)] overflow-y-auto">
          {modalMode === 'view' ? (
            <div className="space-y-6">
              {/* Vendor Avatar/Icon */}
              <div className="flex justify-center mb-6">
                <div className="w-20 h-20 bg-gradient-to-br from-black to-gray-900 rounded-full flex items-center justify-center shadow-lg">
                  <Package className="w-10 h-10 text-white" />
                </div>
              </div>

              {/* Vendor Details */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      Vendor Name
                    </label>
                    <p className="text-lg font-semibold text-gray-900 dark:text-white">{selectedVendor?.name}</p>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      <Phone className="inline w-4 h-4 mr-1" />
                      Phone Number
                    </label>
                    <p className="text-gray-900 dark:text-white">{selectedVendor?.phone || 'Not provided'}</p>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      <Mail className="inline w-4 h-4 mr-1" />
                      Email Address
                    </label>
                    <p className="text-gray-900 dark:text-white break-all">{selectedVendor?.email || 'Not provided'}</p>
                  </div>
                </div>

                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      Vendor Type
                    </label>
                    <span className="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                      <Package className="w-4 h-4 mr-1" />
                      {selectedVendor?.vendor_type?.name || 'Not specified'}
                    </span>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
                    <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                      Status
                    </label>
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                      selectedVendor?.status === 'active' 
                        ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' 
                        : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}>
                      {selectedVendor?.status === 'active' ? (
                        <UserCheck className="w-4 h-4 mr-1" />
                      ) : (
                        <UserX className="w-4 h-4 mr-1" />
                      )}
                      {selectedVendor?.status}
                    </span>
                  </div>
                </div>
              </div>

              {selectedVendor?.address && (
                <div className="bg-gray-50 dark:bg-gray-700 rounded-xl p-4 border border-gray-200 dark:border-gray-600">
                  <label className="block text-sm font-medium text-gray-600 dark:text-gray-400 mb-2">
                    <MapPin className="inline w-4 h-4 mr-1" />
                    Address
                  </label>
                  <p className="text-gray-900 dark:text-white leading-relaxed">{selectedVendor?.address}</p>
                </div>
              )}
            </div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-6">
             {/* Form Header */}
                                       <div className="text-center pb-4 border-b border-gray-200 dark:border-gray-700">
                                        <div className="w-16 h-16 bg-gradient-to-br from-black to-gray-900 rounded-full flex items-center justify-center mx-auto mb-3 shadow-lg">
                                           <Package className="w-8 h-8 text-white" />
                                         </div>
                                         <p className="text-gray-600 dark:text-gray-400">
                                           {modalMode === 'create' ? 'Create a new vendor profile' : 'Update vendor information'}
                                         </p>
                                       </div>
                     
                                       {/* Form Fields */}
                                       <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                         <div className="md:col-span-2">
                                           <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                             Vendor Name *
                                           </label>
                                           <div className="relative">
                                             <input
                                               type="text"
                                               name="name"
                                               value={formData.name}
                                               onChange={handleInputChange}
                                               required
                                               className="w-full pl-4 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                                               placeholder="Enter vendor name"
                                             />
                                           </div>
                                         </div>
                     
                                         <div>
                                           <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                             Phone Number
                                           </label>
                                           <div className="relative">
                                             <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                             <input
                                               type="tel"
                                               name="phone"
                                               value={formData.phone}
                                               onChange={handleInputChange}
                                               className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                                               placeholder="Enter phone number"
                                             />
                                           </div>
                                         </div>
                     
                                         <div>
                                           <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                             Email Address
                                           </label>
                                           <div className="relative">
                                             <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                             <input
                                               type="email"
                                               name="email"
                                               value={formData.email}
                                               onChange={handleInputChange}
                                               className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md"
                                               placeholder="Enter email address"
                                             />
                                           </div>
                                         </div>
                     
                                         <div>
                                           <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                             Vendor Type *
                                           </label>
                                           <div className="relative">
                                             <Package className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                                             <select
                                               name="vendor_type_id"
                                               value={formData.vendor_type_id}
                                               onChange={handleInputChange}
                                               required
                                               className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md appearance-none"
                                             >
                                               <option value="">Select vendor type</option>
                                               {vendorTypes.map(type => (
                                                 <option key={type.id} value={type.id}>{type.name}</option>
                                               ))}
                                             </select>
                                           </div>
                                         </div>
                     
                                         <div>
                                           <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                             Status
                                           </label>
                                           <div className="relative">
                                             <select
                                               name="status"
                                               value={formData.status}
                                               onChange={handleInputChange}
                                               className="w-full px-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md appearance-none"
                                             >
                                               <option value="active">Active</option>
                                               <option value="inactive">Inactive</option>
                                             </select>
                                           </div>
                                         </div>
                     
                                         <div className="md:col-span-2">
                                           <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                                             Address
                                           </label>
                                           <div className="relative">
                                             <MapPin className="absolute left-3 top-3 text-gray-400 w-4 h-4" />
                                             <textarea
                                               name="address"
                                               value={formData.address}
                                               onChange={handleInputChange}
                                               rows={3}
                                               className="w-full pl-10 pr-4 py-3 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-white transition-all duration-200 shadow-sm hover:shadow-md resize-none"
                                               placeholder="Enter vendor address"
                                             />
                                           </div>
                                         </div>
                                       </div>
                     
                                       {/* Form Actions */}
                                       <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                                         <button
                                           type="button"
                                           onClick={closeModal}
                                           className="px-6 py-3 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-xl font-medium transition-all duration-200 shadow-sm hover:shadow-md"
                                         >
                                           Cancel
                                         </button>
                                         <button
                                           type="submit"
                                           disabled={submitting}
                                               className={`px-6 py-3 rounded-xl font-medium transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-0.5 ${
                                             submitting 
                                               ? 'bg-gray-400 cursor-not-allowed' 
                                               : 'bg-gradient-to-r from-black to-gray-900 hover:from-gray-900 hover:to-black'
                                           } text-white`}
                                         >
                                           {submitting ? (
                                             <div className="flex items-center space-x-2">
                                               <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                                               <span>Submitting...</span>
                                             </div>
                                           ) : (
                                             modalMode === 'create' ? 'Create Vendor' : 'Update Vendor'
                                           )}
                                         </button>
                                       </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default VendorModal;
