import{j as e,R as N,L as v,r as t,l as B,u as k,e as S,k as Z,m as A,p as I,q as P,B as V,n as T,o as E}from"./frontendSettingsAPI-b918c411.js";const U=({frontendSettings:s})=>s.showPreloader==1&&e.jsxs("div",{className:"egns-preloader",children:[e.jsx("div",{className:"preloader-close-btn",children:e.jsxs("span",{children:[e.jsx("i",{className:"bi bi-x-lg"})," Close"]})}),e.jsx("div",{className:"container",children:e.jsx("div",{className:"row d-flex justify-content-center",children:e.jsx("div",{className:"col-6",children:e.jsxs("div",{className:"circle-border",children:[e.jsx("div",{className:"moving-circle"}),e.jsx("div",{className:"moving-circle"}),e.jsx("div",{className:"moving-circle"}),e.jsxs("svg",{width:"180px",height:"150px",viewBox:"0 0 187.3 93.7",preserveAspectRatio:"xMidYMid meet",style:{left:"50%",top:"50%",position:"absolute",transform:"translate(-50%, -50%) matrix(1, 0, 0, 1, 0, 0)"},children:[e.jsx("path",{stroke:"#D90A2C",id:"outline",fill:"none",strokeWidth:"4",strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",d:"M93.9,46.4c9.3,9.5,13.8,17.9,23.5,17.9s17.5-7.8,17.5-17.5s-7.8-17.6-17.5-17.5c-9.7,0.1-13.3,7.2-22.1,17.1 c-8.9,8.8-15.7,17.9-25.4,17.9s-17.5-7.8-17.5-17.5s7.8-17.5,17.5-17.5S86.2,38.6,93.9,46.4z"}),e.jsx("path",{id:"outline-bg",opacity:"0.05",fill:"none",stroke:"#959595",strokeWidth:"4",strokeLinecap:"round",strokeLinejoin:"round",strokeMiterlimit:"10",d:"M93.9,46.4c9.3,9.5,13.8,17.9,23.5,17.9s17.5-7.8,17.5-17.5s-7.8-17.6-17.5-17.5c-9.7,0.1-13.3,7.2-22.1,17.1 c-8.9,8.8-15.7,17.9-25.4,17.9s-17.5-7.8-17.5-17.5s7.8-17.5,17.5-17.5S86.2,38.6,93.9,46.4z"})]})]})})})})]}),_=()=>e.jsx("div",{className:"modal signUp-modal two fade",id:"signUpModal01",tabIndex:"-1","aria-labelledby":"signUpModal01Label","aria-hidden":"true",children:e.jsx("div",{className:"modal-dialog modal-dialog-centered",children:e.jsx("div",{className:"modal-content",children:e.jsx("div",{className:"modal-body",children:e.jsxs("div",{className:"login-wrapper",children:[e.jsxs("div",{className:"login-img",children:[e.jsx("img",{src:"/Frontend/assets/img/home1/login-img.png",alt:"login"}),e.jsx("div",{className:"logo",children:e.jsx("a",{href:"index.html",children:e.jsx("img",{src:"/Frontend/assets/img/logo.svg",alt:"logo"})})})]}),e.jsxs("div",{className:"login-content",children:[e.jsxs("div",{className:"login-header",children:[e.jsx("h4",{className:"modal-title",id:"signUpModal01Label",children:"Sign Up"}),e.jsxs("p",{children:["Already have an account?"," ",e.jsx("button",{type:"button","data-bs-toggle":"modal","data-bs-target":"#logInModal01",children:"Log In"})]}),e.jsx("button",{type:"button",className:"btn-close","data-bs-dismiss":"modal","aria-label":"Close",children:e.jsx("i",{className:"bi bi-x"})})]}),e.jsxs("form",{children:[e.jsxs("div",{className:"row g-4",children:[e.jsx("div",{className:"col-md-6",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"First Name*"}),e.jsx("input",{type:"text",placeholder:"Daniel"})]})}),e.jsx("div",{className:"col-md-6",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Last Name*"}),e.jsx("input",{type:"text",placeholder:"Last name"})]})}),e.jsx("div",{className:"col-md-12",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Enter your email address*"}),e.jsx("input",{type:"email",placeholder:"Type email"})]})}),e.jsx("div",{className:"col-md-6",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Password*"}),e.jsx("input",{id:"password",type:"password",placeholder:"*** ***"}),e.jsx("i",{className:"bi bi-eye-slash",id:"togglePassword"})]})}),e.jsx("div",{className:"col-md-6",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Confirm Password*"}),e.jsx("input",{id:"password2",type:"password",placeholder:"*** ***"}),e.jsx("i",{className:"bi bi-eye-slash",id:"togglePassword2"})]})}),e.jsx("div",{className:"col-md-12",children:e.jsx("div",{className:"form-inner",children:e.jsx("button",{className:"primary-btn2",type:"submit",children:"Sign Up Now"})})})]}),e.jsx("div",{className:"terms-conditon",children:e.jsxs("p",{children:["By sign up, you agree to the"," ",e.jsx("a",{href:"#",children:"‘terms & conditions’"})]})}),e.jsxs("ul",{className:"social-icon",children:[e.jsx("li",{children:e.jsx("a",{href:"#",children:e.jsx("img",{src:"/Frontend/assets/img/home1/icon/google.svg",alt:"Google"})})}),e.jsx("li",{children:e.jsx("a",{href:"#",children:e.jsx("img",{src:"/Frontend/assets/img/home1/icon/facebook.svg",alt:"Facebook"})})}),e.jsx("li",{children:e.jsx("a",{href:"#",children:e.jsx("img",{src:"/Frontend/assets/img/home1/icon/twiter.svg",alt:"Twitter"})})})]})]})]})]})})})})}),R=()=>e.jsx("div",{className:"modal signUp-modal two fade",id:"logInModal01",tabIndex:"-1","aria-labelledby":"logInModal01Label","aria-hidden":"true",children:e.jsx("div",{className:"modal-dialog modal-xl modal-dialog-centered",children:e.jsx("div",{className:"modal-content",children:e.jsx("div",{className:"modal-body",children:e.jsxs("div",{className:"login-wrapper",children:[e.jsxs("div",{className:"login-img",children:[e.jsx("img",{src:"/Frontend/assets/img/home1/login-img.png",alt:"login"}),e.jsx("div",{className:"logo",children:e.jsx("a",{href:"index.html",children:e.jsx("img",{src:"/Frontend/assets/img/logo.svg",alt:"logo"})})})]}),e.jsxs("div",{className:"login-content",children:[e.jsxs("div",{className:"login-header",children:[e.jsx("h4",{className:"modal-title",id:"logInModal01Label",children:"Log In"}),e.jsxs("p",{children:["Don’t have any account?"," ",e.jsx("button",{type:"button","data-bs-toggle":"modal","data-bs-target":"#signUpModal01",children:"Sign Up"})]}),e.jsx("button",{type:"button",className:"btn-close","data-bs-dismiss":"modal","aria-label":"Close",children:e.jsx("i",{className:"bi bi-x"})})]}),e.jsxs("form",{children:[e.jsxs("div",{className:"row g-4",children:[e.jsx("div",{className:"col-md-12",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Enter your email address*"}),e.jsx("input",{type:"email",placeholder:"Type email"})]})}),e.jsx("div",{className:"col-md-12",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Password*"}),e.jsx("input",{id:"password3",type:"password",placeholder:"*** ***"}),e.jsx("i",{className:"bi bi-eye-slash",id:"togglePassword3"})]})}),e.jsx("div",{className:"col-lg-12",children:e.jsxs("div",{className:"form-agreement form-inner d-flex justify-content-between flex-wrap",children:[e.jsxs("div",{className:"form-group",children:[e.jsx("input",{type:"checkbox",id:"rememberMe"}),e.jsx("label",{htmlFor:"rememberMe",children:"Remember Me"})]}),e.jsx("a",{href:"#",className:"forgot-pass",children:"Forget Password?"})]})}),e.jsx("div",{className:"col-md-12",children:e.jsx("div",{className:"form-inner",children:e.jsx("button",{className:"primary-btn2",type:"submit",children:"Log In"})})})]}),e.jsx("div",{className:"terms-conditon",children:e.jsxs("p",{children:["By sign up, you agree to the"," ",e.jsx("a",{href:"#",children:"‘terms & conditions’"})]})}),e.jsxs("ul",{className:"social-icon",children:[e.jsx("li",{children:e.jsx("a",{href:"#",children:e.jsx("img",{src:"/Frontend/assets/img/home1/icon/google.svg",alt:"Google"})})}),e.jsx("li",{children:e.jsx("a",{href:"#",children:e.jsx("img",{src:"/Frontend/assets/img/home1/icon/facebook.svg",alt:"Facebook"})})}),e.jsx("li",{children:e.jsx("a",{href:"#",children:e.jsx("img",{src:"/Frontend/assets/img/home1/icon/twiter.svg",alt:"Twitter"})})})]})]})]})]})})})})}),W=()=>e.jsx("div",{className:"modal adSearch-modal fade",id:"adSearchModal01",tabIndex:"-1","aria-hidden":"true",children:e.jsx("div",{className:"modal-dialog modal-lg modal-dialog-centered",children:e.jsxs("div",{className:"modal-content",children:[e.jsx("button",{type:"button",className:"btn-close","data-bs-dismiss":"modal","aria-label":"Close",children:e.jsx("i",{className:"bi bi-x"})}),e.jsx("div",{className:"modal-body",children:e.jsxs("form",{children:[e.jsx("h5",{className:"main-title",children:"Advanced Option"}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-md-12 mb-30",children:e.jsx("div",{className:"form-inner",children:e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"Sydne City, Australia"}),e.jsx("option",{value:"2",children:"Dhaka, Bangladesh"}),e.jsx("option",{value:"3",children:"Tokyo, Japan"})]})})}),e.jsx("h5",{children:"More Filter"}),e.jsxs("div",{className:"row mb-10",children:[e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Select For"}),e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"Rent"}),e.jsx("option",{value:"2",children:"Sale"}),e.jsx("option",{value:"3",children:"Buy"})]})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Property Type"}),e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"Health Care"}),e.jsx("option",{value:"2",children:"Development"}),e.jsx("option",{value:"3",children:"Industrial"}),e.jsx("option",{value:"4",children:"Home Town"}),e.jsx("option",{value:"5",children:"Bungalow"}),e.jsx("option",{value:"6",children:"House"})]})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Build Year"}),e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"2015"}),e.jsx("option",{value:"2",children:"2016"}),e.jsx("option",{value:"3",children:"2017"}),e.jsx("option",{value:"4",children:"2018"})]})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Condition"}),e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"Used"}),e.jsx("option",{value:"2",children:"New"})]})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Property Size"}),e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"1200sqf"}),e.jsx("option",{value:"2",children:"1500sqf"}),e.jsx("option",{value:"3",children:"2400sqf"}),e.jsx("option",{value:"4",children:"2500sqf"})]})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Number Of Room"}),e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"1"}),e.jsx("option",{value:"2",children:"2"}),e.jsx("option",{value:"3",children:"3"}),e.jsx("option",{value:"4",children:"4"}),e.jsx("option",{value:"5",children:"5"})]})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Number Of Bath"}),e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"1"}),e.jsx("option",{value:"2",children:"2"}),e.jsx("option",{value:"3",children:"3"}),e.jsx("option",{value:"4",children:"4"}),e.jsx("option",{value:"5",children:"5"})]})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Property Face"}),e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"North"}),e.jsx("option",{value:"2",children:"South"}),e.jsx("option",{value:"3",children:"East"}),e.jsx("option",{value:"4",children:"West"}),e.jsx("option",{value:"5",children:"Other"})]})]})})]}),e.jsx("h5",{className:"mb-20",children:"Price Range"}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-lg-6 mb-20",children:e.jsxs("div",{className:"range-wrapper2",children:[e.jsx("div",{className:"slider-wrapper",children:e.jsx("div",{id:"eg-range-slider"})}),e.jsxs("div",{className:"valus",children:[e.jsxs("div",{className:"min-value",children:[e.jsx("span",{children:"$"}),e.jsx("input",{type:"text",className:"from",value:"200",readOnly:!0})]}),e.jsxs("div",{className:"min-value",children:[e.jsx("span",{children:"$"}),e.jsx("input",{type:"text",className:"to",value:"2000",readOnly:!0})]})]})]})}),e.jsx("div",{className:"col-md-3 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Min (Price)"}),e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"$2,234"}),e.jsx("option",{value:"2",children:"$3,234"}),e.jsx("option",{value:"3",children:"$4,234"}),e.jsx("option",{value:"4",children:"$5,234"})]})]})}),e.jsx("div",{className:"col-md-3 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Max (Price)"}),e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"$12,234"}),e.jsx("option",{value:"2",children:"$13,234"}),e.jsx("option",{value:"3",children:"$14,234"}),e.jsx("option",{value:"4",children:"$15,234"})]})]})})]})]}),e.jsx("div",{className:"apply-btn pt-30",children:e.jsx("button",{className:"primary-btn2",type:"submit",children:"Apply Filter"})})]})})]})})}),$=()=>e.jsx("div",{className:"modal signUp-modal sell-with-us fade",id:"sellUsModal01",tabIndex:"-1","aria-labelledby":"sellUsModal01Label","aria-hidden":"true",children:e.jsx("div",{className:"modal-dialog modal-dialog-centered",children:e.jsxs("div",{className:"modal-content",children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h4",{className:"modal-title",id:"sellUsModal01Label",children:"Sell Your Car"}),e.jsx("button",{type:"button",className:"btn-close","data-bs-dismiss":"modal","aria-label":"Close",children:e.jsx("i",{className:"bi bi-x"})})]}),e.jsx("div",{className:"modal-body",children:e.jsxs("form",{children:[e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-lg-12 mb-15",children:e.jsx("h5",{children:"Your Personal Info"})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Full Name*"}),e.jsx("input",{type:"text",placeholder:"Full Name*"})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Phone*"}),e.jsx("input",{type:"text",placeholder:"+880- 123 234 ***"})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Email (Optional)"}),e.jsx("input",{type:"text",placeholder:"Enter your email address"})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Location*"}),e.jsx("input",{type:"text",placeholder:"Enter your address"})]})})]}),e.jsxs("div",{className:"row",children:[e.jsx("div",{className:"col-lg-12 mb-15 mt-25",children:e.jsx("h5",{children:"Your Car Info"})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Car Brand Name*"}),e.jsx("input",{type:"text",placeholder:"Toyota"})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Model*"}),e.jsx("input",{type:"text",placeholder:"RS eTN 80"})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Reg. Year*"}),e.jsx("input",{type:"text",placeholder:"2022"})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Mileage*"}),e.jsx("input",{type:"text",placeholder:"23,456 miles"})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Fuel Type*"}),e.jsx("input",{type:"text",placeholder:"Petrol"})]})}),e.jsx("div",{className:"col-md-6 mb-20",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Selling Price*"}),e.jsx("input",{type:"text",placeholder:"Ex- $23,342.000"})]})}),e.jsx("div",{className:"col-md-12 mb-35",children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Your Car Note*"}),e.jsx("textarea",{placeholder:"Write somethings"})]})}),e.jsx("div",{className:"col-lg-12",children:e.jsx("div",{className:"form-inner",children:e.jsx("button",{className:"primary-btn2",type:"submit",children:"Submit Now"})})})]})]})})]})})}),D=()=>e.jsxs("div",{children:[e.jsx(_,{}),e.jsx(R,{}),e.jsx(W,{}),e.jsx($,{})]}),O=()=>e.jsxs("div",{className:"sidebar-menu",children:[e.jsxs("div",{className:"mobile-logo-area d-flex justify-content-between align-items-center",children:[e.jsx("div",{className:"mobile-logo-wrap",children:e.jsx("a",{href:"index.html",children:e.jsx("img",{alt:"image",src:"/Frontend/assets/img/sb-logo.svg"})})}),e.jsx("div",{className:"menu-button menu-close-btn",children:e.jsx("i",{className:"bi bi-x"})})]}),e.jsxs("ul",{className:"menu-list",children:[e.jsxs("li",{className:"menu-item-has-children active",children:[e.jsx("a",{href:"#",className:"drop-down",children:"Home"}),e.jsx("i",{className:"bi bi-plus dropdown-icon"}),e.jsxs("ul",{className:"sub-menu",children:[e.jsx("li",{children:e.jsx("a",{href:"index.html",children:"Home 01"})}),e.jsx("li",{children:e.jsx("a",{href:"index2.html",children:"Home 02"})}),e.jsx("li",{children:e.jsx("a",{href:"index3.html",children:"Home 03"})}),e.jsx("li",{children:e.jsx("a",{href:"index4.html",children:"Home 04"})}),e.jsx("li",{children:e.jsx("a",{href:"index5.html",children:"Home 05"})}),e.jsx("li",{children:e.jsx("a",{href:"index6.html",children:"Home 06"})})]})]}),e.jsxs("li",{className:"position-inherit",children:[e.jsx("a",{href:"#",className:"drop-down",children:"FOR SALE"}),e.jsx("i",{className:"bi bi-plus dropdown-icon"}),e.jsx("div",{className:"mega-menu",children:e.jsxs("ul",{className:"menu-row",children:[e.jsxs("li",{className:"menu-single-item",children:[e.jsx("h6",{children:"Apartment Types"}),e.jsxs("ul",{children:[e.jsx("li",{children:e.jsx("a",{href:"poperty-listing-no-sidebar.html",children:"Houses (10)"})}),e.jsx("li",{children:e.jsx("a",{href:"poperty-listing-no-sidebar.html",children:"Industires (13)"})}),e.jsx("li",{children:e.jsx("a",{href:"poperty-listing-no-sidebar.html",children:"Home Twon (33)"})}),e.jsx("li",{children:e.jsx("a",{href:"poperty-listing-no-sidebar.html",children:"Development (15)"})}),e.jsx("li",{children:e.jsx("a",{href:"poperty-listing-no-sidebar.html",children:"Health Care (20)"})}),e.jsx("li",{children:e.jsx("a",{href:"poperty-listing-no-sidebar.html",children:"Office (10)"})}),e.jsx("li",{children:e.jsx("a",{href:"poperty-listing-no-sidebar.html",children:"Banglow (10)"})}),e.jsx("li",{children:e.jsx("a",{href:"poperty-listing-no-sidebar.html",children:"Hotel (10)"})}),e.jsx("li",{className:"explore-more-btn",children:e.jsxs("a",{href:"poperty-listing-no-sidebar.html",children:["Explore More ",e.jsx("i",{className:"bi bi-arrow-right-short"})]})})]})]}),e.jsxs("li",{className:"menu-single-item",children:[e.jsx("h6",{children:"Popular Cities "}),e.jsxs("ul",{children:[e.jsx("li",{children:e.jsxs("a",{href:"poperty-listing-no-sidebar.html",children:["Panama City (10)",e.jsx("img",{src:"/Frontend/assets/img/menu-icon/panama.svg",alt:""})]})}),e.jsx("li",{children:e.jsxs("a",{href:"poperty-listing-no-sidebar.html",children:["Sydne City (10)",e.jsx("img",{src:"/Frontend/assets/img/menu-icon/sydne.svg",alt:""})]})}),e.jsx("li",{children:e.jsxs("a",{href:"poperty-listing-no-sidebar.html",children:["Melbourne City (10)",e.jsx("img",{src:"/Frontend/assets/img/menu-icon/melbourne.svg",alt:""})]})}),e.jsx("li",{children:e.jsxs("a",{href:"poperty-listing-no-sidebar.html",children:["New Delhi (10)",e.jsx("img",{src:"/Frontend/assets/img/menu-icon/delhi.svg",alt:""})]})}),e.jsx("li",{children:e.jsxs("a",{href:"poperty-listing-no-sidebar.html",children:["New York (10)",e.jsx("img",{src:"/Frontend/assets/img/menu-icon/newYork.svg",alt:""})]})}),e.jsx("li",{children:e.jsxs("a",{href:"poperty-listing-no-sidebar.html",children:["Menchester City (10)",e.jsx("img",{src:"/Frontend/assets/img/menu-icon/menchester.svg",alt:""})]})}),e.jsx("li",{children:e.jsxs("a",{href:"poperty-listing-no-sidebar.html",children:["City of Greece (10)",e.jsx("img",{src:"/Frontend/assets/img/menu-icon/greece.svg",alt:""})]})}),e.jsx("li",{children:e.jsxs("a",{href:"poperty-listing-no-sidebar.html",children:["City of Abu-dabi (10)",e.jsx("img",{src:"/Frontend/assets/img/menu-icon/abudabi.svg",alt:""})]})}),e.jsx("li",{className:"explore-more-btn",children:e.jsxs("a",{href:"poperty-listing-no-sidebar.html",children:["Explore More ",e.jsx("i",{className:"bi bi-arrow-right-short"})]})})]})]})]})})]})]}),e.jsxs("div",{className:"topbar-right",children:[e.jsx("button",{type:"button",className:"modal-btn header-user-btn sell-btn","data-bs-toggle":"modal","data-bs-target":"#logInModal01",children:"REGISTER/ LOGIN"}),e.jsx("a",{className:"primary-btn3",href:"dashboard-add-property.html",children:"ADD PROPERTY"})]})]}),q=()=>e.jsx(e.Fragment,{children:e.jsxs("div",{className:"search-bar",children:[e.jsx("div",{className:"close-btn",children:e.jsx("i",{className:"bi bi-x"})}),e.jsxs("div",{className:"filter-wrap",children:[e.jsxs("form",{children:[e.jsx("div",{className:"form-inner",children:e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"For Sale"}),e.jsx("option",{value:"2",children:"For Rent"})]})}),e.jsx("div",{className:"form-inner",children:e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"Property Type"}),e.jsx("option",{value:"2",children:"Health Care"}),e.jsx("option",{value:"3",children:"Development"}),e.jsx("option",{value:"4",children:"Industrial"}),e.jsx("option",{value:"5",children:"Homw Town"}),e.jsx("option",{value:"6",children:"Banglow"})]})}),e.jsx("div",{className:"form-inner",children:e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"Location"}),e.jsx("option",{value:"2",children:"Sydne City"}),e.jsx("option",{value:"3",children:"Chicago City"}),e.jsx("option",{value:"4",children:"New Delhi"}),e.jsx("option",{value:"5",children:"Sydne City"})]})}),e.jsx("div",{className:"form-inner",children:e.jsxs("select",{children:[e.jsx("option",{value:"1",children:"Budget"}),e.jsx("option",{value:"2",children:"10000-15000"}),e.jsx("option",{value:"3",children:"15000-20000"}),e.jsx("option",{value:"4",children:"20000-25000"}),e.jsx("option",{value:"5",children:"25000-300"})]})}),e.jsx("div",{className:"form-inner",children:e.jsxs("button",{className:"primary-btn3",type:"submit",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",children:e.jsx("path",{d:"M10.2746 9.04904C11.1219 7.89293 11.5013 6.45956 11.3371 5.0357C11.1729 3.61183 10.4771 2.30246 9.38898 1.36957C8.30083 0.436668 6.90056 -0.050966 5.46831 0.00422091C4.03607 0.0594078 2.67747 0.653346 1.66433 1.66721C0.651194 2.68107 0.0582276 4.04009 0.00406556 5.47238C-0.0500965 6.90466 0.43854 8.30458 1.37222 9.39207C2.30589 10.4795 3.61575 11.1744 5.03974 11.3376C6.46372 11.5008 7.89682 11.1203 9.05232 10.2722H9.05145C9.07769 10.3072 9.10569 10.3405 9.13719 10.3729L12.5058 13.7415C12.6699 13.9057 12.8924 13.9979 13.1245 13.998C13.3566 13.9981 13.5793 13.906 13.7435 13.7419C13.9076 13.5779 13.9999 13.3553 14 13.1232C14.0001 12.8911 13.908 12.6685 13.7439 12.5043L10.3753 9.13566C10.344 9.104 10.3104 9.07562 10.2746 9.04904ZM10.5004 5.68567C10.5004 6.31763 10.3759 6.9434 10.1341 7.52726C9.89223 8.11112 9.53776 8.64162 9.0909 9.08849C8.64403 9.53535 8.11352 9.88983 7.52967 10.1317C6.94581 10.3735 6.32003 10.498 5.68807 10.498C5.05611 10.498 4.43034 10.3735 3.84648 10.1317C3.26262 9.88983 2.73211 9.53535 2.28525 9.08849C1.83838 8.64162 1.48391 8.11112 1.24207 7.52726C1.00023 6.9434 0.875753 6.31763 0.875753 5.68567C0.875753 4.40936 1.38276 3.18533 2.28525 2.28284C3.18773 1.38036 4.41177 0.873346 5.68807 0.873346C6.96438 0.873346 8.18841 1.38036 9.0909 2.28284C9.99338 3.18533 10.5004 4.40936 10.5004 5.68567Z"})}),"Search"]})})]}),e.jsx("div",{className:"advanced-btn",children:e.jsxs("button",{type:"button","data-bs-toggle":"modal","data-bs-target":"#adSearchModal01",children:["Advanced Filter",e.jsx("svg",{width:"13",height:"10",viewBox:"0 0 13 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M3.48336 0V8.0272L4.16668 7.36221L4.91394 8.09055L2.95489 10L0.99585 8.09055L1.74311 7.36221L2.42642 8.0272V0H3.48336ZM8.23961 7.72638V8.75657H5.59725V7.72638H8.23961ZM9.82502 5.15092V6.18111H5.59725V5.15092H9.82502ZM11.4104 2.57546V3.60565H5.59725V2.57546H11.4104ZM12.9958 0V1.03018H5.59725V0H12.9958Z"})})]})})]})]})}),z=({frontendSettings:s})=>{const[c,i]=N.useState([]),[d,o]=N.useState([]);return N.useEffect(()=>{fetch("/api/menu-manages?menu_position_id=1").then(a=>a.json()).then(a=>{a.success&&Array.isArray(a.data)&&i(a.data)}),fetch("/api/menu-manages?menu_position_id=2").then(a=>a.json()).then(a=>{a.success&&Array.isArray(a.data)&&o(a.data)})},[]),e.jsxs("div",{className:"topbar-header",children:[s.showTopBar===1&&e.jsxs("div",{className:"top-bar style-2",children:[e.jsx("div",{className:"content text-white",children:e.jsxs("div",{className:"icon",children:[e.jsxs("svg",{width:"28",height:"28",viewBox:"0 0 16 16",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M2.5 3H13.5C14.3284 3 15 3.67157 15 4.5V11.5C15 12.3284 14.3284 13 13.5 13H2.5C1.67157 13 1 12.3284 1 11.5V4.5C1 3.67157 1.67157 3 2.5 3Z",stroke:"white","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"}),e.jsx("path",{d:"M15 4.5L8 8.5L1 4.5",stroke:"white","stroke-width":"1.5","stroke-linecap":"round","stroke-linejoin":"round"})]}),e.jsx("span",{children:"Get In Touch"}),e.jsx("h6",{children:e.jsx("a",{href:`mailto:${s==null?void 0:s.footerEmail}`,className:"text-white",children:s==null?void 0:s.footerEmail})})]})}),e.jsx("div",{className:"top-bar-items",children:e.jsx("ul",{children:c.map(a=>e.jsx("li",{children:e.jsx(v,{to:a.slug==="home"?"/":`/${a.slug}`,children:a.title})},a.id))})}),e.jsx("div",{className:"topbar-right",children:e.jsxs("div",{className:"hotline-area d-md-flex d-none",children:[e.jsx("div",{className:"icon",children:e.jsx("svg",{width:"28",height:"28",viewBox:"0 0 28 28",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M27.2653 21.5995L21.598 17.8201C20.8788 17.3443 19.9147 17.5009 19.383 18.1798L17.7322 20.3024C17.6296 20.4377 17.4816 20.5314 17.3154 20.5664C17.1492 20.6014 16.9759 20.5752 16.8275 20.4928L16.5134 20.3196C15.4725 19.7522 14.1772 19.0458 11.5675 16.4352C8.95784 13.8246 8.25001 12.5284 7.6826 11.4893L7.51042 11.1753C7.42683 11.0269 7.39968 10.8532 7.43398 10.6864C7.46827 10.5195 7.56169 10.3707 7.69704 10.2673L9.81816 8.61693C10.4968 8.08517 10.6536 7.1214 10.1784 6.40198L6.39895 0.734676C5.91192 0.00208106 4.9348 -0.21784 4.18082 0.235398L1.81096 1.65898C1.06634 2.09672 0.520053 2.80571 0.286612 3.63733C-0.56677 6.74673 0.0752209 12.1131 7.98033 20.0191C14.2687 26.307 18.9501 27.9979 22.1677 27.9979C22.9083 28.0011 23.6459 27.9048 24.3608 27.7115C25.1925 27.4783 25.9016 26.932 26.3391 26.1871L27.7641 23.8187C28.218 23.0645 27.9982 22.0868 27.2653 21.5995Z"})})}),e.jsxs("div",{className:"content",children:[e.jsx("span",{children:"To More Inquiry"}),e.jsx("h6",{children:e.jsx("a",{href:"tel:{ frontendSettings?.Phone  }",children:s.phone})})]})]})})]}),e.jsxs("header",{className:"header-area style-2",children:[e.jsx("div",{className:"header-logo d-lg-none d-flex",children:e.jsx(v,{to:"/",children:e.jsx("img",{alt:"logo",className:"img-fluid",src:s.logo})})}),e.jsx("div",{className:"company-logo d-lg-block d-none",children:e.jsx(v,{to:"/",children:e.jsx("img",{src:(s==null?void 0:s.logo)||"/default-logo.png",alt:"logo"})})}),e.jsxs("div",{className:"menu-button sidebar-button mobile-menu-btn d-lg-none d-flex",children:[e.jsx("svg",{width:"15",height:"12",viewBox:"0 0 15 12",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M0 0.75C0 0.551088 0.0790176 0.360322 0.21967 0.21967C0.360322 0.0790178 0.551088 0 0.75 0H10.5C10.6989 0 10.8897 0.0790178 11.0303 0.21967C11.171 0.360322 11.25 0.551088 11.25 0.75C11.25 0.948912 11.171 1.13968 11.0303 1.28033C10.8897 1.42098 10.6989 1.5 10.5 1.5H0.75C0.551088 1.5 0.360322 1.42098 0.21967 1.28033C0.0790176 1.13968 0 0.948912 0 0.75ZM14.25 5.25H0.75C0.551088 5.25 0.360322 5.32902 0.21967 5.46967C0.0790176 5.61032 0 5.80109 0 6C0 6.19891 0.0790176 6.38968 0.21967 6.53033C0.360322 6.67098 0.551088 6.75 0.75 6.75H14.25C14.4489 6.75 14.6397 6.67098 14.7803 6.53033C14.921 6.38968 15 6.19891 15 6C15 5.80109 14.921 5.61032 14.7803 5.46967C14.6397 5.32902 14.4489 5.25 14.25 5.25ZM7.5 10.5H0.75C0.551088 10.5 0.360322 10.579 0.21967 10.7197C0.0790176 10.8603 0 11.0511 0 11.25C0 11.4489 0.0790176 11.6397 0.21967 11.7803C0.360322 11.921 0.551088 12 0.75 12H7.5C7.69891 12 7.88968 11.921 8.03033 11.7803C8.17098 11.6397 8.25 11.4489 8.25 11.25C8.25 11.0511 8.17098 10.8603 8.03033 10.7197C7.88968 10.579 7.69891 10.5 7.5 10.5Z"})}),e.jsx("span",{children:"MENU"})]}),e.jsxs("div",{className:"main-menu",children:[e.jsx("div",{className:"mobile-logo-area d-lg-none d-flex justify-content-between align-items-center",children:e.jsx("div",{className:"mobile-logo-wrap",children:e.jsx(v,{to:"/",children:e.jsx("img",{alt:"logo",src:s.logo})})})}),e.jsx("ul",{className:"menu-list",children:d.filter(a=>!a.parent_id).map(a=>{const m=d.filter(n=>n.parent_id===a.id);return m.length>0?e.jsxs("li",{className:"position-inherit menu-parent",style:{position:"relative"},children:[e.jsx("a",{href:a.slug==="home"?"/":`/${a.slug}`,className:"drop-down",children:a.title}),e.jsx("i",{className:"bi bi-plus dropdown-icon d-lg-none d-block"}),e.jsx("ul",{className:"dropdown-menu",style:{display:"none",position:"absolute",left:0,top:"100%",zIndex:1e3,background:"#fff",color:"#222",minWidth:"180px",boxShadow:"0 2px 8px rgba(0,0,0,0.12)",padding:"8px 0",borderRadius:"4px",border:"1px solid #eee"},children:m.map(n=>e.jsx("li",{style:{padding:"6px 18px"},children:e.jsx(v,{to:n.slug==="home"?"/":`/${n.slug}`,style:{color:"#222",textDecoration:"none",display:"block"},children:n.title})},n.id))})]},a.id):e.jsx("li",{children:e.jsx(v,{to:a.slug&&a.slug.toLowerCase()==="home"?"/":`/${a.slug}`,children:a.title})},a.id)})}),e.jsx("style",{children:`
            .menu-parent:hover > .dropdown-menu {
              display: block !important;
            }
          `})]}),e.jsxs("div",{className:"nav-right d-lg-flex d-none jsutify-content-end align-items-center",children:[e.jsx("div",{className:"search-btn d-lg-flex d-none",children:e.jsx("a",{children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 12 12",children:e.jsx("path",{d:"M8.80684 7.75632C9.53304 6.76537 9.85829 5.53677 9.71754 4.31631C9.57678 3.09585 8.9804 1.97354 8.04769 1.17391C7.11499 0.374287 5.91476 -0.0436852 4.68712 0.00361793C3.45949 0.050921 2.29498 0.560011 1.42657 1.42904C0.558166 2.29806 0.0499094 3.46294 0.00348477 4.69061C-0.0429399 5.91828 0.375891 7.11821 1.17619 8.05034C1.97648 8.98247 3.09922 9.57805 4.31978 9.71794C5.54034 9.85782 6.76871 9.53168 7.75913 8.80478H7.75838C7.78088 8.83478 7.80488 8.86328 7.83188 8.89103L10.7193 11.7784C10.8599 11.9191 11.0507 11.9982 11.2496 11.9983C11.4486 11.9984 11.6394 11.9194 11.7801 11.7788C11.9208 11.6382 11.9999 11.4474 12 11.2485C12.0001 11.0495 11.9211 10.8587 11.7805 10.718L8.89309 7.83057C8.86628 7.80342 8.83744 7.77835 8.80684 7.75557V7.75632Z"})})})}),e.jsx("button",{type:"button",className:"modal-btn header-user-btn d-lg-flex d-none","data-bs-toggle":"modal","data-bs-target":"#logInModal01",children:e.jsx("svg",{width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M14.4311 12.759C15.417 11.4291 16 9.78265 16 8C16 3.58169 12.4182 0 8 0C3.58169 0 0 3.58169 0 8C0 12.4182 3.58169 16 8 16C10.3181 16 12.4058 15.0141 13.867 13.4387C14.0673 13.2226 14.2556 12.9957 14.4311 12.759ZM13.9875 12C14.7533 10.8559 15.1999 9.48009 15.1999 8C15.1999 4.02355 11.9764 0.799983 7.99991 0.799983C4.02355 0.799983 0.799983 4.02355 0.799983 8C0.799983 9.48017 1.24658 10.8559 2.01245 12C2.97866 10.5566 4.45301 9.48194 6.17961 9.03214C5.34594 8.45444 4.79998 7.49102 4.79998 6.39995C4.79998 4.63266 6.23271 3.19993 8 3.19993C9.76729 3.19993 11.2 4.63266 11.2 6.39995C11.2 7.49093 10.654 8.45444 9.82039 9.03206C11.5469 9.48194 13.0213 10.5565 13.9875 12ZM13.4722 12.6793C12.3495 10.8331 10.3188 9.59997 8.00008 9.59997C5.68126 9.59997 3.65049 10.8331 2.52776 12.6794C3.84829 14.2222 5.80992 15.2 8 15.2C10.1901 15.2 12.1517 14.2222 13.4722 12.6793ZM8 8.79998C9.32551 8.79998 10.4 7.72554 10.4 6.39995C10.4 5.07444 9.32559 3.99992 8 3.99992C6.6744 3.99992 5.59997 5.07452 5.59997 6.40003C5.59997 7.72554 6.67449 8.79998 8 8.79998Z"})})}),s.addPropertyButton===1&&e.jsxs(v,{className:"primary-btn3 d-lg-flex d-none",to:"/dashboard-add-property",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",children:e.jsxs("g",{clipPath:"url(#clip0_485_1365)",children:[e.jsx("path",{d:"M7 13.125C5.37555 13.125 3.81763 12.4797 2.66897 11.331C1.52031 10.1824 0.875 8.62445 0.875 7C0.875 5.37555 1.52031 3.81763 2.66897 2.66897C3.81763 1.52031 5.37555 0.875 7 0.875C8.62445 0.875 10.1824 1.52031 11.331 2.66897C12.4797 3.81763 13.125 5.37555 13.125 7C13.125 8.62445 12.4797 10.1824 11.331 11.331C10.1824 12.4797 8.62445 13.125 7 13.125ZM7 14C8.85652 14 10.637 13.2625 11.9497 11.9497C13.2625 10.637 14 8.85652 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0C5.14348 0 3.36301 0.737498 2.05025 2.05025C0.737498 3.36301 0 5.14348 0 7C0 8.85652 0.737498 10.637 2.05025 11.9497C3.36301 13.2625 5.14348 14 7 14Z"}),e.jsx("path",{d:"M7 3.5C7.11603 3.5 7.22731 3.54609 7.30936 3.62814C7.39141 3.71019 7.4375 3.82147 7.4375 3.9375V6.5625H10.0625C10.1785 6.5625 10.2898 6.60859 10.3719 6.69064C10.4539 6.77269 10.5 6.88397 10.5 7C10.5 7.11603 10.4539 7.22731 10.3719 7.30936C10.2898 7.39141 10.1785 7.4375 10.0625 7.4375H7.4375V10.0625C7.4375 10.1785 7.39141 10.2898 7.30936 10.3719C7.22731 10.4539 7.11603 10.5 7 10.5C6.88397 10.5 6.77269 10.4539 6.69064 10.3719C6.60859 10.2898 6.5625 10.1785 6.5625 10.0625V7.4375H3.9375C3.82147 7.4375 3.71019 7.39141 3.62814 7.30936C3.54609 7.22731 3.5 7.11603 3.5 7C3.5 6.88397 3.54609 6.77269 3.62814 6.69064C3.71019 6.60859 3.82147 6.5625 3.9375 6.5625H6.5625V3.9375C6.5625 3.82147 6.60859 3.71019 6.69064 3.62814C6.77269 3.54609 6.88397 3.5 7 3.5Z"})]})}),"ADD PROPERTY"]})]})]})]})},Y=()=>{const[s,c]=t.useState({}),[i,d]=t.useState(!0);t.useEffect(()=>{o()},[]);const o=async()=>{try{const a=await B.getAll();a.success&&a.data&&c(m=>({...m,...a.data}))}catch(a){console.error("Error fetching frontend settings:",a)}finally{d(!1)}};return k(),S(),e.jsxs("div",{children:[s.showshowPreloader===1&&e.jsx(U,{}),e.jsx(D,{}),e.jsx(O,{}),e.jsx(q,{}),e.jsx(z,{frontendSettings:s})]})},J=({pageContents:s=null,pageId:c=1,widgetId:i=1,sliderProps:d={},customSliders:o=null})=>{const[a,m]=t.useState([]),[n,r]=t.useState(!0),[h,l]=t.useState(null);t.useEffect(()=>{if(o&&Array.isArray(o)){m(o),r(!1);return}if(s&&Array.isArray(s))try{const j=s.find(g=>g.widget_id===i);if(j&&j.pageContent){const g=typeof j.pageContent=="string"?JSON.parse(j.pageContent):j.pageContent;g&&g.sliders&&Array.isArray(g.sliders)&&m(g.sliders)}r(!1);return}catch(j){console.error("Error parsing pageContents:",j),l(j.message),r(!1);return}(async()=>{try{r(!0);const j=await fetch(`/api/frontend/page-contents?page_id=${c}&widget_id=${i}&paginate=false`);if(!j.ok)throw new Error(`HTTP error! status: ${j.status}`);const g=await j.json();if(g.success&&g.data&&g.data.length>0){const u=g.data.find(w=>w.widget_id===i);if(u&&u.pageContent){const w=typeof u.pageContent=="string"?JSON.parse(u.pageContent):u.pageContent;w&&w.sliders&&Array.isArray(w.sliders)&&m(w.sliders)}}else console.log("No data found")}catch(j){console.error("Error fetching slider data:",j),l(j.message)}finally{r(!1)}})()},[s,c,i,o]);const x=a,{showPagination:p=!0,showRating:b=!0,containerClass:f="banner-section2",sliderClass:y="home2-banner-slider",loadingMinHeight:L="400px",defaultButtonText:M="Find Property",defaultRating:H="5.0",defaultReviewsCount:F="2348"}=d;return n?e.jsx("div",{className:f,children:e.jsx("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:L},children:e.jsx("div",{className:"spinner-border",role:"status",children:e.jsx("span",{className:"sr-only",children:"Loading slider data..."})})})}):(h&&console.warn("Slider API error:",h),!x||x.length===0?null:e.jsx(e.Fragment,{children:e.jsxs("div",{className:f,children:[p&&e.jsx("div",{className:"banner2-swiper-pagination"}),e.jsx("div",{className:`swiper ${y}`,children:e.jsx("div",{className:"swiper-wrapper",children:x.map((C,j)=>e.jsx("div",{className:"swiper-slide",children:e.jsx("div",{className:"banner-wrapper",style:{backgroundImage:`linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.15) 100%), url(${C.image})`},children:e.jsx("div",{className:"row",children:e.jsx("div",{className:"col-lg-8",children:e.jsxs("div",{className:"banner-content",children:[e.jsx("h1",{children:C.title}),e.jsx("p",{children:C.description}),e.jsxs("div",{className:"banner-content-bottom",children:[e.jsx("div",{className:"view-dt-btn",children:e.jsxs("a",{href:C.button_link,className:"primary-btn3",children:[e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 18 18",fill:"currentColor",children:e.jsx("path",{d:"M12.5 11h-.79l-.28-.27C12.41 9.59 13 8.11 13 6.5 13 2.91 10.09 0 6.5 0S0 2.91 0 6.5 2.91 13 6.5 13c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L17.49 16l-4.99-5zm-6 0C4.01 11 2 8.99 2 6.5S4.01 2 6.5 2 11 4.01 11 6.5 8.99 11 6.5 11z"})}),C.button_text||M]})}),b&&e.jsx("div",{className:"rating",children:e.jsxs("a",{href:"#",children:[e.jsxs("div",{className:"review-top",children:[e.jsx("div",{className:"logo",children:e.jsx("img",{src:C.ratingLogo,alt:"Trustpilot Logo"})}),e.jsx("div",{className:"star"})]}),e.jsx("div",{className:"content",children:e.jsxs("ul",{children:[e.jsxs("li",{children:["Trust Rating ",e.jsx("span",{children:C.rating||H})]}),e.jsxs("li",{children:[e.jsx("span",{children:C.reviews_count||F})," Reviews"]})]})})]})})]})]})})})})},j))})})]})}))},G=({pageContents:s=[]})=>{const[c,i]=t.useState(null);return t.useEffect(()=>{if(!s||s.length===0)return;const d=s.find(o=>o.widget_id===4);if(d)try{const o=typeof d.pageContent=="string"?JSON.parse(d.pageContent):d.pageContent;i(o)}catch(o){console.error("Error parsing about widget data:",o)}},[s]),c?e.jsx(e.Fragment,{children:e.jsx("div",{className:"home2-about-area pt-100 mb-100",children:e.jsx("div",{className:"container",children:e.jsxs("div",{className:"row g-lg-4 gy-5",children:[e.jsxs("div",{className:"col-xl-4",children:[e.jsx("div",{className:"about-img wow fadeInUp","data-wow-delay":"200ms",children:e.jsx("img",{src:c.image1||"/Frontend/assets/img/home2/home2-about-img1.png",alt:"about"})}),e.jsx("div",{className:"activetis wow fadeInUp","data-wow-delay":"300ms",children:(c.stats||[]).map((d,o)=>e.jsxs("div",{className:"single-activiti",children:[e.jsx("div",{className:"icon",children:e.jsx("img",{src:d.icon||"/Frontend/assets/img/home2/icon/home1.svg",alt:"icon"})}),e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"number",children:[e.jsx("h5",{className:"counter",children:d.number}),e.jsx("span",{children:d.suffix})]}),e.jsx("p",{children:d.label})]})]},o))})]}),e.jsx("div",{className:"col-xl-8",children:e.jsxs("div",{className:"about-content-wrap wow fadeInUp","data-wow-delay":"400ms",children:[e.jsxs("div",{className:"section-title1",children:[e.jsxs("span",{children:["(Since-",c.serviceFrom,")"]}),e.jsx("h2",{children:c.title})]}),e.jsx("h6",{children:c.subtitle}),c.aboutText&&c.aboutText.replace(/<[^>]+>/g,""),e.jsxs("div",{className:"author-and-img",children:[e.jsxs("div",{className:"author-area",children:[e.jsx("img",{src:c.ceoSignature||"/Frontend/assets/img/home2/icon/author-signature.svg",alt:"author signature"}),e.jsxs("div",{className:"author-name-deg",children:[e.jsx("h6",{children:c.authorName||"Natrison Mongla"}),e.jsxs("span",{children:["(",c.authorPosition||"CEO & Founder",")"]})]})]}),e.jsx("div",{className:"about-img",children:e.jsx("img",{src:c.image2||"/Frontend/assets/img/home2/home2-about-img2.png",alt:"about"})})]})]})})]})})})}):e.jsx("div",{className:"home2-about-area pt-100 mb-100",children:e.jsx("div",{className:"container text-center",children:e.jsx("h4",{children:"Loading About Us section..."})})})},K=()=>{const[s,c]=t.useState([]),[i,d]=t.useState(""),[o,a]=t.useState(""),[m,n]=t.useState(10);return t.useEffect(()=>{fetch("/api/frontend/page-contents?page_id=1&widget_id=6&paginate=false").then(r=>r.json()).then(r=>{if(r.success&&Array.isArray(r.data)&&r.data.length>0){const h=r.data.find(l=>l.widget_id===6);if(h&&h.pageContent){let l=typeof h.pageContent=="string"?JSON.parse(h.pageContent):h.pageContent;d(l.title||""),a(l.subTitle||""),l.perPage&&n(l.perPage)}}})},[]),t.useEffect(()=>{fetch(`/api/public/states?per_page=${m}`).then(r=>r.json()).then(r=>{r.success&&Array.isArray(r.data)&&c(r.data)}).catch(r=>{console.error("Error fetching states:",r)})},[m]),e.jsx("div",{className:"recent-launched-project mb-100",children:e.jsxs("div",{className:"container",children:[e.jsx("div",{className:"row mb-50 wow fadeInUp","data-wow-delay":"200ms",children:e.jsxs("div",{className:"col-lg-12 d-flex align-items-end justify-content-between gap-3 flex-wrap",children:[e.jsxs("div",{className:"section-title-2",children:[e.jsx("h2",{children:i||"Properties By Citiessds"}),e.jsx("p",{children:o||"Here are some of the featured Apartment in different categories"})]}),e.jsxs("div",{className:"slider-btn-group2",children:[e.jsx("div",{className:"slider-btn prev-5",children:e.jsx("svg",{width:"9",height:"15",viewBox:"0 0 8 13",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M0 6.50008L8 0L2.90909 6.50008L8 13L0 6.50008Z"})})}),e.jsx("div",{className:"slider-btn next-5",children:e.jsx("svg",{width:"9",height:"15",viewBox:"0 0 8 13",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 6.50008L0 0L5.09091 6.50008L0 13L8 6.50008Z"})})})]})]})}),e.jsx("div",{className:"row wow fadeInUp","data-wow-delay":"300ms",children:e.jsx("div",{className:"col-lg-12",children:e.jsx("div",{className:"swiper recent-launch-car-slider",children:e.jsx("div",{className:"swiper-wrapper",children:s.map(r=>e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"product-card2",children:[e.jsx("div",{className:"product-img",children:e.jsx("img",{src:r.image?`/state/${r.image}`:"/Frontend/assets/img/home2/city-01.png",alt:r.name})}),e.jsxs("div",{className:"product-content",children:[e.jsx("h6",{children:r.name}),e.jsxs("span",{children:[r.property_count||0," Property"]})]})]})},r.id))})})})})]})})},Q=()=>{const[s,c]=t.useState(null),[i,d]=t.useState(!0),[o,a]=t.useState(null);return t.useEffect(()=>{(async()=>{try{d(!0),console.log("Fetching Why Choose Us data from database...");const n=await fetch("/api/frontend/page-contents?page_id=1&widget_id=7&paginate=false");if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const r=await n.json();if(console.log("Why Choose Us API Response:",r),r.success&&r.data&&r.data.length>0){const h=r.data.find(l=>l.widget_id===7);if(h&&h.pageContent){const l=typeof h.pageContent=="string"?JSON.parse(h.pageContent):h.pageContent;console.log("Parsed Why Choose Us config:",l);const x={title:l.shoulder||"Why Only Choose Neckle",subtitle:l.underTitle||"Here are some of the featured Apartment in different categories",features:l.items?l.items.map(p=>({title:p.title||"Feature Title",description:p.subTitle||"Feature description goes here...",icon:"/Frontend/assets/img/home2/icon/affordable.svg"})):[],logoIcon:l.iconImage,image1:l.image,image2:l.imagetwo,reviewText:l.reviewText};c(x),console.log("Why Choose Us data set from database:",x)}else console.log("No widget found with widget_id 7")}else console.log("No data found in API response")}catch(n){console.error("Error fetching why choose us data:",n),a(n.message)}finally{d(!1)}})()},[]),console.log("Why Choose Us data from database:",s),i?e.jsx("div",{className:"home2-why-choose-section mb-100 pt-90 pb-90",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:e.jsx("div",{className:"spinner-border",role:"status",children:e.jsx("span",{className:"sr-only",children:"Loading Why Choose Us..."})})})})}):o||!s?e.jsx("div",{className:"home2-why-choose-section mb-100 pt-90 pb-90",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{children:"Unable to load Why Choose Us data"}),e.jsx("p",{className:"text-muted",children:o?`Error: ${o}`:"No data found in database"}),e.jsx("p",{className:"text-muted",children:"Please check the database for page_id=1 and widget_id=7"})]})})})}):e.jsx(e.Fragment,{children:e.jsx("div",{className:"home2-why-choose-section mb-100 pt-90 pb-90",children:e.jsxs("div",{className:"container",children:[e.jsx("div",{className:"row mb-50 wow fadeInUp","data-wow-delay":"200ms",children:e.jsx("div",{className:"col-lg-12",children:e.jsxs("div",{className:"section-title-2 text-center",children:[e.jsx("h2",{children:s.title}),e.jsx("p",{children:s.subtitle})]})})}),e.jsxs("div",{className:"row g-lg-4 gy-5 mb-50",children:[e.jsx("div",{className:"col-lg-6",children:e.jsx("div",{className:"why-choose-content-area",children:e.jsx("ul",{children:s.features&&s.features.length>0&&s.features.map((m,n)=>e.jsxs("li",{className:"single-choose wow fadeInUp","data-wow-delay":`${300+n*100}ms`,children:[e.jsx("div",{className:"icon",children:e.jsx("img",{src:m.icon||"/Frontend/assets/img/home2/icon/affordable.svg",alt:m.title||"Feature"})}),e.jsxs("div",{className:"content",children:[e.jsx("h5",{children:e.jsx("span",{children:m.title})}),e.jsx("p",{children:m.description})]})]},n))})})}),e.jsx("div",{className:"col-lg-6",children:e.jsxs("div",{className:"why-choose-img-wrap wow zoomIn","data-wow-delay":"400ms",children:[e.jsx("div",{className:"logo-area",children:e.jsx("img",{src:s.logoIcon||"/Frontend/assets/img/home2/icon/house-1.svg",alt:"House"})}),e.jsxs("div",{className:"row g-lg-4 g-2",children:[e.jsx("div",{className:"col-6",children:e.jsx("div",{className:"choose-img",children:e.jsx("img",{src:s.image1||"/Frontend/assets/img/home2/choose-01.png",alt:"Choose 01"})})}),e.jsx("div",{className:"col-6",children:e.jsx("div",{className:"choose-img",children:e.jsx("img",{src:s.image2||"/Frontend/assets/img/home2/choose-02.png",alt:"Choose 02"})})})]})]})})]}),e.jsx("div",{className:"row wow fadeInUp","data-wow-delay":"200ms",children:e.jsx("div",{className:"col-lg-12",children:e.jsxs("div",{className:"trustpilot-review",children:[e.jsx("div",{dangerouslySetInnerHTML:{__html:s.reviewText||"Excellent! 5.0 Rating out of <strong>5.0</strong> based on <strong>245354</strong> reviews"}}),e.jsx("img",{src:"/Frontend/assets/img/home1/icon/trustpilot-star2.svg",alt:"Trustpilot Stars"}),e.jsx("img",{src:"/Frontend/assets/img/home1/icon/trustpilot-logo.svg",alt:"Trustpilot Logo"})]})})})]})})})},X=()=>{const[s,c]=t.useState([]);return t.useEffect(()=>{fetch("/api/frontend/featured/projects").then(i=>i.json()).then(i=>{i.success&&Array.isArray(i.data)&&c(i.data)})},[]),e.jsxs("div",{className:"featured-car-section mb-100",children:[e.jsx("div",{className:"container",children:e.jsx("div",{className:"row mb-50 wow fadeInUp","data-wow-delay":"200ms",children:e.jsx("div",{className:"col-lg-12 d-flex align-items-center justify-content-between gap-3 flex-wrap",children:e.jsxs("div",{className:"section-title-2",children:[e.jsx("h2",{children:"Featured Property"}),e.jsx("p",{children:"Here are some of the featured Apartment in different categories"})]})})})}),e.jsx("div",{className:"container-fluid",children:e.jsx("div",{className:"row wow fadeInUp","data-wow-delay":"300ms",children:e.jsx("div",{className:"swiper home2-featured-slider",children:e.jsx("div",{className:"swiper-wrapper",children:s.map(i=>{var d;return e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"feature-card",children:[e.jsx("div",{className:"product-img",children:e.jsx("img",{className:"img-fluid",src:((d=i.featured_image)==null?void 0:d.image_url)||"assets/img/home2/feature-1.png",alt:i.title})}),e.jsxs("div",{className:"product-content",children:[e.jsx("div",{className:"price",children:e.jsx("strong",{children:i.total_price?`$${i.total_price}`:""})}),e.jsx("h5",{children:e.jsx("a",{href:`property-details/${i.id}`,children:i.title})}),e.jsxs("ul",{className:"features",children:[e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home2/icon/bed1.svg",alt:""}),i.bedrooms??"-"," Beds"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home2/icon/bath1.svg",alt:""}),i.bathrooms??"-"," Baths"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home2/icon/size1.svg",alt:""}),"Sq.ft- ",i.area_sqft??"-"]})]})]})]})},i.id)})})})})})]})},e0=()=>{const[s,c]=t.useState(null),[i,d]=t.useState(!0),[o,a]=t.useState(null);return t.useEffect(()=>{(async()=>{try{d(!0),console.log("Fetching How It Works data from database...");const n=await fetch("/api/frontend/page-contents?page_id=1&widget_id=9&paginate=false");if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const r=await n.json();if(console.log("How It Works API Response:",r),r.success&&r.data&&r.data.length>0){const h=r.data.find(l=>l.widget_id===9);if(h&&h.pageContent){const l=typeof h.pageContent=="string"?JSON.parse(h.pageContent):h.pageContent;console.log("Parsed How It Works config:",l);const x={title:l.shoulder||"How Does It Work",subtitle:l.underText||"Here are some of the featured Apartment in different categories",videoUrl:l.buttonUrl||"https://www.youtube.com/watch?v=MLpWrANjFbI&ab_channel=eidelchteinadvogados",videoText:l.buttonName||"Watch video",steps:l.items?l.items.map(p=>({title:p.title||"Step Title",description:p.text||"Step description goes here...",icon:p.image||"/Frontend/assets/img/home2/icon/loaction.svg"})):[]};c(x),console.log("How It Works data set from database:",x)}else console.log("No widget found with widget_id 9")}else console.log("No data found in API response")}catch(n){console.error("Error fetching how it works data:",n),a(n.message)}finally{d(!1)}})()},[]),i?e.jsx("div",{className:"how-it-work-section mb-100",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:e.jsx("div",{className:"spinner-border",role:"status",children:e.jsx("span",{className:"sr-only",children:"Loading How It Works..."})})})})}):o||!s?e.jsx("div",{className:"how-it-work-section mb-100",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{children:"Unable to load How It Works data"}),e.jsx("p",{className:"text-muted",children:o?`Error: ${o}`:"No data found in database"}),e.jsx("p",{className:"text-muted",children:"Please check the database for page_id=1 and widget_id=9"})]})})})}):e.jsx(e.Fragment,{children:e.jsx("div",{className:"how-it-work-section mb-100",children:e.jsxs("div",{className:"container",children:[e.jsx("div",{className:"row mb-50 wow fadeInUp","data-wow-delay":"200ms",children:e.jsxs("div",{className:"col-lg-12 d-flex align-items-end justify-content-between gap-3 flex-wrap",children:[e.jsxs("div",{className:"section-title-2",children:[e.jsx("h2",{children:s.title||"How Does It Work"}),e.jsx("p",{children:s.subtitle||"Here are some of the featured Apartment in different categories"})]}),e.jsx("div",{className:"video-btn",children:e.jsxs("a",{"data-fancybox":"gallery",href:s.videoUrl||"https://www.youtube.com/watch?v=MLpWrANjFbI&ab_channel=eidelchteinadvogados",children:[e.jsx("i",{className:"bi bi-play-circle"})," ",s.videoText||"Watch video"]})})]})}),e.jsx("div",{className:"row wow fadeInUp","data-wow-delay":"300ms",children:e.jsx("div",{className:"col-lg-12",children:e.jsx("div",{className:"work-process-group",children:e.jsx("div",{className:"row justify-content-center g-lg-0 gy-5",children:s.steps&&s.steps.length>0&&s.steps.map((m,n)=>e.jsx("div",{className:"col-lg-3 col-sm-6",children:e.jsxs("div",{className:"single-work-process text-center",children:[e.jsx("div",{className:"step",children:e.jsx("span",{children:String(n+1).padStart(2,"0")})}),e.jsx("div",{className:"icon",children:e.jsx("img",{src:m.icon||"/Frontend/assets/img/home2/icon/loaction.svg",alt:m.title||"Step"})}),e.jsxs("div",{className:"content",children:[e.jsx("h6",{children:m.title||"Step Title"}),e.jsx("p",{children:m.description||"Step description goes here..."})]})]})},n))})})})}),e.jsx("div",{className:"row wow fadeInUp","data-wow-delay":"400ms",children:e.jsx("div",{className:"col-lg-12 d-flex justify-content-center",children:e.jsxs("div",{className:"trustpilot-review",children:[s.reviewText?e.jsx("div",{dangerouslySetInnerHTML:{__html:s.reviewText}}):e.jsxs(e.Fragment,{children:[e.jsx("strong",{children:"Excellent!"}),e.jsx("img",{src:"/Frontend/assets/img/home1/icon/trustpilot-star2.svg",alt:""}),e.jsxs("p",{children:["5.0 Rating out of ",e.jsx("strong",{children:"5.0"})," based on"," ",e.jsxs("a",{href:"#",children:[e.jsx("strong",{children:"245354"})," reviews"]})]})]}),e.jsx("img",{src:"/Frontend/assets/img/home1/icon/trustpilot-star2.svg",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/icon/trustpilot-logo.svg",alt:""})]})})})]})})})},s0=()=>{const[s,c]=t.useState([]);return t.useEffect(()=>{fetch("/api/frontend/recent/projects").then(i=>i.json()).then(i=>{i.success&&Array.isArray(i.data)&&c(i.data)})},[]),e.jsx("div",{className:"recent-launched-project mb-100",children:e.jsxs("div",{className:"container",children:[e.jsx("div",{className:"row mb-50 wow fadeInUp","data-wow-delay":"200ms",children:e.jsxs("div",{className:"col-lg-12 d-flex align-items-end justify-content-between gap-3 flex-wrap",children:[e.jsxs("div",{className:"section-title-2",children:[e.jsx("h2",{children:"Recent Property"}),e.jsx("p",{children:"Here are some of the featured Apartment in different categories"})]}),e.jsxs("div",{className:"tab-and-slider-btn-group",children:[e.jsxs("ul",{className:"nav nav-tabs",id:"myTab5",role:"tablist",children:[e.jsx("li",{className:"nav-item",role:"presentation",children:e.jsx("button",{className:"nav-link active",id:"sedan-tab","data-bs-toggle":"tab","data-bs-target":"#sedan",type:"button",role:"tab","aria-controls":"sedan","aria-selected":"true",children:"On Going"})}),e.jsx("li",{className:"nav-item",role:"presentation",children:e.jsx("button",{className:"nav-link",id:"suv-tab","data-bs-toggle":"tab","data-bs-target":"#suv",type:"button",role:"tab","aria-controls":"suv","aria-selected":"false",children:"For Sale"})}),e.jsx("li",{className:"nav-item",role:"presentation",children:e.jsx("button",{className:"nav-link",id:"toyota-tab","data-bs-toggle":"tab","data-bs-target":"#toyota",type:"button",role:"tab","aria-controls":"toyota","aria-selected":"false",children:"For Rent"})})]}),e.jsxs("div",{className:"slider-btn-group2",children:[e.jsx("div",{className:"slider-btn prev-5",children:e.jsx("svg",{width:"9",height:"15",viewBox:"0 0 8 13",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M0 6.50008L8 0L2.90909 6.50008L8 13L0 6.50008Z"})})}),e.jsx("div",{className:"slider-btn next-5",children:e.jsx("svg",{width:"9",height:"15",viewBox:"0 0 8 13",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 6.50008L0 0L5.09091 6.50008L0 13L8 6.50008Z"})})})]})]})]})}),e.jsx("div",{className:"row wow fadeInUp","data-wow-delay":"300ms",children:e.jsx("div",{className:"col-lg-12",children:e.jsxs("div",{className:"tab-content",id:"myTabContent5",children:[e.jsx("div",{className:"tab-pane fade show active",id:"sedan",role:"tabpanel","aria-labelledby":"sedan-tab",children:e.jsx("div",{className:"swiper recent-launch-car-slider",children:e.jsx("div",{className:"swiper-wrapper",children:s.map(i=>{var d;return e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"product-card2 two",children:[e.jsxs("div",{className:"product-img",children:[e.jsx("a",{href:"#",className:"fav",children:e.jsx("svg",{width:"14",height:"13",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.00012 2.40453L6.37273 1.75966C4.90006 0.245917 2.19972 0.76829 1.22495 2.67141C0.767306 3.56653 0.664053 4.8589 1.4997 6.50827C2.30473 8.09639 3.97953 9.99864 7.00012 12.0706C10.0207 9.99864 11.6946 8.09639 12.5005 6.50827C13.3362 4.85803 13.2338 3.56653 12.7753 2.67141C11.8005 0.76829 9.10019 0.245042 7.62752 1.75879L7.00012 2.40453ZM7.00012 13.125C-6.41666 4.25953 2.86912 -2.65995 6.84612 1.00016C6.89862 1.04829 6.95024 1.09816 7.00012 1.14979C7.04949 1.09821 7.10087 1.04859 7.15413 1.00104C11.1302 -2.6617 20.4169 4.25865 7.00012 13.125Z"})})}),e.jsx("img",{src:((d=i.featured_image)==null?void 0:d.image_url)||"/Frontend/assets/img/home2/city-01.png",alt:i.title})]}),e.jsxs("div",{className:"product-content",children:[e.jsx("div",{className:"details-btn",children:e.jsx("a",{href:`property-details/${i.id}`,children:e.jsx("i",{className:"bi bi-arrow-right-short"})})}),e.jsx("div",{className:"price",children:e.jsx("strong",{children:i.total_price?`$${i.total_price}`:""})}),e.jsx("h6",{children:e.jsx("a",{href:`property-details/${i.id}`,children:i.title})}),e.jsxs("ul",{className:"features",children:[e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bed2.svg",alt:""}),i.bedrooms??"-"," Beds"]}),e.jsxs("li",{children:[i.bathrooms??"-"," Baths"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/size2.svg",alt:""}),"Sq.ft- ",i.area_sqft??"-"]})]})]})]})},i.id)})})})}),e.jsx("div",{className:"tab-pane fade",id:"suv",role:"tabpanel","aria-labelledby":"suv-tab",children:e.jsx("div",{className:"swiper recent-launch-car-slider",children:e.jsxs("div",{className:"swiper-wrapper",children:[e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"product-card2 two",children:[e.jsxs("div",{className:"product-img",children:[e.jsx("a",{href:"#",className:"fav",children:e.jsx("svg",{width:"14",height:"13",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.00012 2.40453L6.37273 1.75966C4.90006 0.245917 2.19972 0.76829 1.22495 2.67141C0.767306 3.56653 0.664053 4.8589 1.4997 6.50827C2.30473 8.09639 3.97953 9.99864 7.00012 12.0706C10.0207 9.99864 11.6946 8.09639 12.5005 6.50827C13.3362 4.85803 13.2338 3.56653 12.7753 2.67141C11.8005 0.76829 9.10019 0.245042 7.62752 1.75879L7.00012 2.40453ZM7.00012 13.125C-6.41666 4.25953 2.86912 -2.65995 6.84612 1.00016C6.89862 1.04829 6.95024 1.09816 7.00012 1.14979C7.04949 1.09821 7.10087 1.04859 7.15413 1.00104C11.1302 -2.6617 20.4169 4.25865 7.00012 13.125Z"})})}),e.jsx("img",{src:"/Frontend/assets/img/home2/city-01.png",alt:""})]}),e.jsxs("div",{className:"product-content",children:[e.jsx("div",{className:"details-btn",children:e.jsx("a",{href:"poperty-deatils1.html",children:e.jsx("i",{className:"bi bi-arrow-right-short"})})}),e.jsx("div",{className:"price",children:e.jsx("strong",{children:"$32,445.00"})}),e.jsx("h6",{children:e.jsx("a",{href:"poperty-deatils1.html",children:"Azure Horizons"})}),e.jsxs("ul",{className:"features",children:[e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bed2.svg",alt:""}),"04 Beds"]}),e.jsx("li",{children:"02 Baths"}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/size2.svg",alt:""}),"Sq.ft- 124,560"]})]})]})]})}),e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"product-card2 two",children:[e.jsxs("div",{className:"product-img",children:[e.jsx("a",{href:"#",className:"fav",children:e.jsx("svg",{width:"14",height:"13",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.00012 2.40453L6.37273 1.75966C4.90006 0.245917 2.19972 0.76829 1.22495 2.67141C0.767306 3.56653 0.664053 4.8589 1.4997 6.50827C2.30473 8.09639 3.97953 9.99864 7.00012 12.0706C10.0207 9.99864 11.6946 8.09639 12.5005 6.50827C13.3362 4.85803 13.2338 3.56653 12.7753 2.67141C11.8005 0.76829 9.10019 0.245042 7.62752 1.75879L7.00012 2.40453ZM7.00012 13.125C-6.41666 4.25953 2.86912 -2.65995 6.84612 1.00016C6.89862 1.04829 6.95024 1.09816 7.00012 1.14979C7.04949 1.09821 7.10087 1.04859 7.15413 1.00104C11.1302 -2.6617 20.4169 4.25865 7.00012 13.125Z"})})}),e.jsx("img",{src:"/Frontend/assets/img/home2/city-02.png",alt:""})]}),e.jsxs("div",{className:"product-content",children:[e.jsx("div",{className:"details-btn",children:e.jsx("a",{href:"poperty-deatils1.html",children:e.jsx("i",{className:"bi bi-arrow-right-short"})})}),e.jsx("div",{className:"price",children:e.jsx("strong",{children:"$12,445.00"})}),e.jsx("h6",{children:e.jsx("a",{href:"poperty-deatils1.html",children:"Nexus Realty"})}),e.jsxs("ul",{className:"features",children:[e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bed2.svg",alt:""}),"04 Beds"]}),e.jsx("li",{children:"02 Baths"}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/size2.svg",alt:""}),"Sq.ft- 124,560"]})]})]})]})}),e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"product-card2 two",children:[e.jsxs("div",{className:"product-img",children:[e.jsx("a",{href:"#",className:"fav",children:e.jsx("svg",{width:"14",height:"13",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.00012 2.40453L6.37273 1.75966C4.90006 0.245917 2.19972 0.76829 1.22495 2.67141C0.767306 3.56653 0.664053 4.8589 1.4997 6.50827C2.30473 8.09639 3.97953 9.99864 7.00012 12.0706C10.0207 9.99864 11.6946 8.09639 12.5005 6.50827C13.3362 4.85803 13.2338 3.56653 12.7753 2.67141C11.8005 0.76829 9.10019 0.245042 7.62752 1.75879L7.00012 2.40453ZM7.00012 13.125C-6.41666 4.25953 2.86912 -2.65995 6.84612 1.00016C6.89862 1.04829 6.95024 1.09816 7.00012 1.14979C7.04949 1.09821 7.10087 1.04859 7.15413 1.00104C11.1302 -2.6617 20.4169 4.25865 7.00012 13.125Z"})})}),e.jsx("img",{src:"/Frontend/assets/img/home2/city-03.png",alt:""})]}),e.jsxs("div",{className:"product-content",children:[e.jsx("div",{className:"details-btn",children:e.jsx("a",{href:"poperty-deatils1.html",children:e.jsx("i",{className:"bi bi-arrow-right-short"})})}),e.jsx("div",{className:"price",children:e.jsx("strong",{children:"$10,445.00"})}),e.jsx("h6",{children:e.jsx("a",{href:"poperty-deatils1.html",children:"Astral Homes"})}),e.jsxs("ul",{className:"features",children:[e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bed2.svg",alt:""}),"04 Beds"]}),e.jsx("li",{children:"02 Baths"}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/size2.svg",alt:""}),"Sq.ft- 124,560"]})]})]})]})}),e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"product-card2 two",children:[e.jsxs("div",{className:"product-img",children:[e.jsx("a",{href:"#",className:"fav",children:e.jsx("svg",{width:"14",height:"13",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.00012 2.40453L6.37273 1.75966C4.90006 0.245917 2.19972 0.76829 1.22495 2.67141C0.767306 3.56653 0.664053 4.8589 1.4997 6.50827C2.30473 8.09639 3.97953 9.99864 7.00012 12.0706C10.0207 9.99864 11.6946 8.09639 12.5005 6.50827C13.3362 4.85803 13.2338 3.56653 12.7753 2.67141C11.8005 0.76829 9.10019 0.245042 7.62752 1.75879L7.00012 2.40453ZM7.00012 13.125C-6.41666 4.25953 2.86912 -2.65995 6.84612 1.00016C6.89862 1.04829 6.95024 1.09816 7.00012 1.14979C7.04949 1.09821 7.10087 1.04859 7.15413 1.00104C11.1302 -2.6617 20.4169 4.25865 7.00012 13.125Z"})})}),e.jsx("img",{src:"/Frontend/assets/img/home2/city-04.png",alt:""})]}),e.jsxs("div",{className:"product-content",children:[e.jsx("div",{className:"details-btn",children:e.jsx("a",{href:"poperty-deatils1.html",children:e.jsx("i",{className:"bi bi-arrow-right-short"})})}),e.jsx("div",{className:"price",children:e.jsx("strong",{children:"$8,445.00"})}),e.jsx("h6",{children:e.jsx("a",{href:"poperty-deatils1.html",children:"Nimbus Properties"})}),e.jsxs("ul",{className:"features",children:[e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bed2.svg",alt:""}),"04 Beds"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bath2.svg",alt:""}),"02 Baths"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/size2.svg",alt:""}),"Sq.ft- 124,560"]})]})]})]})})]})})}),e.jsx("div",{className:"tab-pane fade",id:"toyota",role:"tabpanel","aria-labelledby":"toyota-tab",children:e.jsx("div",{className:"swiper recent-launch-car-slider",children:e.jsxs("div",{className:"swiper-wrapper",children:[e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"product-card2 two",children:[e.jsxs("div",{className:"product-img",children:[e.jsx("a",{href:"#",className:"fav",children:e.jsx("svg",{width:"14",height:"13",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.00012 2.40453L6.37273 1.75966C4.90006 0.245917 2.19972 0.76829 1.22495 2.67141C0.767306 3.56653 0.664053 4.8589 1.4997 6.50827C2.30473 8.09639 3.97953 9.99864 7.00012 12.0706C10.0207 9.99864 11.6946 8.09639 12.5005 6.50827C13.3362 4.85803 13.2338 3.56653 12.7753 2.67141C11.8005 0.76829 9.10019 0.245042 7.62752 1.75879L7.00012 2.40453ZM7.00012 13.125C-6.41666 4.25953 2.86912 -2.65995 6.84612 1.00016C6.89862 1.04829 6.95024 1.09816 7.00012 1.14979C7.04949 1.09821 7.10087 1.04859 7.15413 1.00104C11.1302 -2.6617 20.4169 4.25865 7.00012 13.125Z"})})}),e.jsx("img",{src:"/Frontend/assets/img/home2/city-01.png",alt:""})]}),e.jsxs("div",{className:"product-content",children:[e.jsx("div",{className:"details-btn",children:e.jsx("a",{href:"poperty-deatils1.html",children:e.jsx("i",{className:"bi bi-arrow-right-short"})})}),e.jsx("div",{className:"price",children:e.jsx("strong",{children:"$32,445.00"})}),e.jsx("h6",{children:e.jsx("a",{href:"poperty-deatils1.html",children:"Azure Horizons"})}),e.jsxs("ul",{className:"features",children:[e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bed2.svg",alt:""}),"04 Beds"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bath2.svg",alt:""}),"02 Baths"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/size2.svg",alt:""}),"Sq.ft- 124,560"]})]})]})]})}),e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"product-card2 two",children:[e.jsxs("div",{className:"product-img",children:[e.jsx("a",{href:"#",className:"fav",children:e.jsx("svg",{width:"14",height:"13",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.00012 2.40453L6.37273 1.75966C4.90006 0.245917 2.19972 0.76829 1.22495 2.67141C0.767306 3.56653 0.664053 4.8589 1.4997 6.50827C2.30473 8.09639 3.97953 9.99864 7.00012 12.0706C10.0207 9.99864 11.6946 8.09639 12.5005 6.50827C13.3362 4.85803 13.2338 3.56653 12.7753 2.67141C11.8005 0.76829 9.10019 0.245042 7.62752 1.75879L7.00012 2.40453ZM7.00012 13.125C-6.41666 4.25953 2.86912 -2.65995 6.84612 1.00016C6.89862 1.04829 6.95024 1.09816 7.00012 1.14979C7.04949 1.09821 7.10087 1.04859 7.15413 1.00104C11.1302 -2.6617 20.4169 4.25865 7.00012 13.125Z"})})}),e.jsx("img",{src:"/Frontend/assets/img/home2/city-02.png",alt:""})]}),e.jsxs("div",{className:"product-content",children:[e.jsx("div",{className:"details-btn",children:e.jsx("a",{href:"poperty-deatils1.html",children:e.jsx("i",{className:"bi bi-arrow-right-short"})})}),e.jsx("div",{className:"price",children:e.jsx("strong",{children:"$12,445.00"})}),e.jsx("h6",{children:e.jsx("a",{href:"poperty-deatils1.html",children:"Nexus Realty"})}),e.jsxs("ul",{className:"features",children:[e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bed2.svg",alt:""}),"04 Beds"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bath2.svg",alt:""}),"02 Baths"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/size2.svg",alt:""}),"Sq.ft- 124,560"]})]})]})]})}),e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"product-card2 two",children:[e.jsxs("div",{className:"product-img",children:[e.jsx("a",{href:"#",className:"fav",children:e.jsx("svg",{width:"14",height:"13",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.00012 2.40453L6.37273 1.75966C4.90006 0.245917 2.19972 0.76829 1.22495 2.67141C0.767306 3.56653 0.664053 4.8589 1.4997 6.50827C2.30473 8.09639 3.97953 9.99864 7.00012 12.0706C10.0207 9.99864 11.6946 8.09639 12.5005 6.50827C13.3362 4.85803 13.2338 3.56653 12.7753 2.67141C11.8005 0.76829 9.10019 0.245042 7.62752 1.75879L7.00012 2.40453ZM7.00012 13.125C-6.41666 4.25953 2.86912 -2.65995 6.84612 1.00016C6.89862 1.04829 6.95024 1.09816 7.00012 1.14979C7.04949 1.09821 7.10087 1.04859 7.15413 1.00104C11.1302 -2.6617 20.4169 4.25865 7.00012 13.125Z"})})}),e.jsx("img",{src:"/Frontend/assets/img/home2/city-03.png",alt:""})]}),e.jsxs("div",{className:"product-content",children:[e.jsx("div",{className:"details-btn",children:e.jsx("a",{href:"poperty-deatils1.html",children:e.jsx("i",{className:"bi bi-arrow-right-short"})})}),e.jsx("div",{className:"price",children:e.jsx("strong",{children:"$10,445.00"})}),e.jsx("h6",{children:e.jsx("a",{href:"poperty-deatils1.html",children:"Astral Homes"})}),e.jsxs("ul",{className:"features",children:[e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bed2.svg",alt:""}),"04 Beds"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bath2.svg",alt:""}),"02 Baths"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/size2.svg",alt:""}),"Sq.ft- 124,560"]})]})]})]})}),e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"product-card2 two",children:[e.jsxs("div",{className:"product-img",children:[e.jsx("a",{href:"#",className:"fav",children:e.jsx("svg",{width:"14",height:"13",viewBox:"0 0 14 14",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M7.00012 2.40453L6.37273 1.75966C4.90006 0.245917 2.19972 0.76829 1.22495 2.67141C0.767306 3.56653 0.664053 4.8589 1.4997 6.50827C2.30473 8.09639 3.97953 9.99864 7.00012 12.0706C10.0207 9.99864 11.6946 8.09639 12.5005 6.50827C13.3362 4.85803 13.2338 3.56653 12.7753 2.67141C11.8005 0.76829 9.10019 0.245042 7.62752 1.75879L7.00012 2.40453ZM7.00012 13.125C-6.41666 4.25953 2.86912 -2.65995 6.84612 1.00016C6.89862 1.04829 6.95024 1.09816 7.00012 1.14979C7.04949 1.09821 7.10087 1.04859 7.15413 1.00104C11.1302 -2.6617 20.4169 4.25865 7.00012 13.125Z"})})}),e.jsx("img",{src:"/Frontend/assets/img/home2/city-04.png",alt:""})]}),e.jsxs("div",{className:"product-content",children:[e.jsx("div",{className:"details-btn",children:e.jsx("a",{href:"poperty-deatils1.html",children:e.jsx("i",{className:"bi bi-arrow-right-short"})})}),e.jsx("div",{className:"price",children:e.jsx("strong",{children:"$8,445.00"})}),e.jsx("h6",{children:e.jsx("a",{href:"poperty-deatils1.html",children:"Nimbus Properties"})}),e.jsxs("ul",{className:"features",children:[e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bed2.svg",alt:""}),"04 Beds"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/bath2.svg",alt:""}),"02 Baths"]}),e.jsxs("li",{children:[e.jsx("img",{src:"/Frontend/assets/img/home1/icon/size2.svg",alt:""}),"Sq.ft- 124,560"]})]})]})]})})]})})})]})})})]})})},i0=()=>{const[s,c]=t.useState(null),[i,d]=t.useState(!0),[o,a]=t.useState(null);return t.useEffect(()=>{(async()=>{try{d(!0),console.log("Fetching Advertise data from database...");const n=await fetch("/api/frontend/page-contents?page_id=1&widget_id=11&paginate=false");if(!n.ok)throw new Error(`HTTP error! status: ${n.status}`);const r=await n.json();if(console.log("Advertise API Response:",r),r.success&&r.data&&r.data.length>0){const h=r.data.find(l=>l.widget_id===11);if(h&&h.pageContent){const l=typeof h.pageContent=="string"?JSON.parse(h.pageContent):h.pageContent;console.log("Parsed Advertise config:",l);const x={title:l.title,subtitle:l.subTitle,description:l.details?l.details.replace(/<[^>]*>/g,""):"A car that is dependable and has a low risk of breakdowns is highly desirable.",image:l.image};c(x)}}}catch(n){console.error("Error fetching advertise data:",n),a(n.message)}finally{d(!1)}})()},[]),i?e.jsx("div",{className:"recommended-apartment-section mb-100",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:e.jsx("div",{className:"spinner-border",role:"status",children:e.jsx("span",{className:"sr-only",children:"Loading Advertisement..."})})})})}):o||!s?e.jsx("div",{className:"recommended-apartment-section mb-100",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"d-flex justify-content-center align-items-center",style:{minHeight:"400px"},children:e.jsxs("div",{className:"text-center",children:[e.jsx("h3",{children:"Unable to load Advertisement data"}),e.jsx("p",{className:"text-muted",children:o?`Error: ${o}`:"No data found in database"}),e.jsx("p",{className:"text-muted",children:"Please check the database for page_id=1 and widget_id=11"})]})})})}):e.jsx(e.Fragment,{children:e.jsxs("div",{className:"recommended-apartment-section mb-100",children:[e.jsxs("div",{className:"recomended-content-wrap",children:[e.jsxs("div",{className:"section-title1 wow fadeInUp","data-wow-delay":"200ms",children:[e.jsx("span",{children:s.subtitle}),e.jsx("h2",{children:s.title})]}),e.jsx("div",{className:"divider d-xl-flex d-none"}),e.jsxs("div",{className:"recomended-content wow fadeInUp","data-wow-delay":"200ms",children:[e.jsx("p",{children:s.description}),e.jsx("a",{href:"property-listing-left-sidebar.html",className:"primary-btn3",children:"Show Best Home"})]})]}),e.jsx("div",{className:"aparment-img",children:e.jsx("img",{src:s.image,alt:s.title})})]})})},l0=()=>e.jsx(e.Fragment,{children:e.jsx("div",{className:"home2-testimonial-section mb-100",children:e.jsxs("div",{className:"container",children:[e.jsx("div",{className:"row mb-50 wow fadeInUp","data-wow-delay":"200ms",children:e.jsxs("div",{className:"col-lg-12 d-flex align-items-end justify-content-between gap-3 flex-wrap",children:[e.jsxs("div",{className:"section-title-2",children:[e.jsx("h2",{children:"Our Customer Reviews"}),e.jsx("p",{children:"Here are some of the featured Apartment in different categories"})]}),e.jsxs("div",{className:"review-and-btn d-flex flex-wrap align-items-center gap-sm-5 gap-3",children:[e.jsx("div",{className:"rating",children:e.jsxs("a",{href:"#",children:[e.jsxs("div",{className:"review-top",children:[e.jsx("div",{className:"logo"}),e.jsx("div",{className:"star"})]}),e.jsx("div",{className:"content",children:e.jsxs("ul",{children:[e.jsxs("li",{children:["Trust Rating ",e.jsx("span",{children:"5.0"})]}),e.jsxs("li",{children:[e.jsx("span",{children:"2348"})," Reviews"]})]})})]})}),e.jsxs("div",{className:"slider-btn-group2",children:[e.jsx("div",{className:"slider-btn prev-6",children:e.jsx("svg",{width:"9",height:"15",viewBox:"0 0 8 13",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M0 6.50008L8 0L2.90909 6.50008L8 13L0 6.50008Z"})})}),e.jsx("div",{className:"slider-btn next-6",children:e.jsx("svg",{width:"9",height:"15",viewBox:"0 0 8 13",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M8 6.50008L0 0L5.09091 6.50008L0 13L8 6.50008Z"})})})]})]})]})}),e.jsx("div",{className:"row wow fadeInUp","data-wow-delay":"300ms",children:e.jsx("div",{className:"col-lg-12",children:e.jsx("div",{className:"swiper customer-feedback-slider2",children:e.jsxs("div",{className:"swiper-wrapper",children:[e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"feedback-card",children:[e.jsxs("div",{className:"feedback-top",children:[e.jsx("div",{className:"stat-area"}),e.jsx("div",{className:"services",children:e.jsx("span",{children:"Trusted Company"})})]}),e.jsx("p",{children:"neckle-Agency customer feedback is an invaluable source of information that can help businesses improve their offerings and provide better experiences."}),e.jsxs("div",{className:"author-name",children:[e.jsx("h6",{children:"Jhon Abraham,"}),e.jsx("span",{children:"25 minutes ago"})]})]})}),e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"feedback-card",children:[e.jsxs("div",{className:"feedback-top",children:[e.jsx("div",{className:"stat-area"}),e.jsx("div",{className:"services",children:e.jsx("span",{children:"Trusted Company"})})]}),e.jsx("p",{children:"neckle-Agency customer feedback is an invaluable source of information that can help businesses improve their offerings and provide better experiences."}),e.jsxs("div",{className:"author-name",children:[e.jsx("h6",{children:"Michayel Jhon,"}),e.jsx("span",{children:"25 minutes ago"})]})]})}),e.jsx("div",{className:"swiper-slide",children:e.jsxs("div",{className:"feedback-card",children:[e.jsxs("div",{className:"feedback-top",children:[e.jsx("div",{className:"stat-area"}),e.jsx("div",{className:"services",children:e.jsx("span",{children:"Trusted Company"})})]}),e.jsx("p",{children:"neckle-Agencycustomer feedback is an invaluable source of information that can help businesses improve their offerings and provide better experiences."}),e.jsxs("div",{className:"author-name",children:[e.jsx("h6",{children:"Rakhab Uddin,"}),e.jsx("span",{children:"25 minutes ago"})]})]})})]})})})})]})})}),a0=()=>e.jsx("div",{className:"trusted-partner-section mb-100",children:e.jsx("div",{className:"container",children:e.jsx("div",{className:"row",children:e.jsxs("div",{className:"col-lg-12",children:[e.jsxs("div",{className:"sub-title",children:[e.jsx("h6",{children:"Our Trusted Partners"}),e.jsx("div",{className:"dash"})]}),e.jsx("div",{className:"partner-slider",children:e.jsxs("div",{className:"marquee_text2",children:[e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-01.png",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-02.png",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-03.png",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-04.png",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-05.png",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-06.png",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-01.png",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-02.png",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-03.png",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-04.png",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-05.png",alt:""}),e.jsx("img",{src:"/Frontend/assets/img/home1/company-logo-06.png",alt:""})]})})]})})})}),r0=()=>e.jsx(e.Fragment,{children:e.jsx("div",{className:"news-section pt-90 pb-90",children:e.jsxs("div",{className:"container",children:[e.jsx("div",{className:"row mb-50 wow fadeInUp","data-wow-delay":"200ms",children:e.jsx("div",{className:"col-lg-12 d-flex align-items-end justify-content-between flex-wrap gap-4",children:e.jsxs("div",{className:"section-title-2",children:[e.jsx("h2",{children:"The Latest Insight"}),e.jsx("p",{children:"Here are some of the featured Apartment in different categories"})]})})}),e.jsxs("div",{className:"row g-4 justify-content-center",children:[e.jsx("div",{className:"col-lg-4 col-md-6 wow fadeInUp","data-wow-delay":"200ms",children:e.jsxs("div",{className:"news-card",children:[e.jsxs("div",{className:"news-img",children:[e.jsx("a",{href:"blog-details.html",children:e.jsx("img",{src:"/Frontend/assets/img/home1/news-01.png",alt:""})}),e.jsx("div",{className:"date",children:e.jsx("a",{href:"blog-standard.html",children:"Apartment"})})]}),e.jsxs("div",{className:"content",children:[e.jsx("h6",{children:e.jsx("a",{href:"blog-details.html",children:"The Rise of Remote Work: How It's Shaping Real Estate Trends"})}),e.jsx("div",{className:"news-btm",children:e.jsxs("div",{className:"author-area",children:[e.jsx("div",{className:"author-img",children:e.jsx("img",{src:"/Frontend/assets/img/home1/author-01.png",alt:""})}),e.jsxs("div",{className:"author-content",children:[e.jsx("h6",{children:"Mr. Morris Mannu"}),e.jsx("a",{href:"blog-standard.html",children:"Posted on - 03 April, 2023"})]})]})})]})]})}),e.jsx("div",{className:"col-lg-4 col-md-6 wow fadeInUp","data-wow-delay":"300ms",children:e.jsxs("div",{className:"news-card",children:[e.jsxs("div",{className:"news-img",children:[e.jsx("a",{href:"blog-details.html",children:e.jsx("img",{src:"/Frontend/assets/img/home1/news-02.png",alt:""})}),e.jsx("div",{className:"date",children:e.jsx("a",{href:"blog-standard.html",children:"Home Town"})})]}),e.jsxs("div",{className:"content",children:[e.jsx("h6",{children:e.jsx("a",{href:"blog-details.html",children:"Real Estate Investing 101: Essential Tips for New Investors"})}),e.jsx("div",{className:"news-btm",children:e.jsxs("div",{className:"author-area",children:[e.jsx("div",{className:"author-img",children:e.jsx("img",{src:"/Frontend/assets/img/home1/author-02.png",alt:""})}),e.jsxs("div",{className:"author-content",children:[e.jsx("h6",{children:"Daniel Scoot"}),e.jsx("a",{href:"blog-standard.html",children:"Posted on - 03 April, 2023"})]})]})})]})]})}),e.jsx("div",{className:"col-lg-4 col-md-6 wow fadeInUp","data-wow-delay":"400ms",children:e.jsxs("div",{className:"news-card",children:[e.jsxs("div",{className:"news-img",children:[e.jsx("a",{href:"blog-details.html",children:e.jsx("img",{src:"/Frontend/assets/img/home1/news-03.png",alt:""})}),e.jsx("div",{className:"date",children:e.jsx("a",{href:"blog-standard.html",children:"Industrial"})})]}),e.jsxs("div",{className:"content",children:[e.jsx("h6",{children:e.jsx("a",{href:"blog-details.html",children:"The Hot Real Estate Market: Tips for Buyers and Sellers"})}),e.jsx("div",{className:"news-btm",children:e.jsxs("div",{className:"author-area",children:[e.jsx("div",{className:"author-img",children:e.jsx("img",{src:"/Frontend/assets/img/home1/author-03.png",alt:""})}),e.jsxs("div",{className:"author-content",children:[e.jsx("h6",{children:"Mulish Kary"}),e.jsx("a",{href:"blog-standard.html",children:"Posted on - 03 April, 2023"})]})]})})]})]})})]})]})})}),n0=()=>{const{slug:s}=Z(),[c,i]=t.useState(null),[d,o]=t.useState(null),[a,m]=t.useState([]),n=t.useMemo(()=>[...a].sort((h,l)=>(h.order_index??0)-(l.order_index??0)),[a]);t.useEffect(()=>{if(!s)return;(async()=>{var l;try{const x=await A.getBySlug(s);i(((l=x.data)==null?void 0:l.id)||null)}catch(x){o(x.error||"Menu not found")}})()},[s]),t.useEffect(()=>{if(!c)return;(async()=>{try{const l=await I.getByPageId(c);m(Array.isArray(l.data)?l.data:[])}catch{o("Failed to fetch page contents")}})()},[c]);const r={1:J,4:G,8:X,6:K,7:Q,9:e0,10:s0,11:i0,12:l0,13:a0,14:r0};return e.jsxs("div",{className:"page-container",children:[d&&e.jsx("div",{className:"alert alert-danger text-center my-3",children:d}),n.map((h,l)=>{const x=r[h.widget_id];return x?e.jsx(x,{pageContents:a},h.widget_id+"-"+l):null})]})},t0=()=>e.jsx("footer",{children:e.jsxs("div",{className:"container-fluid",children:[e.jsx("div",{className:"footer-top",children:e.jsxs("div",{className:"row row-cols-xl-5 row-cols-lg-4 row-cols-md-3 row-cols-sm-3 row-cols-1 justify-content-center g-lg-4 gy-5 ",children:[e.jsx("div",{className:"col d-flex justify-content-lg-start",children:e.jsxs("div",{className:"footer-widget",children:[e.jsx("div",{className:"widget-title",children:e.jsx("h5",{children:"About Company"})}),e.jsx("div",{className:"menu-container",children:e.jsxs("ul",{children:[e.jsx("li",{children:e.jsxs("a",{href:"about.html",children:["About Us",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"return-enchange.html",children:["Return & Exchange",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"return-enchange.html",children:["Refund Policy",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"customer-review.html",children:["Reviews",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"faq.html",children:["FAQ’s",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"contact.html",children:["Contact Us",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})})]})})]})}),e.jsx("div",{className:"col d-flex justify-content-sm-center",children:e.jsxs("div",{className:"footer-widget",children:[e.jsx("div",{className:"widget-title",children:e.jsx("h5",{children:"Search & Explore"})}),e.jsx("div",{className:"menu-container",children:e.jsxs("ul",{children:[e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Home For Sale",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Home For Rent",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["To Buy Home",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Sell Your Home",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"shop.html",children:["Shop Now",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"shop.html",children:["Sitemap",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})})]})})]})}),e.jsx("div",{className:"col d-flex justify-content-lg-center justify-content-sm-end",children:e.jsxs("div",{className:"footer-widget",children:[e.jsx("div",{className:"widget-title",children:e.jsx("h5",{children:"Apartments Type"})}),e.jsx("div",{className:"menu-container",children:e.jsxs("ul",{children:[e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Industrial",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Development",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Home Town",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Office",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Health Care",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Banglow",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["House",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Flat Share",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Park Home",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})})]})})]})}),e.jsx("div",{className:"col d-flex justify-content-xl-center justify-content-lg-end justify-content-sm-center",children:e.jsxs("div",{className:"footer-widget",children:[e.jsx("div",{className:"widget-title",children:e.jsx("h5",{children:"Home By Location"})}),e.jsx("div",{className:"menu-container",children:e.jsxs("ul",{children:[e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Panama City",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Sydne, AUS",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["New Delhi",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Kualalumpur",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Melbourne",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["City of Abu-Dubi",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Menchester City",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})}),e.jsx("li",{children:e.jsxs("a",{href:"property-listing-left-sidebar.html",children:["Dhaka City",e.jsx("svg",{width:"10",height:"10",viewBox:"0 0 10 10",xmlns:"http://www.w3.org/2000/svg",children:e.jsx("path",{d:"M9.1605 0H0.849401C0.626751 0 0.413219 0.0884475 0.255781 0.245885C0.0983438 0.403323 0.00989626 0.616854 0.00989626 0.839505C0.00989626 1.06216 0.0983438 1.27569 0.255781 1.43312C0.413219 1.59056 0.626751 1.67901 0.849401 1.67901H7.13309L0.256291 8.55665C0.17611 8.63409 0.112154 8.72673 0.0681567 8.82915C0.0241591 8.93157 0.00100033 9.04173 3.16969e-05 9.1532C-0.********* 9.26467 0.020304 9.37522 0.062515 9.47839C0.104726 9.58156 0.167062 9.67529 0.245885 9.75412C0.324709 9.83294 0.418441 9.89527 0.521613 9.93748C0.624785 9.9797 0.735331 10.0009 0.846799 9.99997C0.958268 9.999 1.06843 9.97584 1.17085 9.93184C1.27327 9.88785 1.36591 9.82389 1.44335 9.74371L8.32099 2.86649V9.1506C8.32099 9.37325 8.40944 9.58678 8.56688 9.74422C8.72431 9.90166 8.93785 9.9901 9.1605 9.9901C9.38315 9.9901 9.59668 9.90166 9.75412 9.74422C9.91155 9.58678 10 9.37325 10 9.1506V0.839505C10 0.616854 9.91155 0.403323 9.75412 0.245885C9.59668 0.0884475 9.38315 0 9.1605 0Z"})})]})})]})})]})}),e.jsx("div",{className:"col d-flex justify-content-xl-end justify-content-sm-center",children:e.jsxs("div",{className:"footer-widget",children:[e.jsx("div",{className:"widget-title",children:e.jsx("h5",{children:"Download App"})}),e.jsx("div",{className:"app-download",children:e.jsxs("ul",{children:[e.jsx("li",{children:e.jsx("a",{href:"#",children:e.jsx("img",{src:"/Frontend/assets/img/home1/icon/google-app.svg",alt:""})})}),e.jsx("li",{children:e.jsx("a",{href:"#",children:e.jsx("img",{src:"/Frontend/assets/img/home1/icon/apple-app.svg",alt:""})})})]})})]})})]})}),e.jsxs("div",{className:"footer-center",children:[e.jsx("div",{className:"footer-logo",children:e.jsx("a",{href:"index.html",children:e.jsx("img",{src:"/Frontend/assets/img/footer-logo.svg",alt:""})})}),e.jsxs("div",{className:"contact-area",children:[e.jsxs("div",{className:"hotline-area",children:[e.jsx("div",{className:"icon",children:e.jsxs("svg",{width:"32",height:"32",viewBox:"0 0 32 32",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M31.1603 24.6852L24.6834 20.3658C23.8615 19.8221 22.7597 20.001 22.152 20.7769L20.2654 23.2027C20.1481 23.3573 19.9789 23.4645 19.789 23.5045C19.599 23.5444 19.4011 23.5145 19.2314 23.4203L18.8725 23.2224C17.6828 22.574 16.2025 21.7667 13.22 18.7831C10.2375 15.7995 9.42859 14.3181 8.78012 13.1306L8.58334 12.7717C8.48781 12.6021 8.45678 12.4037 8.49597 12.213C8.53516 12.0223 8.64193 11.8522 8.79662 11.734L11.2208 9.84792C11.9964 9.2402 12.1756 8.13874 11.6324 7.31655L7.31309 0.83963C6.75648 0.00237835 5.63977 -0.24896 4.77809 0.269026L2.06967 1.89597C1.21867 2.39626 0.594346 3.20652 0.327557 4.15695C-0.647737 7.71055 0.0859667 13.8435 9.12038 22.879C16.3071 30.0651 21.6572 31.9976 25.3345 31.9976C26.1809 32.0013 27.0239 31.8912 27.8409 31.6703C28.7915 31.4038 29.6018 30.7794 30.1018 29.9281L31.7304 27.2214C32.2491 26.3595 31.9979 25.2421 31.1603 24.6852ZM30.8115 26.6742L29.1867 29.3826C28.8277 29.997 28.2449 30.4488 27.5603 30.6432C24.2797 31.5439 18.5483 30.7979 9.87489 22.1245C1.20149 13.4511 0.455538 7.72017 1.35622 4.4391C1.55097 3.75367 2.00324 3.17011 2.61841 2.81053L5.32682 1.1857C5.7007 0.960737 6.18538 1.06978 6.4269 1.4331L8.77324 4.95577L10.7426 7.90946C10.9784 8.26609 10.9009 8.74409 10.5645 9.00798L8.13978 10.8941C7.40188 11.4583 7.19117 12.4792 7.64547 13.2895L7.83801 13.6393C8.51953 14.8892 9.36684 16.4442 12.4603 19.5371C15.5537 22.63 17.1081 23.4773 18.3575 24.1588L18.7078 24.3518C19.518 24.8061 20.539 24.5954 21.1032 23.8575L22.9893 21.4328C23.2533 21.0966 23.7311 21.0191 24.0879 21.2547L30.5642 25.5741C30.9278 25.8154 31.0368 26.3004 30.8115 26.6742ZM18.1324 5.33496C23.1367 5.34053 27.1921 9.39599 27.1977 14.4003C27.1977 14.6948 27.4364 14.9335 27.7309 14.9335C28.0255 14.9335 28.2642 14.6948 28.2642 14.4003C28.258 8.8072 23.7255 4.27462 18.1324 4.2685C17.8378 4.2685 17.5991 4.50721 17.5991 4.80173C17.5991 5.09625 17.8378 5.33496 18.1324 5.33496Z"}),e.jsx("path",{d:"M18.1324 8.53424C21.3704 8.53805 23.9944 11.162 23.9982 14.4001C23.9982 14.5415 24.0544 14.6771 24.1544 14.7771C24.2544 14.8771 24.39 14.9333 24.5314 14.9333C24.6728 14.9333 24.8085 14.8771 24.9085 14.7771C25.0085 14.6771 25.0646 14.5415 25.0646 14.4001C25.0602 10.5733 21.9591 7.47215 18.1324 7.46777C17.8378 7.46777 17.5991 7.70649 17.5991 8.00101C17.5991 8.29553 17.8378 8.53424 18.1324 8.53424Z"}),e.jsx("path",{d:"M18.1324 11.7344C19.6041 11.7362 20.7968 12.9289 20.7986 14.4007C20.7986 14.5422 20.8548 14.6778 20.9548 14.7778C21.0548 14.8778 21.1905 14.934 21.3319 14.934C21.4733 14.934 21.6089 14.8778 21.7089 14.7778C21.8089 14.6778 21.8651 14.5422 21.8651 14.4007C21.8627 12.3402 20.1929 10.6703 18.1324 10.668C17.8378 10.668 17.5991 10.9067 17.5991 11.2012C17.5991 11.4957 17.8378 11.7344 18.1324 11.7344Z"})]})}),e.jsxs("div",{className:"content",children:[e.jsx("span",{children:"To More Inquiry"}),e.jsx("h6",{children:e.jsx("a",{href:"tel:+990737621432",children:"+990-737 621 432"})})]})]}),e.jsxs("div",{className:"hotline-area",children:[e.jsx("div",{className:"icon",children:e.jsxs("svg",{width:"32",height:"33",viewBox:"0 0 32 33",xmlns:"http://www.w3.org/2000/svg",children:[e.jsx("path",{d:"M16.6608 18.13C15.4654 18.1243 14.2777 17.9369 13.1387 17.5742C12.2446 17.2751 11.446 16.7441 10.8242 16.0355C10.2024 15.3269 9.77978 14.466 9.59946 13.5406C9.19786 11.6068 9.93012 9.56195 11.6069 7.92995C11.7871 7.75461 11.9742 7.58665 12.168 7.42649C13.0138 6.71831 14.0193 6.22662 15.0976 5.99386C16.1759 5.7611 17.2947 5.79426 18.3573 6.09049C19.3764 6.4159 20.2699 7.04873 20.915 7.90213C21.5601 8.75553 21.9253 9.78766 21.9605 10.8569C22.0387 12.1181 21.6276 13.3609 20.8128 14.3268C20.5045 14.715 20.0953 15.0108 19.6299 15.1816C19.1646 15.3525 18.6612 15.3918 18.1749 15.2953C17.9743 15.2536 17.7841 15.172 17.6158 15.0551C17.4474 14.9383 17.3044 14.7887 17.1952 14.6153C17.0972 14.4468 17.0342 14.2603 17.01 14.067C16.9858 13.8736 17.0009 13.6774 17.0544 13.49C17.5211 11.7268 17.9952 9.04729 18 9.02062C18.0122 8.95163 18.0378 8.88572 18.0755 8.82665C18.1132 8.76757 18.1621 8.7165 18.2195 8.67633C18.2769 8.63617 18.3416 8.6077 18.41 8.59256C18.4784 8.57742 18.5491 8.5759 18.6181 8.58809C18.6871 8.60027 18.753 8.62593 18.8121 8.66359C18.8712 8.70125 18.9222 8.75017 18.9624 8.80757C19.0026 8.86497 19.031 8.92972 19.0462 8.99812C19.0613 9.06652 19.0628 9.13723 19.0507 9.20622C19.0309 9.31769 18.5637 11.9566 18.0859 13.7625C18.069 13.812 18.0625 13.8645 18.0666 13.9167C18.0707 13.9689 18.0854 14.0197 18.1099 14.066C18.1836 14.1679 18.2949 14.2364 18.4192 14.2564C18.7169 14.3061 19.0226 14.2735 19.3032 14.1621C19.5838 14.0507 19.8286 13.8648 20.0112 13.6244C20.644 12.8674 20.961 11.8958 20.8965 10.9113C20.8711 10.0601 20.5829 9.23771 20.0714 8.55687C19.56 7.87603 18.8504 7.37014 18.04 7.10862C17.1472 6.86304 16.2081 6.83838 15.3036 7.03675C14.3992 7.23513 13.5566 7.65059 12.8485 8.24729C12.6773 8.38969 12.5104 8.53849 12.3504 8.69422C11.5216 9.50062 10.1973 11.1742 10.6437 13.3236C10.7911 14.0615 11.1287 14.7481 11.6231 15.3153C12.1175 15.8826 12.7515 16.3109 13.4624 16.5577C15.9637 17.3556 19.5584 17.4521 21.4517 15.0974C21.5414 14.9907 21.6693 14.9234 21.808 14.9098C21.9467 14.8962 22.0852 14.9375 22.1939 15.0248C22.3026 15.1121 22.3728 15.2384 22.3895 15.3768C22.4061 15.5151 22.368 15.6546 22.2832 15.7652C20.8827 17.507 18.7515 18.13 16.6608 18.13Z"}),e.jsx("path",{d:"M14.8353 15.3649C14.2714 15.3747 13.7214 15.1899 13.2779 14.8417C12.2545 14.0225 12.2262 12.599 12.5131 11.6299C12.6102 11.3073 12.7398 10.9953 12.9009 10.6993C13.301 9.89185 13.9409 9.22779 14.7329 8.79794C15.2132 8.54876 15.761 8.46069 16.2953 8.54674C16.8295 8.63279 17.322 8.8884 17.6998 9.27581C18.0847 9.69756 18.3746 10.197 18.5499 10.7403C18.594 10.8728 18.5844 11.0172 18.5232 11.1427C18.4621 11.2681 18.3541 11.3646 18.2226 11.4113C18.0911 11.4581 17.9465 11.4514 17.8198 11.3928C17.6932 11.3342 17.5946 11.2282 17.5451 11.0977C17.4187 10.6964 17.2085 10.3265 16.9286 10.0123C16.7085 9.78721 16.4209 9.6402 16.1095 9.59369C15.7981 9.54719 15.4801 9.60374 15.2038 9.75474C14.6098 10.0897 14.1325 10.5983 13.8358 11.2123C13.7112 11.4425 13.6106 11.6848 13.5355 11.9355C13.3281 12.6363 13.3739 13.5515 13.9457 14.0091C14.5707 14.5115 15.6257 14.2993 16.2193 13.7873C16.6614 13.389 17.0413 12.9266 17.3462 12.4155C17.3831 12.356 17.4314 12.3043 17.4884 12.2635C17.5453 12.2226 17.6097 12.1934 17.6779 12.1774C17.7461 12.1614 17.8168 12.159 17.886 12.1704C17.9551 12.1817 18.0213 12.2066 18.0809 12.2435C18.1404 12.2805 18.1921 12.3288 18.2329 12.3857C18.2738 12.4426 18.303 12.507 18.319 12.5753C18.335 12.6435 18.3374 12.7142 18.326 12.7833C18.3147 12.8524 18.2898 12.9187 18.2529 12.9782C17.8914 13.5802 17.4413 14.1245 16.9179 14.5926C16.3348 15.0847 15.5982 15.3578 14.8353 15.3649Z"}),e.jsx("path",{d:"M30.4005 32.0023H1.60049C1.17627 32.0019 0.769552 31.8332 0.469585 31.5332C0.169619 31.2332 0.000911967 30.8265 0.000488386 30.4023V10.669C0.000424993 10.5676 0.0292616 10.4683 0.0836186 10.3827C0.137976 10.2971 0.215601 10.2288 0.307397 10.1858C0.399192 10.1427 0.501355 10.1267 0.601912 10.1397C0.702468 10.1526 0.797252 10.1939 0.875155 10.2588L13.961 21.1346C14.535 21.6089 15.2564 21.8683 16.001 21.8683C16.7456 21.8683 17.467 21.6089 18.041 21.1346L31.1258 10.2583C31.2038 10.1934 31.2986 10.152 31.3992 10.1391C31.4998 10.1262 31.602 10.1422 31.6938 10.1853C31.7856 10.2284 31.8633 10.2968 31.9176 10.3825C31.9719 10.4682 32.0007 10.5675 32.0005 10.669V30.4023C32.0001 30.8265 31.8314 31.2332 31.5314 31.5332C31.2314 31.8332 30.8247 32.0019 30.4005 32.0023ZM1.06716 11.8055V30.4023C1.06716 30.6967 1.30609 30.9356 1.60049 30.9356H30.4005C30.5419 30.9356 30.6776 30.8794 30.7776 30.7794C30.8776 30.6794 30.9338 30.5438 30.9338 30.4023V11.8055L18.7216 21.9548C17.956 22.5875 16.994 22.9337 16.0009 22.9339C15.0079 22.934 14.0457 22.5882 13.28 21.9559L1.06716 11.8055Z"}),e.jsx("path",{d:"M0.534374 11.2024C0.42111 11.2026 0.310717 11.1668 0.219187 11.1C0.127657 11.0333 0.0597426 10.9392 0.0252829 10.8313C-0.00917678 10.7234 -0.00839247 10.6074 0.0275222 10.4999C0.063437 10.3925 0.132617 10.2993 0.22504 10.2339L5.02504 6.83119C5.14046 6.74936 5.28366 6.71673 5.42314 6.74049C5.56262 6.76424 5.68695 6.84243 5.76877 6.95785C5.8506 7.07327 5.88323 7.21648 5.85947 7.35595C5.83572 7.49543 5.75753 7.61976 5.64211 7.70159L0.842107 11.1043C0.752234 11.1682 0.644662 11.2025 0.534374 11.2024ZM31.4666 11.2024C31.3564 11.2025 31.2488 11.1682 31.1589 11.1043L26.3589 7.70159C26.2447 7.61935 26.1676 7.49531 26.1445 7.35649C26.1213 7.21768 26.154 7.07534 26.2353 6.9605C26.3167 6.84566 26.4401 6.76762 26.5788 6.7434C26.7174 6.71918 26.86 6.75073 26.9754 6.83119L31.7754 10.2339C31.8678 10.2993 31.9369 10.3924 31.9729 10.4997C32.0088 10.607 32.0097 10.723 31.9754 10.8308C31.941 10.9386 31.8733 11.0328 31.7819 11.0996C31.6906 11.1664 31.5798 11.2024 31.4666 11.2024ZM20.9285 3.73572C20.8181 3.73582 20.7103 3.70152 20.6202 3.63759L18.0709 1.82959C17.4975 1.34491 16.7721 1.07691 16.0213 1.07233C15.2705 1.06775 14.5419 1.32688 13.9626 1.80452L11.3813 3.63759C11.2659 3.71941 11.1227 3.75204 10.9832 3.72828C10.8437 3.70453 10.7194 3.62634 10.6376 3.51092C10.5557 3.3955 10.5231 3.2523 10.5469 3.11282C10.5706 2.97334 10.6488 2.84901 10.7642 2.76719L13.3136 0.959185C14.0773 0.33469 15.0346 -0.00443301 16.0212 4.37621e-05C17.0077 0.00452053 17.9619 0.352318 18.72 0.983718L21.2373 2.76719C21.3297 2.83266 21.3989 2.92585 21.4348 3.03327C21.4707 3.14069 21.4715 3.25675 21.4371 3.36465C21.4026 3.47254 21.3347 3.56667 21.2432 3.63338C21.1516 3.7001 21.0412 3.73594 20.928 3.73572H20.9285ZM0.880507 31.7144C0.770687 31.7146 0.663477 31.6809 0.573522 31.6179C0.483567 31.5549 0.415252 31.4657 0.377909 31.3624C0.340566 31.2591 0.336016 31.1468 0.364879 31.0409C0.393742 30.9349 0.454612 30.8405 0.539174 30.7704L12.7098 20.6584C12.7637 20.6136 12.8259 20.5799 12.8928 20.5592C12.9598 20.5385 13.0301 20.5311 13.0999 20.5376C13.1696 20.5441 13.2374 20.5642 13.2994 20.5969C13.3614 20.6295 13.4163 20.6741 13.461 20.728C13.5058 20.7819 13.5395 20.8441 13.5602 20.911C13.5809 20.9779 13.5883 21.0482 13.5818 21.118C13.5754 21.1878 13.5552 21.2556 13.5226 21.3175C13.4899 21.3795 13.4453 21.4344 13.3914 21.4792L1.22077 31.5912C1.12524 31.6708 1.00485 31.7144 0.880507 31.7144ZM31.12 31.7144C30.9956 31.7145 30.8752 31.6709 30.7797 31.5912L18.609 21.4792C18.5538 21.4349 18.5079 21.38 18.474 21.3178C18.4402 21.2556 18.4191 21.1872 18.4119 21.1167C18.4048 21.0463 18.4117 20.9751 18.4324 20.9073C18.4531 20.8396 18.4871 20.7766 18.5323 20.7221C18.5776 20.6676 18.6333 20.6227 18.6961 20.59C18.7589 20.5573 18.8276 20.5374 18.8982 20.5315C18.9688 20.5256 19.0399 20.5338 19.1073 20.5557C19.1746 20.5776 19.237 20.6127 19.2906 20.6589L31.4613 30.7709C31.5459 30.841 31.6067 30.9355 31.6356 31.0414C31.6645 31.1474 31.6599 31.2597 31.6226 31.3629C31.5852 31.4662 31.5169 31.5554 31.427 31.6184C31.337 31.6814 31.2298 31.7146 31.12 31.7144Z"}),e.jsx("path",{d:"M26.6672 15.1919C26.5258 15.1919 26.3901 15.1358 26.2901 15.0357C26.1901 14.9357 26.1339 14.8001 26.1339 14.6586V3.74021C26.1323 3.75088 26.1109 3.73595 26.0752 3.73595H5.92587C5.91421 3.73524 5.90252 3.73691 5.89152 3.74085C5.88052 3.7448 5.87043 3.75093 5.86187 3.75888L5.8672 14.6586C5.8672 14.8001 5.81101 14.9357 5.71099 15.0357C5.61097 15.1358 5.47532 15.1919 5.33387 15.1919C5.19242 15.1919 5.05677 15.1358 4.95675 15.0357C4.85673 14.9357 4.80054 14.8001 4.80054 14.6586V3.73595C4.80891 3.44547 4.93203 3.17014 5.14294 2.97023C5.35384 2.77032 5.63536 2.66211 5.92587 2.66928H26.0752C26.3657 2.66211 26.6472 2.77032 26.8581 2.97023C27.069 3.17014 27.1922 3.44547 27.2005 3.73595V14.6586C27.2005 14.8001 27.1443 14.9357 27.0443 15.0357C26.9443 15.1358 26.8087 15.1919 26.6672 15.1919Z"})]})}),e.jsxs("div",{className:"content",children:[e.jsx("span",{children:"To Send Mail"}),e.jsx("h6",{children:e.jsx("a",{href:"mailto:<EMAIL>",children:"<EMAIL>"})})]})]})]}),e.jsx("div",{className:"newsletter-area",children:e.jsx("form",{children:e.jsxs("div",{className:"form-inner",children:[e.jsx("label",{children:"Our Newsletter"}),e.jsxs("div",{className:"input-area",children:[e.jsx("input",{type:"email",placeholder:"Enter Email"}),e.jsx("button",{type:"submit",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"14",height:"14",viewBox:"0 0 14 14",children:e.jsx("path",{d:"M13.8697 0.129409C13.9314 0.191213 13.9736 0.269783 13.991 0.355362C14.0085 0.44094 14.0004 0.529754 13.9678 0.610771L8.78063 13.5781C8.73492 13.6923 8.65859 13.7917 8.56003 13.8653C8.46148 13.9389 8.34453 13.9839 8.22206 13.9954C8.09958 14.0068 7.97633 13.9842 7.86586 13.9301C7.75539 13.876 7.66199 13.7924 7.59594 13.6887L4.76304 9.23607L0.310438 6.40316C0.206426 6.33718 0.122663 6.24375 0.0683925 6.13318C0.0141218 6.02261 -0.00854707 5.89919 0.00288719 5.77655C0.0143215 5.65391 0.0594144 5.53681 0.13319 5.43817C0.206966 5.33954 0.306557 5.2632 0.420973 5.21759L13.3883 0.0322452C13.4694 -0.000369522 13.5582 -0.00846329 13.6437 0.00896931C13.7293 0.0264019 13.8079 0.0685926 13.8697 0.1303V0.129409ZM5.65267 8.97578L8.11385 12.8427L12.3329 2.29554L5.65267 8.97578ZM11.7027 1.66531L1.1555 5.88436L5.02333 8.34466L11.7027 1.66531Z"})})})]})]})})})]}),e.jsxs("div",{className:"footer-btm",children:[e.jsx("div",{className:"copyright-area",children:e.jsxs("p",{children:["Copyright 2023 ",e.jsx("a",{href:"#",children:"Neckle"})," | Design By ",e.jsx("a",{href:"https://www.egenslab.com/",children:"Egens Lab"})]})}),e.jsxs("div",{className:"social-area",children:[e.jsx("h6",{children:"Follow Neckle:"}),e.jsxs("ul",{children:[e.jsx("li",{children:e.jsx("a",{href:"https://www.facebook.com/",children:e.jsx("i",{className:"bx bxl-facebook"})})}),e.jsx("li",{children:e.jsx("a",{href:"https://twitter.com/",children:e.jsx("i",{className:"bx bxl-twitter"})})}),e.jsx("li",{children:e.jsx("a",{href:"https://www.linkedin.com/",children:e.jsx("i",{className:"bx bxl-linkedin"})})}),e.jsx("li",{children:e.jsx("a",{href:"https://www.instagram.com/",children:e.jsx("i",{className:"bx bxl-instagram-alt"})})})]})]})]})]})}),d0=()=>e.jsxs(V,{children:[e.jsx(Y,{}),e.jsx(T,{children:e.jsx(E,{path:"/:slug/*",element:e.jsx(n0,{})})}),e.jsx(t0,{})]}),c0=P.createRoot(document.getElementById("react-root"));c0.render(e.jsx(d0,{}));
