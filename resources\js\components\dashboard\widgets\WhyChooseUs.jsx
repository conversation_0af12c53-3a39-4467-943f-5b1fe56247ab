import React, { useState, useEffect } from "react";

const WhyChooseUs = ({ widget, handleConfigUpdate }) => {
  const [imagePreview, setImagePreview] = useState("");
  const [imagetwoPreview, setImagetwoPreview] = useState("");
  const [iconImagePreview, setIconImagePreview] = useState("");

  useEffect(() => {
    setImagePreview(widget.config?.image || "");
    setImagetwoPreview(widget.config?.imagetwo || "");
    setIconImagePreview(widget.config?.iconImage || "");
  }, [widget.config]);

  // Generic file handler
  const handleFileChange = (field, previewField) => async (e) => {
    const file = e.target.files[0];
    if (file) {
      const preview = URL.createObjectURL(file);
      setFieldPreview(previewField, preview);

      // Upload file to server
      const formData = new FormData();
      formData.append('image', file);

      try {
        const response = await fetch('/api/page-contents/upload-image', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          handleConfigUpdate(widget.id, field, data.data.url); // Store image URL
        } else {
          alert('Image upload failed: ' + data.message);
        }
      } catch (error) {
        alert('Error uploading image: ' + error.message);
      }
    }
  };

  const setFieldPreview = (field, value) => {
    if (field === "image") setImagePreview(value);
    else if (field === "imagetwo") setImagetwoPreview(value);
    else if (field === "iconImage") setIconImagePreview(value);
  };

  const items = widget.config?.items || [{ title: "", subTitle: "" }];

  const addItem = () => {
    const newItems = [...items, { title: "", subTitle: "" }];
    handleConfigUpdate(widget.id, "items", newItems);
  };

  const removeItem = (index) => {
    const newItems = items.filter((_, i) => i !== index);
    handleConfigUpdate(widget.id, "items", newItems);
  };

  const updateItem = (index, field, value) => {
    const newItems = [...items];
    newItems[index][field] = value;
    handleConfigUpdate(widget.id, "items", newItems);
  };

  return (
    <div className="space-y-4">
      {/* Header */}
      <div className="flex gap-4">
        <div className="flex-1">
          <label>Shoulder</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={widget.config?.shoulder || ""}
            onChange={(e) =>
              handleConfigUpdate(widget.id, "shoulder", e.target.value)
            }
            placeholder="Why Choose Us"
          />
        </div>
        <div className="flex-1">
          <label>Under Title</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={widget.config?.underTitle || ""}
            onChange={(e) =>
              handleConfigUpdate(widget.id, "underTitle", e.target.value)
            }
            placeholder="Sub Title"
          />
        </div>
      </div>

      {/* Images */}
      <div className="flex items-center gap-4">
        <div className="flex-1">
          <label>Upload Image One</label>
          <input
            type="file"
            accept="image/*"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            onChange={handleFileChange("image", "image")}
          />
          {imagePreview && (
            <img
              src={imagePreview}
              alt="Preview"
              className="w-24 h-24 object-cover rounded-md border"
            />
          )}
        </div>

        <div className="flex-1">
          <label>Image Two</label>
          <input
            type="file"
            accept="image/*"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            onChange={handleFileChange("imagetwo", "imagetwo")}
          />
          {imagetwoPreview && (
            <img
              src={imagetwoPreview}
              alt="Preview"
              className="w-24 h-24 object-cover rounded-md border"
            />
          )}
        </div>

        <div className="flex-1">
          <label>Icon Image</label>
          <input
            type="file"
            accept="image/*"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            onChange={handleFileChange("iconImage", "iconImage")}
          />
          {iconImagePreview && (
            <img
              src={iconImagePreview}
              alt="Preview"
              className="w-24 h-24 object-cover rounded-md border"
            />
          )}
        </div>
      </div>

      {/* Review Text */}
      <div className="flex gap-4">
        <label>Review Text</label>
        <input
          type="text"
          className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
          value={widget.config?.reviewText || ""}
          onChange={(e) =>
            handleConfigUpdate(widget.id, "reviewText", e.target.value)
          }
        />
      </div>

      {/* Items */}
      <div className="space-y-4">
        {items.map((item, index) => (
          <div key={index} className="flex gap-3">
            <div className="flex-1">
              <label>Title</label>
              <input
                type="text"
                className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                value={item.title}
                onChange={(e) => updateItem(index, "title", e.target.value)}
              />
            </div>
            <div className="flex-1">
              <label>Sub Title</label>
              <input
                type="text"
                className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                value={item.subTitle}
                onChange={(e) => updateItem(index, "subTitle", e.target.value)}
              />
            </div>
            <button
              type="button"
              className="mt-1 px-3 py-2 bg-red-500 text-white rounded"
              onClick={() => removeItem(index)}
            >
              Remove
            </button>
          </div>
        ))}
        <button
          type="button"
          className="px-4 py-2 bg-blue-500 text-white rounded"
          onClick={addItem}
        >
          Add Item
        </button>
      </div>
    </div>
  );
};

export default WhyChooseUs;
