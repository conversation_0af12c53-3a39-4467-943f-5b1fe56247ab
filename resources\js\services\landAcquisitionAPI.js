import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Accept': 'application/json',
  },
  timeout: 30000, // 30 second timeout
});

// Add auth token interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
api.interceptors.response.use(
    (response) => response,
    (error) => {
      console.error('API Error:', error);
      
      // Handle 401 Unauthorized
      if (error.response?.status === 401) {
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user');
        window.location.href = '/login';
      }
      
      return Promise.reject(error);
    }
  );

export const landAcquisitionAPI = {
    // Get all land acquisitions with pagination and search
    async getAll(params = {}) {
      try {
        const response = await api.get('/land-acquisitions', { params });
        return response.data;
      } catch (error) {
        console.error('Error fetching land acquisitions:', error);
        throw error;
      }
    },
    // Get single land acquisition by ID
    async getById(id) {
      try {
        const response = await api.get(`/land-acquisitions/${id}`);
        return response.data;
      } catch (error) {
        console.error(`Error fetching land acquisition ${id}:`, error);
        throw error;
      }
    },
    // Create new land acquisition
    async create(data) {
      try {
        const response = await api.post('/land-acquisitions', data);
        return response.data;
      } catch (error) {
        console.error('Error creating land acquisition:', error);
        throw error;
      }
    },
    // Update land acquisition
    async update(id, data) {
      try {
        const response = await api.put(`/land-acquisitions/${id}`, data);
        return response.data;
      } catch (error) {
        console.error('Error updating land acquisition:', error);
        throw error;
      }
    },
    // Delete land acquisition
    async delete(id) {
      try {
        const response = await api.delete(`/land-acquisitions/${id}`);
        return response.data;
      } catch (error) {
        console.error('Error deleting land acquisition:', error);
        throw error;
      }
    }
  };
  