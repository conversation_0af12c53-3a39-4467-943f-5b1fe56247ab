<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PaymentTypesTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('payment_types')->insert([
            [
                'name' => 'Cash',
                'description' => 'Cash payment on delivery',
                'is_active' => true,
                'sort_order' => 1,
                'icon' => 'cash-icon.png',   // you can update with your actual icon path
                'color' => '#4CAF50',        // green
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Credit Card',
                'description' => 'Pay using Visa, MasterCard, or AMEX',
                'is_active' => true,
                'sort_order' => 2,
                'icon' => 'credit-card-icon.png',
                'color' => '#2196F3',        // blue
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Bank Transfer',
                'description' => 'Direct bank transfer within 3 business days',
                'is_active' => false,
                'sort_order' => 3,
                'icon' => 'bank-icon.png',
                'color' => '#FF9800',        // orange
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Mobile Payment',
                'description' => 'Pay using mobile wallets (e.g., bKash, Nagad, Rocket)',
                'is_active' => true,
                'sort_order' => 4,
                'icon' => 'mobile-icon.png',
                'color' => '#9C27B0',        // purple
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
