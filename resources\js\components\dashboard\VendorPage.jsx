import React, { useState, useEffect } from 'react';
import { 
  Plus, 
  Filter, 
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import vendorAPI from '../../services/vendorAPI';
import manageTypesAPI from '../../services/manageTypesAPI';
import { showAlert } from '../../utils/sweetAlert';
import VendorStats from './VendorStats';
import VendorSearch from './vendorSearch';
import VendorTable from './VendorTable';
import VendorModal from './VendorModal';

const VendorPage = () => {
  const [vendors, setVendors] = useState([]);
  const [vendorTypes, setVendorTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({
    last_page: 1,
    current_page: 1,
    per_page: 10,
    total: 0
  });
  
  // Filter and search states
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [vendorTypeFilter, setVendorTypeFilter] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [perPage, setPerPage] = useState(10);
  const [sortBy, setSortBy] = useState('created_at');
  const [sortOrder, setSortOrder] = useState('desc');
  
  // Modal states
  const [showModal, setShowModal] = useState(false);
  const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'
  const [selectedVendor, setSelectedVendor] = useState(null);
  const [selectedVendors, setSelectedVendors] = useState([]);

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    phone: '',
    email: '',
    vendor_type_id: '',
    address: '',
    status: 'active'
  });

  // Statistics
  const [statistics, setStatistics] = useState({
    total: 0,
    active: 0,
    inactive: 0,
    by_vendor_type: []
  });

  // Load initial data
  useEffect(() => {
    loadVendors();
    loadVendorTypes();
    loadStatistics();
  }, [currentPage, perPage, searchTerm, statusFilter, vendorTypeFilter, sortBy, sortOrder]);

  const loadVendors = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        per_page: perPage,
        search: searchTerm,
        status: statusFilter,
        vendor_type_id: vendorTypeFilter,
        sort_by: sortBy,
        sort_order: sortOrder
      };

      const response = await vendorAPI.getVendors(params);
      if (response.data?.success) {
        const data = response.data.data || {};
        setVendors(data.data || []);
        setPagination({
          last_page: data.last_page || 1,
          current_page: data.current_page || 1,
          per_page: data.per_page || perPage,
          total: data.total || 0
        });
      } else {
        // Initialize empty state if API call fails
        setVendors([]);
        setPagination({
          last_page: 1,
          current_page: 1,
          per_page: perPage,
          total: 0
        });
      }
    } catch (err) {
      setError('Failed to load vendors');
      console.error('Error loading vendors:', err);
      // Initialize empty state on error
      setVendors([]);
      setPagination({
        last_page: 1,
        current_page: 1, 
        per_page: perPage,
        total: 0
      });
    } finally {
      setLoading(false);
    }
  };

  const loadVendorTypes = async () => {
    try {
      const response = await manageTypesAPI.vendorTypeDropDown();
      if (response.data?.success) {
        setVendorTypes(response.data.data || []);
      }
    } catch (err) {
      console.error('Error loading vendor types:', err);
    }
  };

  const loadStatistics = async () => {
    try {
      const response = await vendorAPI.getVendorsStatistics();
      if (response.data?.success) {
        setStatistics(response.data.data || {});
      }
    } catch (err) {
      console.error('Error loading statistics:', err);
    }
  };

  const handleSearch = (value) => {
    setSearchTerm(value);
    setCurrentPage(1);
  };

  const handleStatusFilter = (status) => {
    setStatusFilter(status);
    setCurrentPage(1);
  };

  const handleVendorTypeFilter = (vendorTypeId) => {
    setVendorTypeFilter(vendorTypeId);
    setCurrentPage(1);
  };

  const openModal = (mode, vendor = null) => {
    setModalMode(mode);
    setSelectedVendor(vendor);
    setSubmitting(false);
    
    if (mode === 'create') {
      setFormData({
        name: '',
        phone: '',
        email: '',
        vendor_type_id: '',
        address: '',
        status: 'active'
      });
    } else if (vendor) {
      setFormData({
        name: vendor.name || '',
        phone: vendor.phone || '',
        email: vendor.email || '',
        vendor_type_id: vendor.vendor_type_id || '',
        address: vendor.address || '',
        status: vendor.status || 'active'
      });
    }
    
    setShowModal(true);
  };

  const closeModal = () => {
    setShowModal(false);
    setSelectedVendor(null);
    setSubmitting(false);
    setFormData({
      name: '',
      phone: '',
      email: '',
      vendor_type_id: '',
      address: '',
      status: 'active'
    });
  };

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.name?.trim()) {
      await showAlert('error', 'Validation Error', 'Vendor name is required');
      return;
    }
    
    if (!formData.vendor_type_id) {
      await showAlert('error', 'Validation Error', 'Vendor type is required');
      return;
    }
    
    setSubmitting(true);
    
    try {
      let response;
      
      if (modalMode === 'create') {
        response = await vendorAPI.createVendor(formData);
      } else if (modalMode === 'edit') {
        response = await vendorAPI.updateVendor(selectedVendor.id, formData);
      }
      
      if (response.data?.success) {
        await showAlert('success', 'Success', `Vendor ${modalMode === 'create' ? 'created' : 'updated'} successfully!`);
        closeModal();
        loadVendors();
        loadStatistics();
      } else {
        await showAlert('error', 'Error', response.data?.message || 'Operation failed');
      }
    } catch (err) {
      console.error('Error submitting form:', err);
      if (err.response?.status === 401) {
        await showAlert('error', 'Unauthorized', 'Please login with proper vendor management permissions');
      } else if (err.response?.status === 403) {
        await showAlert('error', 'Access Denied', 'You do not have permission to create vendors');
      } else {
        await showAlert('error', 'Error', err.response?.data?.message || 'An error occurred');
      }
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (vendorId) => {
    const result = await showAlert('warning', 'Are you sure?', 'This action cannot be undone!', true);
    if (result.isConfirmed) {
      try {
        const response = await vendorAPI.deleteVendor(vendorId);
        if (response.data?.success) {
          await showAlert('success', 'Deleted!', 'Vendor has been deleted successfully.');
          loadVendors();
          loadStatistics();
        }
      } catch (err) {
        console.error('Error deleting vendor:', err);
        await showAlert('error', 'Error', 'Failed to delete vendor');
      }
    }
  };

  const handleBulkStatusUpdate = async (status) => {
    if (selectedVendors.length === 0) {
      await showAlert('warning', 'No Selection', 'Please select vendors to update');
      return;
    }

    const result = await showAlert('warning', 'Confirm Action', `Set selected vendors to ${status}?`, true);
    
    if (result.isConfirmed) {
      try {
        const response = await vendorAPI.bulkStatusUpdate(selectedVendors, status);
        if (response.data?.success) {
          await showAlert('success', 'Updated!', response.data.message);
          setSelectedVendors([]);
          loadVendors();
          loadStatistics();
        }
      } catch (err) {
        console.error('Error updating vendors:', err);
        await showAlert('error', 'Error', 'Failed to update vendors');
      }
    }
  };

  const handleSelectVendor = (vendorId) => {
    setSelectedVendors(prev => 
      prev.includes(vendorId) 
        ? prev.filter(id => id !== vendorId)
        : [...prev, vendorId]
    );
  };

  const handleSelectAll = () => {
    if (selectedVendors.length === vendors.length) {
      setSelectedVendors([]);
    } else {
      setSelectedVendors(vendors.map(vendor => vendor.id));
    }
  };

  if (loading && vendors.length === 0) {
    return (
      <div className="flex items-center justify-center min-h-96">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Vendor Management</h1>
          <p className="text-gray-600 dark:text-gray-400">Manage your vendors and their information</p>
        </div>
        <button
          onClick={() => openModal('create')}
          className="inline-flex items-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors duration-200"
        >
          <Plus className="w-5 h-5 mr-2" />
          Add Vendor
        </button>
      </div>

      {/* Statistics Cards */}
      <VendorStats statistics={statistics} />

      {/* Filters and Search */}
      <Card className="border-gray-200 shadow-sm">
        <CardHeader className="bg-gray-50 border-b">
          <CardTitle className="flex items-center text-lg">
            <Filter className="w-5 h-5 mr-2 text-blue-600" />
            Search & Filter Vendors
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <VendorSearch
            searchTerm={searchTerm}
            statusFilter={statusFilter}
            vendorTypeFilter={vendorTypeFilter}
            perPage={perPage}
            vendorTypes={vendorTypes}
            selectedVendors={selectedVendors}
            onSearch={handleSearch}
            onStatusFilter={handleStatusFilter}
            onVendorTypeFilter={handleVendorTypeFilter}
            onPerPageChange={setPerPage}
            onBulkStatusUpdate={handleBulkStatusUpdate}
          />
        </CardContent>
      </Card>

      {/* Vendors Table */}
      <Card className="border-gray-200 shadow-sm">
        <VendorTable
          vendors={vendors}
          selectedVendors={selectedVendors}
          handleSelectVendor={handleSelectVendor}
          handleSelectAll={handleSelectAll}
          openModal={openModal}
          handleDelete={handleDelete}
        />

      </Card>

      {/* Modal */}
      {showModal && (
          <VendorModal
            showModal={showModal}
            closeModal={closeModal}
            modalMode={modalMode}
            selectedVendor={selectedVendor}
            formData={formData}
            handleInputChange={handleInputChange}
            handleSubmit={handleSubmit}
            submitting={submitting}
            vendorTypes={vendorTypes}
          />

      )}
    </div>
  );
};

export default VendorPage;
