import axios from 'axios';

const API_URL = '/api/menu-positions';

// Create axios instance with default config
const api = axios.create({
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add auth token interceptor
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Menu Position API functions
const menuPositionAPI = {
  // Get all menu positions with pagination and filters
  getAll: async (params = {}) => {
    try {
      const response = await api.get(API_URL, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching menu positions:', error);
      throw error.response?.data || error;
    }
  },
  // Get menu position by ID
  getById: async (id) => {
    try {
      const response = await api.get(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching menu position:', error);
      throw error.response?.data || error;
    }
  },
  // Create new menu position
  create: async (data) => {
    try {
      const response = await api.post(API_URL, data);
      return response.data;
    } catch (error) {
      console.error('Error creating menu position:', error);
      throw error.response?.data || error;
    }
  },
  // Update menu position
  update: async (id, data) => {
    try {
      const response = await api.put(`${API_URL}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating menu position:', error);
      throw error.response?.data || error;
    }
  },
  // Delete menu position
  delete: async (id) => {
    try {
      const response = await api.delete(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting menu position:', error);
      throw error.response?.data || error;
    }
  },
};

export default menuPositionAPI;
