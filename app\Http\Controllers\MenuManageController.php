<?php

namespace App\Http\Controllers;
use Illuminate\Http\JsonResponse;
use App\Models\MenuManage;
use App\Models\Menu;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class MenuManageController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        //
        try {
            $menuManages = MenuManage::when($request->has('menu_position_id'), function($q) use ($request) {
                return $q->where('menu_position_id', $request->menu_position_id);
            })->orderBy('order')->get();
            return response()->json([
                'success' => true,
                'data' => $menuManages
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch menu manages: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //

       
       
        try {
            $validated = $request->validate([
                'menu_position_id' => 'required|exists:menu_positions,id',
                'title' => 'required|string|max:255',
                'slug' => 'nullable|string|max:255',
                'type' => 'required|string',
                'target' => 'required|string',
                'parent_id' => 'nullable', // allow null for parent_id
                'order' => 'required|integer',
                'new_tap' => 'required|boolean',
            ]);

            $title = $request->title;
            $menu = Menu::where('name', $title)->first();
            if ($menu) {
                $validated['slug'] = $menu->slug;
            } else {
                dd('No menu found for this title');
            }
         

            // Treat parent_id=0 as null
            if (isset($validated['parent_id']) && ($validated['parent_id'] === 0 || $validated['parent_id'] === '0')) {
                $validated['parent_id'] = null;
            }

            if (empty($validated['slug'])) {
                $validated['slug'] = $this->generateUniqueSlug($validated['title']);
            }
         
            $menuManage = MenuManage::create($validated);
            return response()->json([
                'success' => true,
                'data' => $menuManage,
                'message' => 'Menu manage created successfully'
            ], 201);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to create menu manage: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(MenuManage $menuManage): JsonResponse
    {
        //
        try {
            return response()->json([
                'success' => true,
                'data' => $menuManage
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch menu manage: ' . $e->getMessage()
            ], 500);
        }   
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, MenuManage $menuManage): JsonResponse

    {
        //
        try {
          
            // Check if this is just an order update (for drag and drop reordering)
           if ($request->has('order') && count($request->all()) === 1) {
                // Add parent_id = null before validation
                $request->merge(['parent_id' => $request->input('parent_id', null)]);

                $validated = $request->validate([
                    'order' => 'required|integer',
                    'parent_id' => 'nullable|exists:menu_manages,id',
                ]);

                $menuManage->update($validated);

                return response()->json([
                    'success' => true,
                    'data' => $menuManage->fresh(),
                    'message' => 'Menu order updated successfully'
                ]);
            }

          
            // Full update validation
            $validated = $request->validate([
                'menu_position_id' => 'sometimes|required|exists:menu_positions,id',
                'title' => 'sometimes|required|string|max:255',
                'slug' => 'nullable|string|max:255',
                'type' => 'sometimes|required|string',
                'target' => 'sometimes|required|string',
                'parent_id' => 'nullable', // allow null for parent_id
                'order' => 'sometimes|required|integer',
                'new_tap' => 'sometimes|required|boolean',
            ]);

            // Treat parent_id=0 as null
            if ($request->has('parent_id') && ($request->input('parent_id') === null || $request->input('parent_id') === '' || $request->input('parent_id') === 'null' || $request->input('parent_id') === 0 || $request->input('parent_id') === '0')) {
                $validated['parent_id'] = null;
            }

            if (empty($validated['slug']) && isset($validated['title'])) {
                $validated['slug'] = $this->generateUniqueSlug($validated['title'], $menuManage->id);
            }

            $menuManage->update($validated);
            return response()->json([
                'success' => true,
                'data' => $menuManage->fresh(),
                'message' => 'Menu manage updated successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update menu manage: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(MenuManage $menuManage):jsonResponse
    {
        //
        try {
            $menuManage->delete();
            return response()->json([
                'success' => true,
                'message' => 'Menu manage deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete menu manage: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Generate a unique slug from the title.
     */
    private function generateUniqueSlug(string $title, ?int $excludeId = null): string
    {
        $slug = Str::slug($title);
        $originalSlug = $slug;
        $count = 1;

        $query = MenuManage::where('slug', $slug);
        if ($excludeId) {
            $query->where('id', '!=', $excludeId);
        }

        while ($query->exists()) {
            $slug = $originalSlug . '-' . $count;
            $count++;
            $query = MenuManage::where('slug', $slug);
            if ($excludeId) {
                $query->where('id', '!=', $excludeId);
            }
        }

        return $slug;
    }

    public function dropDown(Request $request): JsonResponse
    {
        try {
            $menuManages = MenuManage::when($request->has('menu_position_id'), function($q) use ($request) {
                return $q->where('menu_position_id', $request->menu_position_id);
            })->orderBy('order')->get();
            return response()->json([
                'success' => true,
                'data' => $menuManages
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch menu manages: ' . $e->getMessage()
            ], 500);
        }
    }
}
