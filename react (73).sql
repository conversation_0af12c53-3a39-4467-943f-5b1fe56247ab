-- p<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> SQL Dump
-- version 5.2.1
-- https://www.phpmyadmin.net/
--
-- Host: 127.0.0.1
-- Generation Time: Oct 06, 2025 at 11:44 AM
-- Server version: 10.4.32-MariaDB
-- PHP Version: 8.2.12

SET SQL_MODE = "NO_AUTO_VALUE_ON_ZERO";
START TRANSACTION;
SET time_zone = "+00:00";


/*!40101 SET @OLD_CHARACTER_SET_CLIENT=@@CHARACTER_SET_CLIENT */;
/*!40101 SET @OLD_CHARACTER_SET_RESULTS=@@CHARACTER_SET_RESULTS */;
/*!40101 SET @OLD_COLLATION_CONNECTION=@@COLLATION_CONNECTION */;
/*!40101 SET NAMES utf8mb4 */;

--
-- Database: `react`
--

-- --------------------------------------------------------

--
-- Table structure for table `backend_settings`
--

CREATE TABLE `backend_settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `applicationName` varchar(255) DEFAULT NULL,
  `vatTaxRateForCustomers` varchar(255) DEFAULT NULL,
  `vatTaxRateForMerchants` varchar(255) DEFAULT NULL,
  `systemTimeZone` varchar(255) DEFAULT NULL,
  `dateFormat` varchar(255) DEFAULT NULL,
  `commissionFromMerchant` varchar(255) DEFAULT NULL,
  `adminLogo` varchar(255) DEFAULT NULL,
  `invoiceLogo` varchar(255) DEFAULT NULL,
  `driver` varchar(255) DEFAULT NULL,
  `host` varchar(255) DEFAULT NULL,
  `port` varchar(255) DEFAULT NULL,
  `username` varchar(255) DEFAULT NULL,
  `password` varchar(255) DEFAULT NULL,
  `fromAdress` varchar(255) DEFAULT NULL,
  `fromName` varchar(255) DEFAULT NULL,
  `encryption` varchar(255) DEFAULT NULL,
  `enableRecaptcha` varchar(255) DEFAULT NULL,
  `recaptchaKey` varchar(255) DEFAULT NULL,
  `recaptchaSecret` varchar(255) DEFAULT NULL,
  `whatsappChat` varchar(255) DEFAULT NULL,
  `whatsappNumber` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `backend_settings`
--

INSERT INTO `backend_settings` (`id`, `applicationName`, `vatTaxRateForCustomers`, `vatTaxRateForMerchants`, `systemTimeZone`, `dateFormat`, `commissionFromMerchant`, `adminLogo`, `invoiceLogo`, `driver`, `host`, `port`, `username`, `password`, `fromAdress`, `fromName`, `encryption`, `enableRecaptcha`, `recaptchaKey`, `recaptchaSecret`, `whatsappChat`, `whatsappNumber`, `created_at`, `updated_at`) VALUES
(1, 'Oikko', '5', NULL, '(GMT-06:00) Etc/GMT+6', 'dddd, MMMM D, YYYY', '5', '/settings/image/adminLogo_1758804470.svg', '/settings/image/invoiceLogo_1758804470.svg', 'smtp', 'smtp.mailtrap.io', '2525', 'your_mailtrap_user', 'your_mailtrap_pass', '<EMAIL>', 'Info', 'TLS', '1', 'Your recaptcha Key', 'Your recaptcha Secret', '1', '+8801711111111', '2025-09-25 06:46:18', '2025-09-25 06:49:59');

-- --------------------------------------------------------

--
-- Table structure for table `blogs`
--

CREATE TABLE `blogs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `excerpt` text DEFAULT NULL,
  `content` longtext NOT NULL,
  `featured_image` varchar(255) DEFAULT NULL,
  `author_name` varchar(255) NOT NULL,
  `author_email` varchar(255) DEFAULT NULL,
  `author_avatar` varchar(255) DEFAULT NULL,
  `category_id` bigint(20) UNSIGNED NOT NULL,
  `tags` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`tags`)),
  `status` enum('draft','published','archived') NOT NULL DEFAULT 'draft',
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `meta_title` text DEFAULT NULL,
  `meta_description` text DEFAULT NULL,
  `seo_keywords` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`seo_keywords`)),
  `views_count` int(11) NOT NULL DEFAULT 0,
  `likes_count` int(11) NOT NULL DEFAULT 0,
  `shares_count` int(11) NOT NULL DEFAULT 0,
  `published_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `blogs`
--

INSERT INTO `blogs` (`id`, `title`, `slug`, `excerpt`, `content`, `featured_image`, `author_name`, `author_email`, `author_avatar`, `category_id`, `tags`, `status`, `is_featured`, `meta_title`, `meta_description`, `seo_keywords`, `views_count`, `likes_count`, `shares_count`, `published_at`, `created_at`, `updated_at`) VALUES
(1, 'Ultimate Guide to Real Estate Investment in 2024', 'ultimate-guide-real-estate-investment-2024', 'Discover the latest strategies for successful real estate investment in 2024, including market trends, financing options, and expert tips.', '<p>Real estate investment continues to be one of the most reliable ways to build wealth. In 2024, the market presents unique opportunities and challenges that savvy investors need to understand.</p><p>This comprehensive guide covers everything from market analysis to financing strategies, helping both beginners and experienced investors make informed decisions.</p><p>We\'ll explore different types of real estate investments, from residential properties to commercial real estate, and discuss the pros and cons of each approach.</p>', NULL, 'Sarah Johnson', '<EMAIL>', NULL, 2, '\"[\\\"investment\\\",\\\"real estate\\\",\\\"2024\\\",\\\"guide\\\",\\\"property\\\"]\"', 'published', 1, 'Real Estate Investment Guide 2024 - Expert Tips & Strategies', 'Complete guide to real estate investment in 2024. Learn strategies, market analysis, and financing tips from industry experts.', '\"[\\\"real estate investment\\\",\\\"property investment\\\",\\\"real estate 2024\\\",\\\"investment strategies\\\"]\"', 156, 0, 0, '2025-10-01 01:07:48', '2025-10-01 01:07:48', '2025-10-01 01:07:48'),
(2, 'Top 10 Emerging Real Estate Markets to Watch', 'top-10-emerging-real-estate-markets-watch', 'Explore the most promising emerging real estate markets and discover where smart investors are putting their money.', '<p>The real estate landscape is constantly evolving, with new markets emerging as hotspots for investment and development. These markets offer tremendous potential for both residential and commercial investments.</p><p>Our analysis covers demographic trends, economic indicators, and infrastructure development that make these markets particularly attractive for investors.</p><p>From tech hubs to retirement destinations, discover which markets are poised for significant growth in the coming years.</p>', NULL, 'Michael Chen', '<EMAIL>', NULL, 2, '\"[\\\"markets\\\",\\\"emerging\\\",\\\"investment\\\",\\\"trends\\\",\\\"analysis\\\"]\"', 'published', 1, 'Top 10 Emerging Real Estate Markets 2024 - Investment Opportunities', 'Discover the hottest emerging real estate markets with high growth potential. Expert analysis and investment insights.', '\"[\\\"emerging markets\\\",\\\"real estate markets\\\",\\\"investment opportunities\\\",\\\"market analysis\\\"]\"', 243, 0, 0, '2025-09-28 01:07:48', '2025-09-28 01:07:48', '2025-09-28 01:07:48'),
(3, 'Smart Home Technology: Increasing Property Value', 'smart-home-technology-increasing-property-value', 'Learn how smart home technology can significantly increase your property value and attract modern buyers.', '<p>Smart home technology is no longer a luxury—it\'s becoming an expectation for modern homebuyers. Properties equipped with smart features consistently sell for higher prices and faster than traditional homes.</p><p>From automated lighting and climate control to security systems and energy management, smart technology offers both convenience and cost savings.</p><p>Learn which smart home features provide the best return on investment and how to implement them effectively in your properties.</p>', NULL, 'Emily Rodriguez', '<EMAIL>', NULL, 2, '\"[\\\"smart home\\\",\\\"technology\\\",\\\"property value\\\",\\\"modernization\\\",\\\"ROI\\\"]\"', 'published', 0, 'Smart Home Technology ROI - Increase Property Value with Tech', 'Discover how smart home technology can boost property values. Complete guide to smart home ROI and implementation.', '\"[\\\"smart home\\\",\\\"property value\\\",\\\"home technology\\\",\\\"real estate tech\\\"]\"', 89, 0, 0, '2025-09-26 01:07:48', '2025-09-26 01:07:48', '2025-09-26 01:07:48'),
(4, 'Sustainable Building Practices in Modern Construction', 'sustainable-building-practices-modern-construction', 'Discover how sustainable building practices are transforming construction and creating more valuable, eco-friendly properties.', '<p>Sustainability is reshaping the construction industry, with green building practices becoming the new standard. These approaches not only benefit the environment but also result in long-term cost savings and increased property values.</p><p>From energy-efficient materials to water conservation systems, sustainable building practices are revolutionizing how we think about construction and development.</p><p>Explore the latest innovations in sustainable construction and learn how to implement these practices in your next project.</p>', NULL, 'David Kumar', '<EMAIL>', NULL, 2, '\"[\\\"sustainability\\\",\\\"green building\\\",\\\"construction\\\",\\\"environment\\\",\\\"efficiency\\\"]\"', 'published', 0, 'Sustainable Building Practices - Green Construction Guide 2024', 'Complete guide to sustainable building practices in modern construction. Learn green building techniques and benefits.', '\"[\\\"sustainable building\\\",\\\"green construction\\\",\\\"eco-friendly building\\\",\\\"sustainable development\\\"]\"', 67, 0, 0, '2025-09-23 01:07:48', '2025-09-23 01:07:48', '2025-09-23 01:07:48'),
(5, 'First-Time Homebuyer\'s Complete Checklist', 'first-time-homebuyer-complete-checklist', 'Everything first-time homebuyers need to know, from saving for a down payment to closing on your dream home.', '<p>Buying your first home is an exciting milestone, but it can also be overwhelming without proper guidance. This comprehensive checklist ensures you don\'t miss any critical steps in the homebuying process.</p><p>From pre-approval to closing, we\'ll walk you through each stage and provide practical tips to make your homebuying journey smooth and successful.</p><p>Whether you\'re just starting to save for a down payment or ready to make an offer, this guide has something for every stage of the process.</p>', NULL, 'Lisa Thompson', '<EMAIL>', NULL, 2, '\"[\\\"first-time buyer\\\",\\\"homebuying\\\",\\\"checklist\\\",\\\"guide\\\",\\\"mortgage\\\"]\"', 'published', 1, 'First-Time Homebuyer Checklist - Complete Guide 2024', 'Complete checklist for first-time homebuyers. Step-by-step guide from pre-approval to closing.', '\"[\\\"first-time homebuyer\\\",\\\"homebuying checklist\\\",\\\"buying first home\\\",\\\"home purchase guide\\\"]\"', 312, 0, 0, '2025-09-19 01:07:48', '2025-09-19 01:07:48', '2025-09-19 01:07:48'),
(6, 'Real Estate Market Predictions for 2024', 'real-estate-market-predictions-2024', 'Expert analysis and predictions for the 2024 real estate market, including trends, opportunities, and challenges.', '<p>As we move through 2024, the real estate market continues to evolve in response to economic factors, demographic shifts, and changing consumer preferences. Understanding these trends is crucial for making informed decisions.</p><p>Our expert analysis examines interest rate projections, housing supply trends, and regional market variations that will shape the real estate landscape.</p><p>Whether you\'re buying, selling, or investing, these insights will help you navigate the market with confidence.</p>', NULL, 'Robert Martinez', '<EMAIL>', NULL, 2, '\"[\\\"market predictions\\\",\\\"2024\\\",\\\"trends\\\",\\\"analysis\\\",\\\"forecast\\\"]\"', 'published', 0, 'Real Estate Market Predictions 2024 - Expert Analysis & Trends', 'Get expert insights into real estate market predictions for 2024. Analysis of trends, opportunities, and challenges.', '\"[\\\"real estate predictions\\\",\\\"market forecast\\\",\\\"real estate trends 2024\\\",\\\"market analysis\\\"]\"', 0, 0, 0, '2025-10-03 02:48:37', '2025-10-02 01:07:48', '2025-10-03 02:48:37');

-- --------------------------------------------------------

--
-- Table structure for table `cache`
--

CREATE TABLE `cache` (
  `key` varchar(255) NOT NULL,
  `value` mediumtext NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `cache_locks`
--

CREATE TABLE `cache_locks` (
  `key` varchar(255) NOT NULL,
  `owner` varchar(255) NOT NULL,
  `expiration` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `commission_agents`
--

CREATE TABLE `commission_agents` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` varchar(255) DEFAULT NULL,
  `fathers_name` varchar(255) DEFAULT NULL,
  `mothers_name` varchar(255) DEFAULT NULL,
  `nid` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `contractors`
--

CREATE TABLE `contractors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `trade_license` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `address` text DEFAULT NULL,
  `files` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`files`)),
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `contractors`
--

INSERT INTO `contractors` (`id`, `name`, `phone`, `trade_license`, `email`, `address`, `files`, `status`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(3, 'Egens Lab', '+8801871769835', '465465', '<EMAIL>', '51,Arjotpara,Mohakhali Dhaka', '[{\"name\":\"download.png\",\"path\":\"contractor-documents\\/1753764987_download.png\",\"url\":\"\\/storage\\/contractor-documents\\/1753764987_download.png\",\"size\":3711,\"mime_type\":\"image\\/png\"}]', 'active', 1, 1, '2025-07-28 22:55:58', '2025-07-28 22:56:27'),
(4, 'new contractor', '+880171765465', 'alkdjlk', '<EMAIL>', 'Mirpur DOHS', '[{\"name\":\"********** (2).jpg\",\"path\":\"contractor-documents\\/1753771687_********** (2).jpg\",\"url\":\"\\/storage\\/contractor-documents\\/1753771687_********** (2).jpg\",\"size\":317768,\"mime_type\":\"image\\/jpeg\"}]', 'active', 1, 1, '2025-07-29 00:48:07', '2025-07-29 00:48:07'),
(6, 'Shifat E Rasul', '+8801871769835333', '234', '<EMAIL>', '51,Arjotpara,Mohakhali Dhaka', '[{\"name\":\"download (1).png\",\"path\":\"contractor-documents\\/1757054963_download (1).png\",\"url\":\"\\/storage\\/contractor-documents\\/1757054963_download (1).png\",\"size\":86473,\"mime_type\":\"image\\/png\"}]', 'active', 1, 1, '2025-09-05 00:49:23', '2025-09-05 00:49:23'),
(7, 'Shifat E Rasul', '+88018717698335', '465465asdf', '<EMAIL>', '51,Arjotpara,Mohakhali Dhaka', '[{\"name\":\"5958af3e-5bc9-4993-a21c-7e7f9a422caf.pdf\",\"path\":\"contractor-documents\\/1757677292_5958af3e-5bc9-4993-a21c-7e7f9a422caf.pdf\",\"url\":\"\\/storage\\/contractor-documents\\/1757677292_5958af3e-5bc9-4993-a21c-7e7f9a422caf.pdf\",\"size\":80233,\"mime_type\":\"application\\/pdf\"}]', 'active', 1, 1, '2025-09-12 05:41:33', '2025-09-12 05:41:33');

-- --------------------------------------------------------

--
-- Table structure for table `countries`
--

CREATE TABLE `countries` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(2) NOT NULL,
  `iso_code` varchar(3) NOT NULL,
  `capital` varchar(255) DEFAULT NULL,
  `currency` varchar(3) DEFAULT NULL,
  `phone_code` varchar(10) DEFAULT NULL,
  `continent` varchar(255) DEFAULT NULL,
  `population` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `countries`
--

INSERT INTO `countries` (`id`, `name`, `code`, `iso_code`, `capital`, `currency`, `phone_code`, `continent`, `population`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Aruba', 'AW', 'ABW', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(2, 'Afghanistan', 'AF', 'AFG', 'Kabul', 'AFN', '+93', 'Asia', '38928341', 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(3, 'Angola', 'AO', 'AGO', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(4, 'Anguilla', 'AI', 'AIA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(5, 'Åland Islands', 'AX', 'ALA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(6, 'Albania', 'AL', 'ALB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(7, 'Andorra', 'AD', 'AND', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(8, 'United Arab Emirates', 'AE', 'ARE', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(9, 'Argentina', 'AR', 'ARG', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(10, 'Armenia', 'AM', 'ARM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(11, 'American Samoa', 'AS', 'ASM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(12, 'Antarctica', 'AQ', 'ATA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(13, 'French Southern Territories', 'TF', 'ATF', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(14, 'Antigua and Barbuda', 'AG', 'ATG', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(15, 'Australia', 'AU', 'AUS', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(16, 'Austria', 'AT', 'AUT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(17, 'Azerbaijan', 'AZ', 'AZE', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(18, 'Burundi', 'BI', 'BDI', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(19, 'Belgium', 'BE', 'BEL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(20, 'Benin', 'BJ', 'BEN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(21, 'Bonaire, Sint Eustatius and Saba', 'BQ', 'BES', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(22, 'Burkina Faso', 'BF', 'BFA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(23, 'Bangladesh', 'BD', 'BGD', 'Dhaka', 'BDT', '+880', 'Asia', '164689383', 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(24, 'Bulgaria', 'BG', 'BGR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(25, 'Bahrain', 'BH', 'BHR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(26, 'Bahamas', 'BS', 'BHS', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(27, 'Bosnia and Herzegovina', 'BA', 'BIH', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(28, 'Saint Barthélemy', 'BL', 'BLM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(29, 'Belarus', 'BY', 'BLR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(30, 'Belize', 'BZ', 'BLZ', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(31, 'Bermuda', 'BM', 'BMU', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(32, 'Bolivia, Plurinational State of', 'BO', 'BOL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(33, 'Brazil', 'BR', 'BRA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(34, 'Barbados', 'BB', 'BRB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(35, 'Brunei Darussalam', 'BN', 'BRN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(36, 'Bhutan', 'BT', 'BTN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(37, 'Bouvet Island', 'BV', 'BVT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(38, 'Botswana', 'BW', 'BWA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(39, 'Central African Republic', 'CF', 'CAF', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(40, 'Canada', 'CA', 'CAN', 'Ottawa', 'CAD', '+1', 'North America', '37742154', 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(41, 'Cocos (Keeling) Islands', 'CC', 'CCK', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(42, 'Switzerland', 'CH', 'CHE', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(43, 'Chile', 'CL', 'CHL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(44, 'China', 'CN', 'CHN', 'Beijing', 'CNY', '+86', 'Asia', '1439323776', 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(45, 'Côte d', 'CI', 'CIV', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(46, 'Cameroon', 'CM', 'CMR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(47, 'Congo, The Democratic Republic of the', 'CD', 'COD', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(48, 'Congo', 'CG', 'COG', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(49, 'Cook Islands', 'CK', 'COK', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(50, 'Colombia', 'CO', 'COL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(51, 'Comoros', 'KM', 'COM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(52, 'Cabo Verde', 'CV', 'CPV', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(53, 'Costa Rica', 'CR', 'CRI', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(54, 'Cuba', 'CU', 'CUB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(55, 'Curaçao', 'CW', 'CUW', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(56, 'Christmas Island', 'CX', 'CXR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(57, 'Cayman Islands', 'KY', 'CYM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(58, 'Cyprus', 'CY', 'CYP', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(59, 'Czechia', 'CZ', 'CZE', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(60, 'Germany', 'DE', 'DEU', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(61, 'Djibouti', 'DJ', 'DJI', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(62, 'Dominica', 'DM', 'DMA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(63, 'Denmark', 'DK', 'DNK', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(64, 'Dominican Republic', 'DO', 'DOM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(65, 'Algeria', 'DZ', 'DZA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(66, 'Ecuador', 'EC', 'ECU', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(67, 'Egypt', 'EG', 'EGY', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(68, 'Eritrea', 'ER', 'ERI', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(69, 'Western Sahara', 'EH', 'ESH', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(70, 'Spain', 'ES', 'ESP', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(71, 'Estonia', 'EE', 'EST', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(72, 'Ethiopia', 'ET', 'ETH', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(73, 'Finland', 'FI', 'FIN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(74, 'Fiji', 'FJ', 'FJI', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(75, 'Falkland Islands (Malvinas)', 'FK', 'FLK', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(76, 'France', 'FR', 'FRA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(77, 'Faroe Islands', 'FO', 'FRO', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(78, 'Micronesia, Federated States of', 'FM', 'FSM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(79, 'Gabon', 'GA', 'GAB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(80, 'United Kingdom', 'GB', 'GBR', 'London', 'GBP', '+44', 'Europe', '67886011', 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(81, 'Georgia', 'GE', 'GEO', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(82, 'Guernsey', 'GG', 'GGY', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(83, 'Ghana', 'GH', 'GHA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(84, 'Gibraltar', 'GI', 'GIB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(85, 'Guinea', 'GN', 'GIN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(86, 'Guadeloupe', 'GP', 'GLP', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(87, 'Gambia', 'GM', 'GMB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(88, 'Guinea-Bissau', 'GW', 'GNB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(89, 'Equatorial Guinea', 'GQ', 'GNQ', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(90, 'Greece', 'GR', 'GRC', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(91, 'Grenada', 'GD', 'GRD', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(92, 'Greenland', 'GL', 'GRL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(93, 'Guatemala', 'GT', 'GTM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(94, 'French Guiana', 'GF', 'GUF', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(95, 'Guam', 'GU', 'GUM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(96, 'Guyana', 'GY', 'GUY', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(97, 'Hong Kong', 'HK', 'HKG', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(98, 'Heard Island and McDonald Islands', 'HM', 'HMD', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(99, 'Honduras', 'HN', 'HND', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(100, 'Croatia', 'HR', 'HRV', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(101, 'Haiti', 'HT', 'HTI', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(102, 'Hungary', 'HU', 'HUN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(103, 'Indonesia', 'ID', 'IDN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(104, 'Isle of Man', 'IM', 'IMN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(105, 'India', 'IN', 'IND', 'New Delhi', 'INR', '+91', 'Asia', '1380004385', 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(106, 'British Indian Ocean Territory', 'IO', 'IOT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(107, 'Ireland', 'IE', 'IRL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(108, 'Iran, Islamic Republic of', 'IR', 'IRN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(109, 'Iraq', 'IQ', 'IRQ', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(110, 'Iceland', 'IS', 'ISL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(111, 'Israel', 'IL', 'ISR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(112, 'Italy', 'IT', 'ITA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(113, 'Jamaica', 'JM', 'JAM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(114, 'Jersey', 'JE', 'JEY', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(115, 'Jordan', 'JO', 'JOR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(116, 'Japan', 'JP', 'JPN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(117, 'Kazakhstan', 'KZ', 'KAZ', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(118, 'Kenya', 'KE', 'KEN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(119, 'Kyrgyzstan', 'KG', 'KGZ', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(120, 'Cambodia', 'KH', 'KHM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(121, 'Kiribati', 'KI', 'KIR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(122, 'Saint Kitts and Nevis', 'KN', 'KNA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(123, 'Korea, Republic of', 'KR', 'KOR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(124, 'Kuwait', 'KW', 'KWT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(125, 'Lao People', 'LA', 'LAO', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(126, 'Lebanon', 'LB', 'LBN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(127, 'Liberia', 'LR', 'LBR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(128, 'Libya', 'LY', 'LBY', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(129, 'Saint Lucia', 'LC', 'LCA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(130, 'Liechtenstein', 'LI', 'LIE', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(131, 'Sri Lanka', 'LK', 'LKA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(132, 'Lesotho', 'LS', 'LSO', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(133, 'Lithuania', 'LT', 'LTU', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(134, 'Luxembourg', 'LU', 'LUX', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(135, 'Latvia', 'LV', 'LVA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(136, 'Macao', 'MO', 'MAC', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(137, 'Saint Martin (French part)', 'MF', 'MAF', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(138, 'Morocco', 'MA', 'MAR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(139, 'Monaco', 'MC', 'MCO', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(140, 'Moldova, Republic of', 'MD', 'MDA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(141, 'Madagascar', 'MG', 'MDG', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(142, 'Maldives', 'MV', 'MDV', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(143, 'Mexico', 'MX', 'MEX', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(144, 'Marshall Islands', 'MH', 'MHL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(145, 'North Macedonia', 'MK', 'MKD', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(146, 'Mali', 'ML', 'MLI', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(147, 'Malta', 'MT', 'MLT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(148, 'Myanmar', 'MM', 'MMR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(149, 'Montenegro', 'ME', 'MNE', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(150, 'Mongolia', 'MN', 'MNG', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(151, 'Northern Mariana Islands', 'MP', 'MNP', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(152, 'Mozambique', 'MZ', 'MOZ', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(153, 'Mauritania', 'MR', 'MRT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(154, 'Montserrat', 'MS', 'MSR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(155, 'Martinique', 'MQ', 'MTQ', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(156, 'Mauritius', 'MU', 'MUS', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(157, 'Malawi', 'MW', 'MWI', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(158, 'Malaysia', 'MY', 'MYS', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(159, 'Mayotte', 'YT', 'MYT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(160, 'Namibia', 'NA', 'NAM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(161, 'New Caledonia', 'NC', 'NCL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(162, 'Niger', 'NE', 'NER', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(163, 'Norfolk Island', 'NF', 'NFK', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(164, 'Nigeria', 'NG', 'NGA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(165, 'Nicaragua', 'NI', 'NIC', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(166, 'Niue', 'NU', 'NIU', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(167, 'Netherlands', 'NL', 'NLD', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(168, 'Norway', 'NO', 'NOR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(169, 'Nepal', 'NP', 'NPL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(170, 'Nauru', 'NR', 'NRU', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(171, 'New Zealand', 'NZ', 'NZL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(172, 'Oman', 'OM', 'OMN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(173, 'Pakistan', 'PK', 'PAK', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(174, 'Panama', 'PA', 'PAN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(175, 'Pitcairn', 'PN', 'PCN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(176, 'Peru', 'PE', 'PER', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(177, 'Philippines', 'PH', 'PHL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(178, 'Palau', 'PW', 'PLW', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(179, 'Papua New Guinea', 'PG', 'PNG', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(180, 'Poland', 'PL', 'POL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(181, 'Puerto Rico', 'PR', 'PRI', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(182, 'Korea, Democratic', 'KP', 'PRK', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(183, 'Portugal', 'PT', 'PRT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(184, 'Paraguay', 'PY', 'PRY', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(185, 'Palestine, State of', 'PS', 'PSE', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(186, 'French Polynesia', 'PF', 'PYF', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(187, 'Qatar', 'QA', 'QAT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(188, 'Réunion', 'RE', 'REU', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(189, 'Romania', 'RO', 'ROU', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(190, 'Russian Federation', 'RU', 'RUS', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(191, 'Rwanda', 'RW', 'RWA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(192, 'Saudi Arabia', 'SA', 'SAU', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(193, 'Sudan', 'SD', 'SDN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(194, 'Senegal', 'SN', 'SEN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(195, 'Singapore', 'SG', 'SGP', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(196, 'South Georgia and the South Sandwich Islands', 'GS', 'SGS', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(197, 'Saint Helena, Ascension and Tristan da Cunha', 'SH', 'SHN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(198, 'Svalbard and Jan Mayen', 'SJ', 'SJM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(199, 'Solomon Islands', 'SB', 'SLB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(200, 'Sierra Leone', 'SL', 'SLE', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(201, 'El Salvador', 'SV', 'SLV', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(202, 'San Marino', 'SM', 'SMR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(203, 'Somalia', 'SO', 'SOM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(204, 'Saint Pierre and Miquelon', 'PM', 'SPM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(205, 'Serbia', 'RS', 'SRB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(206, 'South Sudan', 'SS', 'SSD', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(207, 'Sao Tome and Principe', 'ST', 'STP', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(208, 'Suriname', 'SR', 'SUR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(209, 'Slovakia', 'SK', 'SVK', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(210, 'Slovenia', 'SI', 'SVN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(211, 'Sweden', 'SE', 'SWE', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(212, 'Eswatini', 'SZ', 'SWZ', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(213, 'Sint Maarten (Dutch part)', 'SX', 'SXM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(214, 'Seychelles', 'SC', 'SYC', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(215, 'Syrian Arab Republic', 'SY', 'SYR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(216, 'Turks and Caicos Islands', 'TC', 'TCA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(217, 'Chad', 'TD', 'TCD', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(218, 'Togo', 'TG', 'TGO', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(219, 'Thailand', 'TH', 'THA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(220, 'Tajikistan', 'TJ', 'TJK', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(221, 'Tokelau', 'TK', 'TKL', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(222, 'Turkmenistan', 'TM', 'TKM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(223, 'Timor-Leste', 'TL', 'TLS', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(224, 'Tonga', 'TO', 'TON', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(225, 'Trinidad and Tobago', 'TT', 'TTO', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(226, 'Tunisia', 'TN', 'TUN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(227, 'Turkey', 'TR', 'TUR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(228, 'Tuvalu', 'TV', 'TUV', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(229, 'Taiwan, Province of China', 'TW', 'TWN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(230, 'Tanzania, United Republic of', 'TZ', 'TZA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(231, 'Uganda', 'UG', 'UGA', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(232, 'Ukraine', 'UA', 'UKR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(233, 'United States Minor Outlying Islands', 'UM', 'UMI', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(234, 'Uruguay', 'UY', 'URY', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(235, 'United States', 'US', 'USA', 'Washington, D.C.', 'USD', '+1', 'North America', '331002651', 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(236, 'Uzbekistan', 'UZ', 'UZB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(237, 'Holy See (Vatican City State)', 'VA', 'VAT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(238, 'Saint Vincent and the Grenadines', 'VC', 'VCT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(239, 'Venezuela, Bolivarian Republic of', 'VE', 'VEN', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(240, 'Virgin Islands, British', 'VG', 'VGB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(241, 'Virgin Islands, U.S.', 'VI', 'VIR', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(242, 'Viet Nam', 'VN', 'VNM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(243, 'Vanuatu', 'VU', 'VUT', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(244, 'Wallis and Futuna', 'WF', 'WLF', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(245, 'Samoa', 'WS', 'WSM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(246, 'Yemen', 'YE', 'YEM', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(247, 'South Africa', 'ZA', 'ZAF', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(248, 'Zambia', 'ZM', 'ZMB', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(249, 'Zimbabwe', 'ZW', 'ZWE', NULL, NULL, NULL, NULL, NULL, 'active', '2025-07-18 04:46:25', '2025-07-18 04:46:25');

-- --------------------------------------------------------

--
-- Table structure for table `currencies`
--

CREATE TABLE `currencies` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(3) NOT NULL,
  `symbol` varchar(10) NOT NULL,
  `exchange_rate` decimal(10,4) NOT NULL DEFAULT 1.0000,
  `is_default` tinyint(1) NOT NULL DEFAULT 0,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `currencies`
--

INSERT INTO `currencies` (`id`, `name`, `code`, `symbol`, `exchange_rate`, `is_default`, `is_active`, `created_at`, `updated_at`) VALUES
(1, 'DS', 'USD', '$', 1.0000, 1, 1, '2025-09-25 00:33:25', '2025-09-25 00:33:44');

-- --------------------------------------------------------

--
-- Table structure for table `customers`
--

CREATE TABLE `customers` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `address` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `customers`
--

INSERT INTO `customers` (`id`, `name`, `email`, `phone`, `status`, `address`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'Customer 1', '<EMAIL>', '01575302123', 'inactive', NULL, '2025-08-04 01:02:06', '2025-08-17 23:03:12', NULL),
(2, 'Customer 2', '<EMAIL>', '01514945799', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(3, 'Customer 3', '<EMAIL>', '01580225622', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(4, 'Customer 4', '<EMAIL>', '01598217393', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(5, 'Customer 5', '<EMAIL>', '01541848936', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(6, 'Customer 6', '<EMAIL>', '01586612658', 'inactive', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(7, 'Customer 7', '<EMAIL>', '01540823736', 'inactive', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(8, 'Customer 8', '<EMAIL>', '01587881265', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(9, 'Customer 9', '<EMAIL>', '01555813075', 'inactive', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(10, 'Customer 10', '<EMAIL>', '01525755101', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(11, 'Customer 11', '<EMAIL>', '01541743283', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(12, 'Customer 12', '<EMAIL>', '01543151255', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(13, 'Customer 13', '<EMAIL>', '01592901274', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(14, 'Customer 14', '<EMAIL>', '01548775802', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(15, 'Customer 15', '<EMAIL>', '01503713768', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(16, 'Customer 16', '<EMAIL>', '01503609383', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(17, 'Customer 17', '<EMAIL>', '01530028683', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(18, 'Customer 18', '<EMAIL>', '01546327434', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(19, 'Customer 19', '<EMAIL>', '01539888575', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(20, 'Customer 20', '<EMAIL>', '01575386403', 'active', NULL, '2025-08-04 01:02:06', '2025-08-04 01:02:06', NULL),
(23, 'Shifat E Rasul', '<EMAIL>', '0187176983555', 'active', NULL, '2025-08-04 02:45:39', '2025-08-04 02:45:57', '2025-08-04 02:45:57'),
(24, 'asdf', '<EMAIL>', '01871769835', 'active', NULL, '2025-08-17 23:02:58', '2025-08-17 23:03:05', '2025-08-17 23:03:05');

-- --------------------------------------------------------

--
-- Table structure for table `development_costings`
--

CREATE TABLE `development_costings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `cost_category` varchar(255) NOT NULL,
  `cost_subcategory` varchar(255) DEFAULT NULL,
  `description` varchar(255) NOT NULL,
  `vendor_reference` varchar(255) DEFAULT NULL,
  `invoice_number` varchar(255) DEFAULT NULL,
  `cost_date` date NOT NULL,
  `budgeted_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `actual_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `paid_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `pending_amount` decimal(15,2) NOT NULL DEFAULT 0.00,
  `payment_status` enum('pending','partial','paid','overdue') NOT NULL DEFAULT 'pending',
  `approval_status` enum('draft','pending_approval','approved','rejected') NOT NULL DEFAULT 'draft',
  `notes` text DEFAULT NULL,
  `supporting_documents` varchar(255) DEFAULT NULL,
  `due_date` date DEFAULT NULL,
  `is_recurring` tinyint(1) NOT NULL DEFAULT 0,
  `recurring_frequency` enum('monthly','quarterly','yearly') DEFAULT NULL,
  `recurring_count` int(11) DEFAULT NULL,
  `priority` enum('low','medium','high','urgent') NOT NULL DEFAULT 'medium',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `approved_by` bigint(20) UNSIGNED DEFAULT NULL,
  `approved_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `employees`
--

CREATE TABLE `employees` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `designation` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `email` varchar(255) NOT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `date_of_joining` timestamp NULL DEFAULT NULL,
  `address` text DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `salary` decimal(10,2) DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `employees`
--

INSERT INTO `employees` (`id`, `name`, `designation`, `phone`, `email`, `status`, `date_of_joining`, `address`, `photo`, `salary`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(3, 'Shifat E Rasul Ullash', 'Software Engineer', '01871769835', '<EMAIL>', 'active', '2025-07-07 18:00:00', '51,Arjotpara,Mohakhali Dhaka', '1753705612_80FQNmDPiB.jpg', 25000.00, 1, 1, '2025-07-28 06:26:52', '2025-07-28 06:26:52');

-- --------------------------------------------------------

--
-- Table structure for table `failed_jobs`
--

CREATE TABLE `failed_jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `uuid` varchar(255) NOT NULL,
  `connection` text NOT NULL,
  `queue` text NOT NULL,
  `payload` longtext NOT NULL,
  `exception` longtext NOT NULL,
  `failed_at` timestamp NOT NULL DEFAULT current_timestamp()
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `frontend_settings`
--

CREATE TABLE `frontend_settings` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `logo` varchar(255) DEFAULT NULL,
  `phone` text DEFAULT NULL,
  `addPropertyButton` int(11) DEFAULT NULL,
  `btnUrl` varchar(255) DEFAULT NULL,
  `showPreloader` varchar(255) DEFAULT NULL,
  `showTopBar` int(11) DEFAULT NULL,
  `primaryColor` varchar(255) DEFAULT NULL,
  `secondaryColor` varchar(255) DEFAULT NULL,
  `facebook` varchar(255) DEFAULT NULL,
  `twitter` varchar(255) DEFAULT NULL,
  `youtube` varchar(255) DEFAULT NULL,
  `instagram` varchar(255) DEFAULT NULL,
  `linkedin` varchar(255) DEFAULT NULL,
  `footerLogo` varchar(255) DEFAULT NULL,
  `footerPhone` text DEFAULT NULL,
  `footerEmail` varchar(255) DEFAULT NULL,
  `copyRightText` text DEFAULT NULL,
  `breadcumImage` varchar(255) DEFAULT NULL,
  `breadcumColor` varchar(255) DEFAULT NULL,
  `metaImage` varchar(255) DEFAULT NULL,
  `MetaTitle` varchar(255) DEFAULT NULL,
  `meta_key_word` text DEFAULT NULL,
  `metaDescription` text DEFAULT NULL,
  `googleAnalytics` varchar(255) DEFAULT NULL,
  `googleClientID` varchar(255) DEFAULT NULL,
  `googleClientSecret` varchar(255) DEFAULT NULL,
  `googleRedirectionUrl` varchar(255) DEFAULT NULL,
  `googleLoginClientId` varchar(255) DEFAULT NULL,
  `googleLoginClientSecret` varchar(255) DEFAULT NULL,
  `googleLoginRedirectionUrl` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `frontend_settings`
--

INSERT INTO `frontend_settings` (`id`, `logo`, `phone`, `addPropertyButton`, `btnUrl`, `showPreloader`, `showTopBar`, `primaryColor`, `secondaryColor`, `facebook`, `twitter`, `youtube`, `instagram`, `linkedin`, `footerLogo`, `footerPhone`, `footerEmail`, `copyRightText`, `breadcumImage`, `breadcumColor`, `metaImage`, `MetaTitle`, `meta_key_word`, `metaDescription`, `googleAnalytics`, `googleClientID`, `googleClientSecret`, `googleRedirectionUrl`, `googleLoginClientId`, `googleLoginClientSecret`, `googleLoginRedirectionUrl`, `created_at`, `updated_at`) VALUES
(1, '/settings/image/logo_1759216538.svg', '+990-737 621 432', 1, '/property-list/deatils', '1', 0, '#000000', '#010818', 'facebook.com', 'twitter.com', 'youtube.com', 'instagram.com', 'linkedin.com', '/settings/image/footerLogo_1758795492.svg', '234234', '<EMAIL>', 'adfasdf', '/settings/image/BreadcumImage_1758795572.png', '#010c23', '/settings/image/metaImage_1758795584.svg', 'aaserwba', 'Oikko,Real Estate', '<p>adfasdfadf</p>', 'adfadf', 'adfad', 'fadfa', 'dfa', 'aweraadfa', NULL, NULL, '2025-09-24 23:56:56', '2025-09-30 05:34:17');

-- --------------------------------------------------------

--
-- Table structure for table `invoices`
--

CREATE TABLE `invoices` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `invoice_type` int(11) NOT NULL,
  `invoice_number` varchar(255) NOT NULL,
  `invoice_date` date NOT NULL,
  `customer_id` int(11) DEFAULT NULL,
  `tenant_id` int(11) DEFAULT NULL,
  `property_id` int(11) DEFAULT NULL,
  `amount` decimal(10,2) DEFAULT NULL,
  `grand_total` decimal(10,2) DEFAULT NULL,
  `discount` decimal(10,2) DEFAULT NULL,
  `is_percentage` int(10) NOT NULL,
  `payment_method_id` int(11) NOT NULL,
  `payment_Type_id` int(11) DEFAULT NULL,
  `payment_amount` decimal(10,2) DEFAULT NULL,
  `due_amount` decimal(10,2) DEFAULT NULL,
  `terms_and_conditions` text DEFAULT NULL,
  `payment_status_id` int(11) NOT NULL,
  `due_date` date DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_by` int(11) DEFAULT NULL,
  `updated_by` int(11) DEFAULT NULL,
  `soft_deleted` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `invoices`
--

INSERT INTO `invoices` (`id`, `invoice_type`, `invoice_number`, `invoice_date`, `customer_id`, `tenant_id`, `property_id`, `amount`, `grand_total`, `discount`, `is_percentage`, `payment_method_id`, `payment_Type_id`, `payment_amount`, `due_amount`, `terms_and_conditions`, `payment_status_id`, `due_date`, `notes`, `created_by`, `updated_by`, `soft_deleted`, `created_at`, `updated_at`) VALUES
(31, 2, 'INV-9728', '2025-08-26', 2, NULL, 1, 1156.00, 1213.80, 0.00, 0, 1, 1, NULL, NULL, NULL, 1, '2025-09-06', NULL, NULL, NULL, 0, '2025-08-28 03:52:24', '2025-08-28 03:52:24'),
(32, 2, 'INV-2647', '2025-09-17', 3, NULL, 1, 1156.00, 1213.80, NULL, 0, 1, 1, NULL, NULL, NULL, 1, '2025-09-27', NULL, NULL, NULL, 0, '2025-09-03 23:40:48', '2025-09-03 23:40:48');

-- --------------------------------------------------------

--
-- Table structure for table `invoice_items`
--

CREATE TABLE `invoice_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `invoice_id` bigint(20) UNSIGNED NOT NULL,
  `item_name` varchar(255) DEFAULT NULL,
  `qty` int(11) NOT NULL DEFAULT 1,
  `item_price` decimal(12,2) NOT NULL DEFAULT 0.00,
  `item_tax` decimal(12,2) NOT NULL DEFAULT 0.00,
  `item_total_price` decimal(12,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `jobs`
--

CREATE TABLE `jobs` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `queue` varchar(255) NOT NULL,
  `payload` longtext NOT NULL,
  `attempts` tinyint(3) UNSIGNED NOT NULL,
  `reserved_at` int(10) UNSIGNED DEFAULT NULL,
  `available_at` int(10) UNSIGNED NOT NULL,
  `created_at` int(10) UNSIGNED NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `job_batches`
--

CREATE TABLE `job_batches` (
  `id` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `total_jobs` int(11) NOT NULL,
  `pending_jobs` int(11) NOT NULL,
  `failed_jobs` int(11) NOT NULL,
  `failed_job_ids` longtext NOT NULL,
  `options` mediumtext DEFAULT NULL,
  `cancelled_at` int(11) DEFAULT NULL,
  `created_at` int(11) NOT NULL,
  `finished_at` int(11) DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `land_acquisitions`
--

CREATE TABLE `land_acquisitions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `land_owner_id` bigint(20) UNSIGNED DEFAULT NULL,
  `land_size_decimal` decimal(10,4) DEFAULT NULL,
  `mauza` varchar(255) DEFAULT NULL,
  `land_size` decimal(10,2) DEFAULT NULL,
  `acquisition_price` decimal(15,2) DEFAULT NULL,
  `khatian_number` varchar(255) DEFAULT NULL,
  `dag_number` varchar(255) DEFAULT NULL,
  `cs_khatian` varchar(255) DEFAULT NULL,
  `sa_khatian` varchar(255) DEFAULT NULL,
  `rs_khatian` varchar(255) DEFAULT NULL,
  `bs_khatian` varchar(255) DEFAULT NULL,
  `full_address` text DEFAULT NULL,
  `purchase_price` decimal(15,2) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('active','sold','disputed','inactive') NOT NULL DEFAULT 'active',
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `land_acquisitions`
--

INSERT INTO `land_acquisitions` (`id`, `land_owner_id`, `land_size_decimal`, `mauza`, `land_size`, `acquisition_price`, `khatian_number`, `dag_number`, `cs_khatian`, `sa_khatian`, `rs_khatian`, `bs_khatian`, `full_address`, `purchase_price`, `notes`, `status`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(10, 11, NULL, '23', 23.00, 23.00, '322', '2300', '32', '32', '23', '23', NULL, NULL, NULL, 'active', NULL, NULL, '2025-09-04 23:32:12', '2025-09-04 23:32:39'),
(11, 10, NULL, 'fasdf', 32.00, 234.00, 'adsfasd', 'adf', 'dsa', 'afasdf', 'adf', 'adfad', NULL, NULL, NULL, 'active', NULL, NULL, '2025-09-12 05:40:54', '2025-09-12 05:40:54');

-- --------------------------------------------------------

--
-- Table structure for table `land_addresses`
--

CREATE TABLE `land_addresses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `land_acquisition_id` bigint(20) UNSIGNED NOT NULL,
  `country_id` bigint(20) UNSIGNED DEFAULT NULL,
  `state_id` bigint(20) UNSIGNED DEFAULT NULL,
  `city_text` varchar(255) DEFAULT NULL,
  `city` varchar(255) DEFAULT NULL COMMENT 'City name as text',
  `specific_address` text DEFAULT NULL,
  `address_type` varchar(255) NOT NULL DEFAULT 'location',
  `address_line_1` text DEFAULT NULL,
  `address_line_2` text DEFAULT NULL,
  `village` varchar(255) DEFAULT NULL,
  `union_ward` varchar(255) DEFAULT NULL,
  `upazila_thana` varchar(255) DEFAULT NULL,
  `district` varchar(255) DEFAULT NULL,
  `division` varchar(255) DEFAULT NULL,
  `country` varchar(255) NOT NULL DEFAULT 'Bangladesh',
  `postal_code` varchar(255) DEFAULT NULL,
  `latitude` decimal(10,8) DEFAULT NULL,
  `longitude` decimal(11,8) DEFAULT NULL,
  `coordinate_system` varchar(255) DEFAULT NULL,
  `north_boundary` text DEFAULT NULL,
  `south_boundary` text DEFAULT NULL,
  `east_boundary` text DEFAULT NULL,
  `west_boundary` text DEFAULT NULL,
  `nearest_landmark` varchar(255) DEFAULT NULL,
  `distance_from_road` decimal(8,2) DEFAULT NULL,
  `road_type` varchar(255) DEFAULT NULL,
  `access_description` text DEFAULT NULL,
  `is_verified` tinyint(1) NOT NULL DEFAULT 0,
  `verified_date` date DEFAULT NULL,
  `verified_by` bigint(20) UNSIGNED DEFAULT NULL,
  `verification_notes` text DEFAULT NULL,
  `status` enum('active','inactive','disputed') NOT NULL DEFAULT 'active',
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `land_addresses`
--

INSERT INTO `land_addresses` (`id`, `land_acquisition_id`, `country_id`, `state_id`, `city_text`, `city`, `specific_address`, `address_type`, `address_line_1`, `address_line_2`, `village`, `union_ward`, `upazila_thana`, `district`, `division`, `country`, `postal_code`, `latitude`, `longitude`, `coordinate_system`, `north_boundary`, `south_boundary`, `east_boundary`, `west_boundary`, `nearest_landmark`, `distance_from_road`, `road_type`, `access_description`, `is_verified`, `verified_date`, `verified_by`, `verification_notes`, `status`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(3, 10, 23, 1, NULL, 'Dhaka', '51,Arjotpara,Mohakhali Dhaka', 'location', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Bangladesh', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 'active', NULL, NULL, '2025-09-04 23:32:12', '2025-09-04 23:32:12'),
(4, 11, 23, 1, NULL, 'Dhaka', '51,Arjotpara,Mohakhali Dhaka', 'location', NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'Bangladesh', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, NULL, NULL, NULL, 'active', NULL, NULL, '2025-09-12 05:40:54', '2025-09-12 05:40:54');

-- --------------------------------------------------------

--
-- Table structure for table `land_documents`
--

CREATE TABLE `land_documents` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `land_acquisition_id` bigint(20) UNSIGNED NOT NULL,
  `document_type` varchar(255) NOT NULL,
  `document_title` varchar(255) NOT NULL,
  `document_description` text DEFAULT NULL,
  `document_number` varchar(255) DEFAULT NULL,
  `document_date` date DEFAULT NULL,
  `issue_date` date DEFAULT NULL,
  `expiry_date` date DEFAULT NULL,
  `document_file_name` varchar(255) NOT NULL,
  `document_file_path` varchar(255) NOT NULL,
  `document_file_type` varchar(255) DEFAULT NULL,
  `document_file_size` bigint(20) DEFAULT NULL,
  `document_file_hash` varchar(255) DEFAULT NULL,
  `issuing_authority` varchar(255) DEFAULT NULL,
  `issuing_office` varchar(255) DEFAULT NULL,
  `authorized_person` varchar(255) DEFAULT NULL,
  `verification_status` enum('pending','verified','rejected','expired') NOT NULL DEFAULT 'pending',
  `verified_date` date DEFAULT NULL,
  `verified_by` bigint(20) UNSIGNED DEFAULT NULL,
  `verification_notes` text DEFAULT NULL,
  `page_count` int(11) DEFAULT NULL,
  `document_summary` text DEFAULT NULL,
  `legal_notes` text DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `confidentiality` enum('public','restricted','confidential') NOT NULL DEFAULT 'restricted',
  `status` enum('active','archived','deleted') NOT NULL DEFAULT 'active',
  `uploaded_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `land_owners`
--

CREATE TABLE `land_owners` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `first_name` varchar(255) NOT NULL,
  `last_name` varchar(255) NOT NULL,
  `father_name` varchar(255) DEFAULT NULL,
  `mother_name` varchar(255) DEFAULT NULL,
  `date_of_birth` date DEFAULT NULL,
  `gender` enum('male','female','other') DEFAULT NULL,
  `marital_status` enum('single','married','divorced','widowed') DEFAULT NULL,
  `phone` varchar(255) NOT NULL,
  `email` varchar(255) DEFAULT NULL,
  `address` text DEFAULT NULL,
  `village` varchar(255) DEFAULT NULL,
  `post_office` varchar(255) DEFAULT NULL,
  `police_station` varchar(255) DEFAULT NULL,
  `district` varchar(255) DEFAULT NULL,
  `division` varchar(255) DEFAULT NULL,
  `country` varchar(255) NOT NULL DEFAULT 'Bangladesh',
  `postal_code` varchar(255) DEFAULT NULL,
  `nid_number` varchar(255) DEFAULT NULL,
  `passport_number` varchar(255) DEFAULT NULL,
  `birth_certificate_number` varchar(255) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `document_type` enum('nid','passport') DEFAULT NULL,
  `nid_front` varchar(255) DEFAULT NULL,
  `nid_back` varchar(255) DEFAULT NULL,
  `passport_photo` varchar(255) DEFAULT NULL,
  `occupation` varchar(255) DEFAULT NULL,
  `annual_income` decimal(15,2) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `status` enum('active','inactive','deceased') NOT NULL DEFAULT 'active',
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `land_owners`
--

INSERT INTO `land_owners` (`id`, `first_name`, `last_name`, `father_name`, `mother_name`, `date_of_birth`, `gender`, `marital_status`, `phone`, `email`, `address`, `village`, `post_office`, `police_station`, `district`, `division`, `country`, `postal_code`, `nid_number`, `passport_number`, `birth_certificate_number`, `photo`, `document_type`, `nid_front`, `nid_back`, `passport_photo`, `occupation`, `annual_income`, `notes`, `status`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(10, 'Fatima', 'Begum', 'Mohammad Ali', NULL, NULL, NULL, NULL, '+880181234567', '<EMAIL>', 'House 25, Road 12, Gulshan-1, Dhaka-1212', NULL, NULL, NULL, NULL, NULL, 'Bangladesh', NULL, '2345678901234', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', NULL, NULL, '2025-07-27 23:04:48', '2025-09-04 05:19:50'),
(11, 'Mohammad', 'Hassan', 'Abdul Majid', NULL, NULL, NULL, NULL, '+880191234567', '<EMAIL>', 'House 35, Road 18, Banani, Dhaka-1213', NULL, NULL, NULL, NULL, NULL, 'Bangladesh', NULL, '3456789012345', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', NULL, NULL, '2025-07-27 23:04:48', '2025-09-04 05:19:56'),
(12, 'Rashida', 'Khatun', 'Abdul Latif', NULL, NULL, NULL, NULL, '+880161234567', '<EMAIL>', 'House 45, Road 22, Uttara, Dhaka-1230', NULL, NULL, NULL, NULL, NULL, 'Bangladesh', NULL, '4567890123456', NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 'active', NULL, NULL, '2025-07-27 23:04:48', '2025-09-04 05:20:00'),
(18, 'Shifat E', 'Rasul', 'Late Shariful Alam', 'Rokshana Pervin', NULL, NULL, NULL, '01871769835342', '<EMAIL>', '51,Arjotpara,Mohakhali Dhaka', NULL, NULL, NULL, NULL, NULL, 'Bangladesh', NULL, '*********', NULL, NULL, NULL, 'passport', NULL, NULL, '/landowners/documents/document_1756982247_YYiNBPKqfU.jpg', NULL, NULL, NULL, 'active', NULL, NULL, '2025-09-04 04:37:27', '2025-09-04 04:37:27'),
(19, 'asdfswer', 'adfadfadf', 'adfadsfad', 'fadsfadsfadfadsfadf', NULL, NULL, NULL, 'adfadsf', '<EMAIL>', 'adfadfadf', NULL, NULL, NULL, NULL, NULL, 'Bangladesh', NULL, '*********', NULL, NULL, '/landowners/photo/landowner_1756982338_RIVakE7eSG.png', 'nid', '/landowners/documents/document_1756982369_oNhgVI7OF8.jpg', '/landowners/documents/document_1756982369_eJq1KdYCeG.png', '/landowners/documents/document_1756982338_d4nfNmUOL1.png', NULL, NULL, NULL, 'inactive', NULL, 1, '2025-09-04 04:38:58', '2025-09-04 22:34:34');

-- --------------------------------------------------------

--
-- Table structure for table `land_owner_audits`
--

CREATE TABLE `land_owner_audits` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `land_owner_id` bigint(20) UNSIGNED NOT NULL,
  `action` varchar(255) NOT NULL,
  `event_type` varchar(255) DEFAULT NULL,
  `old_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`old_values`)),
  `new_values` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`new_values`)),
  `changed_fields` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`changed_fields`)),
  `notes` text DEFAULT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `user_name` varchar(255) DEFAULT NULL,
  `user_email` varchar(255) DEFAULT NULL,
  `ip_address` varchar(255) DEFAULT NULL,
  `user_agent` varchar(255) DEFAULT NULL,
  `source` varchar(255) DEFAULT NULL,
  `description` text DEFAULT NULL,
  `metadata` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`metadata`)),
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `land_owner_audits`
--

INSERT INTO `land_owner_audits` (`id`, `land_owner_id`, `action`, `event_type`, `old_values`, `new_values`, `changed_fields`, `notes`, `user_id`, `user_name`, `user_email`, `ip_address`, `user_agent`, `source`, `description`, `metadata`, `created_at`, `updated_at`) VALUES
(11, 10, 'created', 'automatic', NULL, '\"{\\\"first_name\\\":\\\"Fatima\\\",\\\"last_name\\\":\\\"Begum\\\",\\\"father_name\\\":\\\"Mohammad Ali\\\",\\\"mother_name\\\":null,\\\"address\\\":\\\"House 25, Road 12, Gulshan-1, Dhaka-1212\\\",\\\"phone\\\":\\\"+880181234567\\\",\\\"nid_number\\\":\\\"2345678901234\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_at\\\":\\\"2025-07-28 05:04:48\\\",\\\"updated_at\\\":\\\"2025-07-28 05:04:48\\\"}\"', '\"[\\\"first_name\\\",\\\"last_name\\\",\\\"father_name\\\",\\\"mother_name\\\",\\\"address\\\",\\\"phone\\\",\\\"nid_number\\\",\\\"email\\\",\\\"photo\\\",\\\"document_type\\\",\\\"nid_front\\\",\\\"nid_back\\\",\\\"passport_photo\\\",\\\"status\\\",\\\"created_at\\\",\\\"updated_at\\\"]\"', NULL, NULL, NULL, NULL, '127.0.0.1', 'Symfony', 'api', 'Created new land owner: Fatima Begum', '\"{\\\"model_class\\\":\\\"App\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":10,\\\"timestamp\\\":\\\"2025-07-28 05:04:48\\\",\\\"changes_count\\\":16}\"', '2025-07-27 23:04:48', '2025-07-27 23:04:48'),
(12, 11, 'created', 'automatic', NULL, '\"{\\\"first_name\\\":\\\"Mohammad\\\",\\\"last_name\\\":\\\"Hassan\\\",\\\"father_name\\\":\\\"Abdul Majid\\\",\\\"mother_name\\\":null,\\\"address\\\":\\\"House 35, Road 18, Banani, Dhaka-1213\\\",\\\"phone\\\":\\\"+880191234567\\\",\\\"nid_number\\\":\\\"3456789012345\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_at\\\":\\\"2025-07-28 05:04:48\\\",\\\"updated_at\\\":\\\"2025-07-28 05:04:48\\\"}\"', '\"[\\\"first_name\\\",\\\"last_name\\\",\\\"father_name\\\",\\\"mother_name\\\",\\\"address\\\",\\\"phone\\\",\\\"nid_number\\\",\\\"email\\\",\\\"photo\\\",\\\"document_type\\\",\\\"nid_front\\\",\\\"nid_back\\\",\\\"passport_photo\\\",\\\"status\\\",\\\"created_at\\\",\\\"updated_at\\\"]\"', NULL, NULL, NULL, NULL, '127.0.0.1', 'Symfony', 'api', 'Created new land owner: Mohammad Hassan', '\"{\\\"model_class\\\":\\\"App\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":11,\\\"timestamp\\\":\\\"2025-07-28 05:04:48\\\",\\\"changes_count\\\":16}\"', '2025-07-27 23:04:48', '2025-07-27 23:04:48'),
(13, 12, 'created', 'automatic', NULL, '\"{\\\"first_name\\\":\\\"Rashida\\\",\\\"last_name\\\":\\\"Khatun\\\",\\\"father_name\\\":\\\"Abdul Latif\\\",\\\"mother_name\\\":null,\\\"address\\\":\\\"House 45, Road 22, Uttara, Dhaka-1230\\\",\\\"phone\\\":\\\"+880161234567\\\",\\\"nid_number\\\":\\\"4567890123456\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_at\\\":\\\"2025-07-28 05:04:48\\\",\\\"updated_at\\\":\\\"2025-07-28 05:04:48\\\"}\"', '\"[\\\"first_name\\\",\\\"last_name\\\",\\\"father_name\\\",\\\"mother_name\\\",\\\"address\\\",\\\"phone\\\",\\\"nid_number\\\",\\\"email\\\",\\\"photo\\\",\\\"document_type\\\",\\\"nid_front\\\",\\\"nid_back\\\",\\\"passport_photo\\\",\\\"status\\\",\\\"created_at\\\",\\\"updated_at\\\"]\"', NULL, NULL, NULL, NULL, '127.0.0.1', 'Symfony', 'api', 'Created new land owner: Rashida Khatun', '\"{\\\"model_class\\\":\\\"App\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":12,\\\"timestamp\\\":\\\"2025-07-28 05:04:48\\\",\\\"changes_count\\\":16}\"', '2025-07-27 23:04:48', '2025-07-27 23:04:48'),
(17, 10, 'updated', 'automatic', '\"{\\\"id\\\":10,\\\"first_name\\\":\\\"Fatima\\\",\\\"last_name\\\":\\\"Begum\\\",\\\"father_name\\\":\\\"Mohammad Ali\\\",\\\"mother_name\\\":null,\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"+880181234567\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"House 25, Road 12, Gulshan-1, Dhaka-1212\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"2345678901234\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_by\\\":null,\\\"updated_by\\\":null,\\\"created_at\\\":\\\"2025-07-28T05:04:48.000000Z\\\",\\\"updated_at\\\":\\\"2025-07-28T05:04:48.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"Fatima\\\",\\\"last_name\\\":\\\"Begum\\\",\\\"father_name\\\":\\\"Mohammad Ali\\\",\\\"mother_name\\\":null,\\\"address\\\":\\\"House 25, Road 12, Gulshan-1, Dhaka-1212\\\",\\\"phone\\\":\\\"+880181234567\\\",\\\"nid_number\\\":\\\"2345678901234\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"status\\\":\\\"inactive\\\",\\\"created_at\\\":\\\"2025-07-28 05:04:48\\\",\\\"updated_at\\\":\\\"2025-07-28 05:49:55\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, 1, 'Super Admin', '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: Fatima Begum', '\"{\\\"model_class\\\":\\\"App\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":10,\\\"timestamp\\\":\\\"2025-07-28 05:49:55\\\",\\\"changes_count\\\":2}\"', '2025-07-27 23:49:55', '2025-07-27 23:49:55'),
(18, 11, 'updated', 'automatic', '\"{\\\"id\\\":11,\\\"first_name\\\":\\\"Mohammad\\\",\\\"last_name\\\":\\\"Hassan\\\",\\\"father_name\\\":\\\"Abdul Majid\\\",\\\"mother_name\\\":null,\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"+880191234567\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"House 35, Road 18, Banani, Dhaka-1213\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"3456789012345\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_by\\\":null,\\\"updated_by\\\":null,\\\"created_at\\\":\\\"2025-07-28T05:04:48.000000Z\\\",\\\"updated_at\\\":\\\"2025-07-28T05:04:48.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"Mohammad\\\",\\\"last_name\\\":\\\"Hassan\\\",\\\"father_name\\\":\\\"Abdul Majid\\\",\\\"mother_name\\\":null,\\\"address\\\":\\\"House 35, Road 18, Banani, Dhaka-1213\\\",\\\"phone\\\":\\\"+880191234567\\\",\\\"nid_number\\\":\\\"3456789012345\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"status\\\":\\\"inactive\\\",\\\"created_at\\\":\\\"2025-07-28 05:04:48\\\",\\\"updated_at\\\":\\\"2025-07-28 05:50:01\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, 1, 'Super Admin', '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: Mohammad Hassan', '\"{\\\"model_class\\\":\\\"App\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":11,\\\"timestamp\\\":\\\"2025-07-28 05:50:01\\\",\\\"changes_count\\\":2}\"', '2025-07-27 23:50:01', '2025-07-27 23:50:01'),
(19, 12, 'updated', 'automatic', '\"{\\\"id\\\":12,\\\"first_name\\\":\\\"Rashida\\\",\\\"last_name\\\":\\\"Khatun\\\",\\\"father_name\\\":\\\"Abdul Latif\\\",\\\"mother_name\\\":null,\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"+880161234567\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"House 45, Road 22, Uttara, Dhaka-1230\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"4567890123456\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_by\\\":null,\\\"updated_by\\\":null,\\\"created_at\\\":\\\"2025-07-28T05:04:48.000000Z\\\",\\\"updated_at\\\":\\\"2025-07-28T05:04:48.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"Rashida\\\",\\\"last_name\\\":\\\"Khatun\\\",\\\"father_name\\\":\\\"Abdul Latif\\\",\\\"mother_name\\\":null,\\\"address\\\":\\\"House 45, Road 22, Uttara, Dhaka-1230\\\",\\\"phone\\\":\\\"+880161234567\\\",\\\"nid_number\\\":\\\"4567890123456\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"status\\\":\\\"inactive\\\",\\\"created_at\\\":\\\"2025-07-28 05:04:48\\\",\\\"updated_at\\\":\\\"2025-07-28 05:50:06\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, 1, 'Super Admin', '<EMAIL>', '::1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: Rashida Khatun', '\"{\\\"model_class\\\":\\\"App\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":12,\\\"timestamp\\\":\\\"2025-07-28 05:50:06\\\",\\\"changes_count\\\":2}\"', '2025-07-27 23:50:06', '2025-07-27 23:50:06'),
(48, 18, 'created', 'automatic', NULL, '\"{\\\"first_name\\\":\\\"Shifat E\\\",\\\"last_name\\\":\\\"Rasul\\\",\\\"father_name\\\":\\\"Late Shariful Alam\\\",\\\"mother_name\\\":\\\"Rokshana Pervin\\\",\\\"address\\\":\\\"51,Arjotpara,Mohakhali Dhaka\\\",\\\"phone\\\":\\\"01871769835342\\\",\\\"nid_number\\\":\\\"*********\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":null,\\\"document_type\\\":\\\"passport\\\",\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982247_YYiNBPKqfU.jpg\\\",\\\"status\\\":null,\\\"created_at\\\":\\\"2025-09-04 10:37:27\\\",\\\"updated_at\\\":\\\"2025-09-04 10:37:27\\\"}\"', '\"[\\\"first_name\\\",\\\"last_name\\\",\\\"father_name\\\",\\\"mother_name\\\",\\\"address\\\",\\\"phone\\\",\\\"nid_number\\\",\\\"email\\\",\\\"photo\\\",\\\"document_type\\\",\\\"nid_front\\\",\\\"nid_back\\\",\\\"passport_photo\\\",\\\"status\\\",\\\"created_at\\\",\\\"updated_at\\\"]\"', NULL, NULL, NULL, NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Created new land owner: Shifat E Rasul', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":18,\\\"timestamp\\\":\\\"2025-09-04 10:37:27\\\",\\\"changes_count\\\":16}\"', '2025-09-04 04:37:27', '2025-09-04 04:37:27'),
(51, 19, 'created', 'automatic', NULL, '\"{\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"phone\\\":\\\"adfadsf\\\",\\\"nid_number\\\":\\\"*********\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"passport\\\",\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"status\\\":null,\\\"created_at\\\":\\\"2025-09-04 10:38:58\\\",\\\"updated_at\\\":\\\"2025-09-04 10:38:58\\\"}\"', '\"[\\\"first_name\\\",\\\"last_name\\\",\\\"father_name\\\",\\\"mother_name\\\",\\\"address\\\",\\\"phone\\\",\\\"nid_number\\\",\\\"email\\\",\\\"photo\\\",\\\"document_type\\\",\\\"nid_front\\\",\\\"nid_back\\\",\\\"passport_photo\\\",\\\"status\\\",\\\"created_at\\\",\\\"updated_at\\\"]\"', NULL, NULL, NULL, NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Created new land owner: adfa adfadfadf', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":19,\\\"timestamp\\\":\\\"2025-09-04 10:38:58\\\",\\\"changes_count\\\":16}\"', '2025-09-04 04:38:58', '2025-09-04 04:38:58'),
(52, 19, 'updated', 'automatic', '\"{\\\"id\\\":19,\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"adfadsf\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"*********\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"passport\\\",\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_by\\\":null,\\\"updated_by\\\":null,\\\"created_at\\\":\\\"2025-09-04T10:38:58.000000Z\\\",\\\"updated_at\\\":\\\"2025-09-04T10:38:58.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"phone\\\":\\\"adfadsf\\\",\\\"nid_number\\\":\\\"*********\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"status\\\":\\\"active\\\",\\\"created_at\\\":\\\"2025-09-04 10:38:58\\\",\\\"updated_at\\\":\\\"2025-09-04 10:39:29\\\"}\"', '\"[\\\"document_type\\\",\\\"nid_front\\\",\\\"nid_back\\\",\\\"updated_at\\\"]\"', NULL, NULL, NULL, NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: adfa adfadfadf', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":19,\\\"timestamp\\\":\\\"2025-09-04 10:39:29\\\",\\\"changes_count\\\":4}\"', '2025-09-04 04:39:29', '2025-09-04 04:39:29'),
(53, 19, 'updated', 'automatic', '\"{\\\"id\\\":19,\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"adfadsf\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"*********\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_by\\\":null,\\\"updated_by\\\":null,\\\"created_at\\\":\\\"2025-09-04T10:38:58.000000Z\\\",\\\"updated_at\\\":\\\"2025-09-04T10:39:29.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"phone\\\":\\\"adfadsf\\\",\\\"nid_number\\\":\\\"*********\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"status\\\":\\\"inactive\\\",\\\"created_at\\\":\\\"2025-09-04 10:38:58\\\",\\\"updated_at\\\":\\\"2025-09-04 11:19:16\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, NULL, NULL, NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: adfa adfadfadf', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":19,\\\"timestamp\\\":\\\"2025-09-04 11:19:16\\\",\\\"changes_count\\\":2}\"', '2025-09-04 05:19:16', '2025-09-04 05:19:16'),
(54, 19, 'updated', 'automatic', '\"{\\\"id\\\":19,\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"adfadsf\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"*********\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"inactive\\\",\\\"created_by\\\":null,\\\"updated_by\\\":null,\\\"created_at\\\":\\\"2025-09-04T10:38:58.000000Z\\\",\\\"updated_at\\\":\\\"2025-09-04T11:19:16.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"phone\\\":\\\"adfadsf\\\",\\\"nid_number\\\":\\\"*********\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"status\\\":\\\"active\\\",\\\"created_at\\\":\\\"2025-09-04 10:38:58\\\",\\\"updated_at\\\":\\\"2025-09-04 11:19:46\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, NULL, NULL, NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: adfa adfadfadf', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":19,\\\"timestamp\\\":\\\"2025-09-04 11:19:46\\\",\\\"changes_count\\\":2}\"', '2025-09-04 05:19:46', '2025-09-04 05:19:46'),
(55, 10, 'updated', 'automatic', '\"{\\\"id\\\":10,\\\"first_name\\\":\\\"Fatima\\\",\\\"last_name\\\":\\\"Begum\\\",\\\"father_name\\\":\\\"Mohammad Ali\\\",\\\"mother_name\\\":null,\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"+880181234567\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"House 25, Road 12, Gulshan-1, Dhaka-1212\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"2345678901234\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"inactive\\\",\\\"created_by\\\":null,\\\"updated_by\\\":1,\\\"created_at\\\":\\\"2025-07-28T05:04:48.000000Z\\\",\\\"updated_at\\\":\\\"2025-07-28T05:49:55.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"Fatima\\\",\\\"last_name\\\":\\\"Begum\\\",\\\"father_name\\\":\\\"Mohammad Ali\\\",\\\"mother_name\\\":null,\\\"address\\\":\\\"House 25, Road 12, Gulshan-1, Dhaka-1212\\\",\\\"phone\\\":\\\"+880181234567\\\",\\\"nid_number\\\":\\\"2345678901234\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_at\\\":\\\"2025-07-28 05:04:48\\\",\\\"updated_at\\\":\\\"2025-09-04 11:19:50\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, NULL, NULL, NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: Fatima Begum', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":10,\\\"timestamp\\\":\\\"2025-09-04 11:19:50\\\",\\\"changes_count\\\":2}\"', '2025-09-04 05:19:50', '2025-09-04 05:19:50'),
(56, 11, 'updated', 'automatic', '\"{\\\"id\\\":11,\\\"first_name\\\":\\\"Mohammad\\\",\\\"last_name\\\":\\\"Hassan\\\",\\\"father_name\\\":\\\"Abdul Majid\\\",\\\"mother_name\\\":null,\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"+880191234567\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"House 35, Road 18, Banani, Dhaka-1213\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"3456789012345\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"inactive\\\",\\\"created_by\\\":null,\\\"updated_by\\\":1,\\\"created_at\\\":\\\"2025-07-28T05:04:48.000000Z\\\",\\\"updated_at\\\":\\\"2025-07-28T05:50:01.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"Mohammad\\\",\\\"last_name\\\":\\\"Hassan\\\",\\\"father_name\\\":\\\"Abdul Majid\\\",\\\"mother_name\\\":null,\\\"address\\\":\\\"House 35, Road 18, Banani, Dhaka-1213\\\",\\\"phone\\\":\\\"+880191234567\\\",\\\"nid_number\\\":\\\"3456789012345\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_at\\\":\\\"2025-07-28 05:04:48\\\",\\\"updated_at\\\":\\\"2025-09-04 11:19:56\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, NULL, NULL, NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: Mohammad Hassan', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":11,\\\"timestamp\\\":\\\"2025-09-04 11:19:56\\\",\\\"changes_count\\\":2}\"', '2025-09-04 05:19:56', '2025-09-04 05:19:56'),
(57, 12, 'updated', 'automatic', '\"{\\\"id\\\":12,\\\"first_name\\\":\\\"Rashida\\\",\\\"last_name\\\":\\\"Khatun\\\",\\\"father_name\\\":\\\"Abdul Latif\\\",\\\"mother_name\\\":null,\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"+880161234567\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"House 45, Road 22, Uttara, Dhaka-1230\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"4567890123456\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"inactive\\\",\\\"created_by\\\":null,\\\"updated_by\\\":1,\\\"created_at\\\":\\\"2025-07-28T05:04:48.000000Z\\\",\\\"updated_at\\\":\\\"2025-07-28T05:50:06.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"Rashida\\\",\\\"last_name\\\":\\\"Khatun\\\",\\\"father_name\\\":\\\"Abdul Latif\\\",\\\"mother_name\\\":null,\\\"address\\\":\\\"House 45, Road 22, Uttara, Dhaka-1230\\\",\\\"phone\\\":\\\"+880161234567\\\",\\\"nid_number\\\":\\\"4567890123456\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":null,\\\"document_type\\\":null,\\\"nid_front\\\":null,\\\"nid_back\\\":null,\\\"passport_photo\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_at\\\":\\\"2025-07-28 05:04:48\\\",\\\"updated_at\\\":\\\"2025-09-04 11:20:00\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, NULL, NULL, NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: Rashida Khatun', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":12,\\\"timestamp\\\":\\\"2025-09-04 11:20:00\\\",\\\"changes_count\\\":2}\"', '2025-09-04 05:20:00', '2025-09-04 05:20:00'),
(60, 19, 'updated', 'automatic', '\"{\\\"id\\\":19,\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"adfadsf\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"*********\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_by\\\":null,\\\"updated_by\\\":null,\\\"created_at\\\":\\\"2025-09-04T10:38:58.000000Z\\\",\\\"updated_at\\\":\\\"2025-09-04T11:19:46.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"phone\\\":\\\"adfadsf\\\",\\\"nid_number\\\":\\\"*********\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"status\\\":\\\"inactive\\\",\\\"created_at\\\":\\\"2025-09-04 10:38:58\\\",\\\"updated_at\\\":\\\"2025-09-04 11:25:57\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, 1, 'Super Admin', '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: adfa adfadfadf', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":19,\\\"timestamp\\\":\\\"2025-09-04 11:25:57\\\",\\\"changes_count\\\":2}\"', '2025-09-04 05:25:57', '2025-09-04 05:25:57'),
(61, 19, 'updated', 'automatic', '\"{\\\"id\\\":19,\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"adfadsf\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"*********\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"inactive\\\",\\\"created_by\\\":null,\\\"updated_by\\\":1,\\\"created_at\\\":\\\"2025-09-04T10:38:58.000000Z\\\",\\\"updated_at\\\":\\\"2025-09-04T11:25:57.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"phone\\\":\\\"adfadsf\\\",\\\"nid_number\\\":\\\"*********\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"status\\\":\\\"active\\\",\\\"created_at\\\":\\\"2025-09-04 10:38:58\\\",\\\"updated_at\\\":\\\"2025-09-04 11:26:02\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, 1, 'Super Admin', '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: adfa adfadfadf', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":19,\\\"timestamp\\\":\\\"2025-09-04 11:26:02\\\",\\\"changes_count\\\":2}\"', '2025-09-04 05:26:02', '2025-09-04 05:26:02'),
(62, 19, 'updated', 'automatic', '\"{\\\"id\\\":19,\\\"first_name\\\":\\\"adfa\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"adfadsf\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"*********\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_by\\\":null,\\\"updated_by\\\":1,\\\"created_at\\\":\\\"2025-09-04T10:38:58.000000Z\\\",\\\"updated_at\\\":\\\"2025-09-04T11:26:02.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"asdfswer\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"phone\\\":\\\"adfadsf\\\",\\\"nid_number\\\":\\\"*********\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"status\\\":\\\"active\\\",\\\"created_at\\\":\\\"2025-09-04 10:38:58\\\",\\\"updated_at\\\":\\\"2025-09-04 11:26:14\\\"}\"', '\"[\\\"first_name\\\",\\\"updated_at\\\"]\"', NULL, 1, 'Super Admin', '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: asdfswer adfadfadf', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":19,\\\"timestamp\\\":\\\"2025-09-04 11:26:14\\\",\\\"changes_count\\\":2}\"', '2025-09-04 05:26:14', '2025-09-04 05:26:14'),
(64, 19, 'updated', 'automatic', '\"{\\\"id\\\":19,\\\"first_name\\\":\\\"asdfswer\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"adfadsf\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"*********\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_by\\\":null,\\\"updated_by\\\":1,\\\"created_at\\\":\\\"2025-09-04T10:38:58.000000Z\\\",\\\"updated_at\\\":\\\"2025-09-04T11:26:14.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"asdfswer\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"phone\\\":\\\"adfadsf\\\",\\\"nid_number\\\":\\\"*********\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"status\\\":\\\"inactive\\\",\\\"created_at\\\":\\\"2025-09-04 10:38:58\\\",\\\"updated_at\\\":\\\"2025-09-04 12:11:23\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, 1, 'Super Admin', '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: asdfswer adfadfadf', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":19,\\\"timestamp\\\":\\\"2025-09-04 12:11:23\\\",\\\"changes_count\\\":2}\"', '2025-09-04 06:11:23', '2025-09-04 06:11:23'),
(65, 19, 'updated', 'automatic', '\"{\\\"id\\\":19,\\\"first_name\\\":\\\"asdfswer\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"adfadsf\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"*********\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"inactive\\\",\\\"created_by\\\":null,\\\"updated_by\\\":1,\\\"created_at\\\":\\\"2025-09-04T10:38:58.000000Z\\\",\\\"updated_at\\\":\\\"2025-09-04T12:11:23.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"asdfswer\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"phone\\\":\\\"adfadsf\\\",\\\"nid_number\\\":\\\"*********\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"status\\\":\\\"active\\\",\\\"created_at\\\":\\\"2025-09-04 10:38:58\\\",\\\"updated_at\\\":\\\"2025-09-04 12:11:28\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, 1, 'Super Admin', '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: asdfswer adfadfadf', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":19,\\\"timestamp\\\":\\\"2025-09-04 12:11:28\\\",\\\"changes_count\\\":2}\"', '2025-09-04 06:11:28', '2025-09-04 06:11:28'),
(66, 19, 'updated', 'automatic', '\"{\\\"id\\\":19,\\\"first_name\\\":\\\"asdfswer\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"date_of_birth\\\":null,\\\"gender\\\":null,\\\"marital_status\\\":null,\\\"phone\\\":\\\"adfadsf\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"village\\\":null,\\\"post_office\\\":null,\\\"police_station\\\":null,\\\"district\\\":null,\\\"division\\\":null,\\\"country\\\":\\\"Bangladesh\\\",\\\"postal_code\\\":null,\\\"nid_number\\\":\\\"*********\\\",\\\"passport_number\\\":null,\\\"birth_certificate_number\\\":null,\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"occupation\\\":null,\\\"annual_income\\\":null,\\\"notes\\\":null,\\\"status\\\":\\\"active\\\",\\\"created_by\\\":null,\\\"updated_by\\\":1,\\\"created_at\\\":\\\"2025-09-04T10:38:58.000000Z\\\",\\\"updated_at\\\":\\\"2025-09-04T12:11:28.000000Z\\\"}\"', '\"{\\\"first_name\\\":\\\"asdfswer\\\",\\\"last_name\\\":\\\"adfadfadf\\\",\\\"father_name\\\":\\\"adfadsfad\\\",\\\"mother_name\\\":\\\"fadsfadsfadfadsfadf\\\",\\\"address\\\":\\\"adfadfadf\\\",\\\"phone\\\":\\\"adfadsf\\\",\\\"nid_number\\\":\\\"*********\\\",\\\"email\\\":\\\"<EMAIL>\\\",\\\"photo\\\":\\\"\\\\\\/landowners\\\\\\/photo\\\\\\/landowner_1756982338_RIVakE7eSG.png\\\",\\\"document_type\\\":\\\"nid\\\",\\\"nid_front\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_oNhgVI7OF8.jpg\\\",\\\"nid_back\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982369_eJq1KdYCeG.png\\\",\\\"passport_photo\\\":\\\"\\\\\\/landowners\\\\\\/documents\\\\\\/document_1756982338_d4nfNmUOL1.png\\\",\\\"status\\\":\\\"inactive\\\",\\\"created_at\\\":\\\"2025-09-04 10:38:58\\\",\\\"updated_at\\\":\\\"2025-09-05 04:34:34\\\"}\"', '\"[\\\"status\\\",\\\"updated_at\\\"]\"', NULL, 1, 'Super Admin', '<EMAIL>', '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'api', 'Updated land owner: asdfswer adfadfadf', '\"{\\\"model_class\\\":\\\"Modules\\\\\\\\LandOwners\\\\\\\\Models\\\\\\\\LandOwner\\\",\\\"model_id\\\":19,\\\"timestamp\\\":\\\"2025-09-05 04:34:34\\\",\\\"changes_count\\\":2}\"', '2025-09-04 22:34:34', '2025-09-04 22:34:34');

-- --------------------------------------------------------

--
-- Table structure for table `languages`
--

CREATE TABLE `languages` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `code` varchar(10) NOT NULL,
  `native_name` varchar(255) DEFAULT NULL,
  `flag` varchar(255) DEFAULT NULL,
  `direction` enum('ltr','rtl') NOT NULL DEFAULT 'ltr',
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `is_default` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `languages`
--

INSERT INTO `languages` (`id`, `name`, `code`, `native_name`, `flag`, `direction`, `status`, `is_default`, `created_at`, `updated_at`) VALUES
(1, 'English', 'en', 'English', '🇺🇸', 'ltr', 'active', 1, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(2, 'Spanish', 'es', 'Español', '🇪🇸', 'ltr', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(3, 'French', 'fr', 'Français', '🇫🇷', 'ltr', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(4, 'German', 'de', 'Deutsch', '🇩🇪', 'ltr', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(5, 'Arabic', 'ar', 'العربية', '🇸🇦', 'rtl', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(6, 'Italian', 'it', 'Italiano', '🇮🇹', 'ltr', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(7, 'Portuguese', 'pt', 'Português', '🇵🇹', 'ltr', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(8, 'Portuguese (Brazil)', 'pt-BR', 'Português (Brasil)', '🇧🇷', 'ltr', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(9, 'Japanese', 'ja', '日本語', '🇯🇵', 'ltr', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(10, 'Chinese (Simplified)', 'zh-CN', '简体中文', '🇨🇳', 'ltr', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(11, 'Russian', 'ru', 'Русский', '🇷🇺', 'ltr', 'inactive', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(12, 'Korean', 'ko', '한국어', '🇰🇷', 'ltr', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(13, 'Hindi', 'hi', 'हिन्दी', '🇮🇳', 'ltr', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(14, 'Dutch', 'nl', 'Nederlands', '🇳🇱', 'ltr', 'active', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(15, 'Turkish', 'tr', 'Türkçe', '🇹🇷', 'ltr', 'inactive', 0, '2025-07-18 04:46:25', '2025-07-18 04:46:25');

-- --------------------------------------------------------

--
-- Table structure for table `lease_types`
--

CREATE TABLE `lease_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` enum('1','0') DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `lease_types`
--

INSERT INTO `lease_types` (`id`, `name`, `description`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(4, 'Short-Term Lease', 'Lease agreement typically for 6 months or less, ideal for flexibility.', '1', 1, '2025-08-14 23:56:11', '2025-08-14 23:56:11'),
(5, 'Long-Term Lease', 'Lease agreement for 12 months or more, ideal for stability.', '1', 2, '2025-08-14 23:56:11', '2025-08-14 23:56:11'),
(6, 'Month-to-Month Lease', 'Renews every month until terminated by either party.', '1', 3, '2025-08-14 23:56:11', '2025-08-15 06:05:48'),
(18, 'fdgsd', 'sfgsd', '1', 0, '2025-09-03 00:06:57', '2025-09-03 00:06:57'),
(19, 'hjkh', 'hjkh', '1', 0, '2025-09-03 00:51:48', '2025-09-03 00:51:48');

-- --------------------------------------------------------

--
-- Table structure for table `locations`
--

CREATE TABLE `locations` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `state_id` bigint(20) UNSIGNED NOT NULL,
  `country_id` bigint(20) UNSIGNED NOT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `locations`
--

INSERT INTO `locations` (`id`, `name`, `state_id`, `country_id`, `status`, `created_at`, `updated_at`) VALUES
(2, 'Dhanmondi', 1, 23, 'active', '2025-08-06 00:29:50', '2025-08-06 00:29:50'),
(4, 'Agrabad', 17, 23, 'active', '2025-08-06 00:29:50', '2025-08-06 01:06:53'),
(16, 'Gulshan', 1, 23, 'active', '2025-08-06 00:56:39', '2025-08-06 00:56:39'),
(18, 'Uttara', 1, 23, 'active', '2025-08-06 00:56:39', '2025-08-06 00:56:39'),
(20, 'Los Angeles', 2, 235, 'active', '2025-08-06 00:56:39', '2025-08-06 00:56:39'),
(21, 'San Francisco', 2, 235, 'active', '2025-08-06 00:56:39', '2025-08-06 00:56:39'),
(22, 'Houston', 3, 235, 'active', '2025-08-06 00:56:39', '2025-08-06 00:56:39'),
(23, 'Downtown', 9, 9, 'active', '2025-08-06 00:56:39', '2025-08-06 01:40:49'),
(24, 'Suburbs', 12, 11, 'active', '2025-08-06 00:56:39', '2025-08-06 01:41:33'),
(25, 'Mohakhali', 1, 23, 'active', '2025-08-06 01:09:06', '2025-08-06 01:09:06');

-- --------------------------------------------------------

--
-- Table structure for table `menus`
--

CREATE TABLE `menus` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `is_active` enum('1','0') NOT NULL DEFAULT '1',
  `position` int(11) DEFAULT NULL,
  `parentId` int(11) DEFAULT NULL,
  `childPosition` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `menus`
--

INSERT INTO `menus` (`id`, `name`, `slug`, `is_active`, `position`, `parentId`, `childPosition`, `created_at`, `updated_at`) VALUES
(1, 'Home', 'Home', '1', 1, NULL, NULL, '2025-09-05 06:16:44', '2025-10-06 02:28:15'),
(2, 'About', 'About', '1', 2, NULL, NULL, '2025-09-05 06:16:44', '2025-10-06 02:28:15'),
(3, 'Services', 'Services', '1', 3, NULL, NULL, '2025-09-05 06:16:44', '2025-10-06 02:28:15'),
(4, 'Contact', 'Contact', '1', 4, NULL, NULL, '2025-09-05 06:16:44', '2025-10-06 02:28:15');

-- --------------------------------------------------------

--
-- Table structure for table `menu_manages`
--

CREATE TABLE `menu_manages` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `menu_position_id` int(11) NOT NULL,
  `title` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `type` varchar(255) NOT NULL,
  `target` varchar(255) NOT NULL,
  `parent_id` int(11) DEFAULT NULL,
  `order` int(11) NOT NULL,
  `new_tap` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `menu_manages`
--

INSERT INTO `menu_manages` (`id`, `menu_position_id`, `title`, `slug`, `type`, `target`, `parent_id`, `order`, `new_tap`, `created_at`, `updated_at`) VALUES
(15, 2, 'Home', 'home', 'page', '_self', 16, 2, 0, '2025-10-06 02:28:40', '2025-10-06 03:42:30'),
(16, 2, 'About', 'about', 'page', '_self', NULL, 1, 0, '2025-10-06 02:28:40', '2025-10-06 03:41:22'),
(17, 2, 'Services', 'services', 'page', '_self', NULL, 3, 0, '2025-10-06 02:28:40', '2025-10-06 02:34:03'),
(18, 2, 'Contact', 'contact', 'page', '_self', NULL, 4, 0, '2025-10-06 02:28:41', '2025-10-06 02:33:57');

-- --------------------------------------------------------

--
-- Table structure for table `menu_positions`
--

CREATE TABLE `menu_positions` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `menu_positions`
--

INSERT INTO `menu_positions` (`id`, `name`, `slug`, `created_at`, `updated_at`) VALUES
(1, 'Top Header', 'Top_header', NULL, NULL),
(2, 'Header', 'Header', NULL, NULL),
(3, 'Footer', 'Footer', NULL, NULL);

-- --------------------------------------------------------

--
-- Table structure for table `migrations`
--

CREATE TABLE `migrations` (
  `id` int(10) UNSIGNED NOT NULL,
  `migration` varchar(255) NOT NULL,
  `batch` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `migrations`
--

INSERT INTO `migrations` (`id`, `migration`, `batch`) VALUES
(1, '0001_01_01_000000_create_users_table', 1),
(2, '0001_01_01_000001_create_cache_table', 1),
(3, '0001_01_01_000002_create_jobs_table', 1),
(4, '2024_12_24_000000_create_land_owners_table', 1),
(5, '2024_12_25_000000_create_land_acquisitions_table', 1),
(6, '2025_01_18_000001_create_addons_table', 1),
(7, '2025_06_26_091133_create_commission_agents_table', 2),
(8, '2025_06_27_083955_create_roles_table', 2),
(9, '2025_06_30_044928_add_role_id_to_users_table', 2),
(10, '2025_06_30_050321_create_personal_access_tokens_table', 2),
(11, '2025_07_02_000001_add_tracking_fields_to_land_owners_table', 2),
(12, '2025_07_02_000002_create_land_owner_audits_table', 2),
(13, '2025_07_03_000000_add_document_fields_to_land_owners_table', 2),
(14, '2025_07_03_062518_create_land_documents_table', 2),
(15, '2025_07_07_000000_update_land_owners_table_split_name', 2),
(16, '2025_07_07_094610_drop_name_column_from_land_owners_table', 2),
(17, '2025_07_07_110905_create_land_addresses_table', 2),
(18, '2025_07_08_123200_remove_ownership_and_cash_fields_from_land_owners_table', 2),
(19, '2025_07_09_080013_create_countries_table', 2),
(20, '2025_07_09_092036_create_languages_table', 2),
(21, '2025_07_09_095557_update_users_table_add_profile_fields', 2),
(22, '2025_07_09_095723_migrate_user_names_to_first_last_name', 2),
(23, '2025_07_09_100720_create_currencies_table', 2),
(24, '2025_07_09_103255_add_is_default_to_languages_table', 2),
(25, '2025_07_09_123847_add_status_to_land_owners_table', 2),
(26, '2025_07_10_051024_add_missing_columns_to_land_owner_audits_table', 2),
(27, '2025_07_10_060829_create_states_table', 2),
(28, '2025_07_10_090526_create_cities_table', 2),
(29, '2025_07_22_123401_create_land_owners_table', 3),
(30, '2025_07_22_123407_create_land_owner_audits_table', 3),
(31, '2025_07_22_123413_create_land_acquisitions_table', 3),
(32, '2025_07_22_123419_create_land_documents_table', 3),
(33, '2025_07_22_123424_create_land_addresses_table', 3),
(34, '2025_07_24_102632_add_document_type_to_land_owners_table', 4),
(35, '2025_07_24_103217_fix_land_owner_audits_missing_columns', 5),
(36, '2025_07_11_044751_update_land_addresses_table_structure', 6),
(37, '2025_07_11_043608_add_city_varchar_to_land_addresses_table', 7),
(38, '2025_07_11_121859_create_projects_table', 7),
(39, '2025_07_14_093231_add_unit_types_to_projects_table', 7),
(40, '2025_07_14_093813_change_unit_types_to_integer_in_projects_table', 7),
(41, '2025_07_14_094733_add_address_fields_to_projects_table', 7),
(42, '2025_07_14_104300_create_unit_details_table', 7),
(43, '2025_07_14_113150_create_unit_types_table', 7),
(44, '2025_07_14_115652_remove_bedrooms_bathrooms_from_unit_types_table', 7),
(45, '2025_07_15_050718_add_new_fields_to_unit_types_table', 7),
(46, '2025_07_15_051606_simplify_unit_details_table', 7),
(47, '2025_07_15_052948_add_floor_number_to_unit_details_table', 7),
(48, '2025_07_15_074415_create_employees_table', 7),
(49, '2025_07_15_084459_create_contractors_table', 7),
(50, '2025_07_15_092241_add_files_to_contractors_table', 7),
(51, '2025_07_15_094536_create_vendor_types_table', 7),
(52, '2025_07_15_100226_create_vendors_table', 7),
(53, '2025_07_24_123011_add_missing_land_size_column_to_land_acquisitions_table', 8),
(54, '2025_01_29_000001_create_project_contractors_table', 9),
(55, '2025_07_28_000000_create_development_costings_table', 9),
(56, '2025_07_29_070217_create_project_vendors_table', 10),
(59, '2024_01_15_000006_create_project_employees_table', 11),
(60, '2025_07_29_111558_remove_budget_total_from_projects_table', 12),
(61, '2025_07_29_121539_create_property_amenities_table', 13),
(62, '2025_07_30_071330_create_property_amenities_table', 14),
(63, '2025_08_01_045017_create_property_amenities_table', 15),
(64, '2025_08_01_080236_create_project_amenities_table', 16),
(65, '2025_08_01_125559_create_project_amenities_table', 17),
(66, '2025_08_04_060221_create_customers_table', 18),
(70, '2025_08_04_065256_add_address_to_customers_table', 19),
(71, '2025_08_05_044340_create_project_images_table', 19),
(72, '2025_08_05_061613_create_projects_table', 20),
(73, '2025_08_05_061338_create_project_images_table', 21),
(74, '2025_08_05_061358_create_project_videos_table', 21),
(75, '2025_08_05_061619_create_project_units_table', 21),
(76, '2025_08_05_114610_update_projects_table_country_to_foreign_key', 22),
(77, '2025_08_05_120021_update_projects_table_state_to_foreign_key', 23),
(78, '2025_08_07_062344_create_property_types_table', 24),
(80, '2025_08_08_000001_update_projects_property_type_to_foreign_key', 25),
(81, '2025_08_08_000002_create_property_statuses_table', 26),
(82, '2025_08_08_000003_add_property_status_id_to_projects_table', 27),
(83, '2025_08_08_000001_add_rent_type_to_project_units_table', 28),
(84, '2025_08_11_043946_create_property_stages_table', 29),
(85, '2025_08_12_044136_add_additional_fields_to_property_stages_table', 30),
(87, '2025_08_12_083000_create_unit_types_table', 31),
(88, '2025_08_13_062037_create_property_stages_table', 32),
(89, '2025_08_13_083538_add_sort_order_to_property_stages_table', 33),
(90, '2025_08_13_102256_create_tanants_table', 34),
(91, '2025_08_14_000000_create_project_documents_table', 35),
(92, '2025_08_14_055216_create_property_sevices_table', 36),
(93, '2025_08_14_073013_rename_property_sevices_to_property_services_table', 37),
(94, '2025_08_14_103418_create_rent_types_table', 38),
(95, '2025_08_14_120206_add_sort_order_to_rent_types_table', 39),
(96, '2025_08_15_050922_create_lease_types_table', 40),
(97, '2025_08_15_090332_create_tenants_documents_table', 41),
(98, '2025_08_15_090522_create_tenant_emergency_contacts_table', 42),
(100, '2025_08_15_101009_add_missing_fields_to_tanants_table', 44),
(101, '2025_08_15_093405_create_invoices_table', 45),
(102, '2025_08_15_130001_drop_old_tanants_table', 46),
(103, '2025_08_15_130000_create_tanants_table_clean', 47),
(104, '2025_08_20_054725_create_payment_types_table', 48),
(105, '2025_08_20_063755_create_payment_statuses_table', 49),
(106, '2025_08_20_083329_create_payment_methods_table', 50),
(107, '2025_08_22_055102_create_property_items_table', 51),
(108, '2025_08_22_055101_create_invoice_items_table', 52),
(109, '2025_09_05_113319_create_menus_table', 53),
(110, '2025_09_08_100309_create_widgets_table', 54),
(111, '2025_09_12_085224_create_page_contents_table', 55),
(113, '2025_09_22_103128_create_frontend_settings_table', 56),
(126, '2025_09_24_053156_create_frontend_settings_table', 57),
(134, '2025_09_25_063156_create_backend_settings_table', 58),
(135, '2025_10_03_045640_create_blogs_table', 59),
(136, '2025_10_03_000000_create_blogs_table', 60),
(137, '2025_10_06_051441_create_menu_positions_table', 61),
(138, '2025_10_06_060741_create_menu_manages_table', 62);

-- --------------------------------------------------------

--
-- Table structure for table `page_contents`
--

CREATE TABLE `page_contents` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `page_id` int(11) NOT NULL,
  `widget_id` int(11) NOT NULL,
  `pageContent` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`pageContent`)),
  `order_index` int(11) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `page_contents`
--

INSERT INTO `page_contents` (`id`, `page_id`, `widget_id`, `pageContent`, `order_index`, `created_at`, `updated_at`) VALUES
(435, 1, 1, '{\"title\":\"Slider\",\"subtitle\":null,\"sliders\":[{\"title\":\"Neckle Valley Real Estate\",\"subtitle\":\"To help neckle company stand out and make an impression on potential.\",\"property\":null,\"buttonText\":\"Find Property\",\"buttonUrl\":\"#!\",\"image\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1758024727_68c954172e38c.png\",\"location\":\"Dhaka\",\"ratingLogo\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1759219050_68db8d6aa4741.png\",\"rating\":\"3.5\",\"description\":\"To help neckle company stand out and make an impression on potential.\"},{\"title\":\"Neckle Valley Real Estate\",\"subtitle\":\"To help neckle company stand out and make an impression on potential.\",\"description\":\"To help neckle company stand out and make an impression on potential.\",\"buttonText\":\"Find Property\",\"buttonUrl\":\"#!\",\"image\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1759219214_68db8e0e129a7.png\",\"location\":\"adf\",\"ratingLogo\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1759219224_68db8e181bcb6.png\",\"rating\":null}]}', 0, '2025-09-30 05:53:27', '2025-09-30 05:53:27'),
(436, 1, 2, '{\"title\":\"Search\",\"subtitle\":null,\"searchEnabled\":true,\"locationsearchEnabled\":true,\"bedRoomsearchEnabled\":true,\"pricesearchEnabled\":true}', 1, '2025-09-30 05:53:27', '2025-09-30 05:53:27'),
(437, 1, 4, '{\"title\":\"Get To Know About Neckle\",\"subtitle\":null,\"serviceFrom\":\"1994\",\"projectCompleted\":\"600\",\"projectSold\":\"200\",\"yearOfExperience\":\"4\",\"aboutText\":\"<h2><strong>Welcome to our Neckle!<\\/strong><\\/h2><p>We\'re thrilled to have you join our community of automotive enthusiasts and professionals. Whether you\'re a passionate driver, a car owner, or someone who loves all things automotive, you\'ve come to the right place.At neckle, we strive to create a space where people can connect, share knowledge, and explore the exciting world of automobiles<\\/p>\",\"image\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1758024886_68c954b6c7885.png\",\"imagetwo\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1758024897_68c954c19a7b2.png\",\"ceoSignature\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1758024901_68c954c5623aa.png\"}', 2, '2025-09-30 05:53:27', '2025-09-30 05:53:27'),
(438, 1, 6, '{\"title\":\"Property By City\",\"subtitle\":null,\"onEachView\":\"6\"}', 3, '2025-09-30 05:53:27', '2025-09-30 05:53:27'),
(439, 1, 7, '{\"title\":\"Why Choose Us\",\"subtitle\":null,\"shoulder\":\"Why Only Choose Neckle\",\"underTitle\":\"Here are some of the featured Apartment in different categories\",\"image\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1759231358_68dbbd7e96852.png\",\"imagetwo\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1758084313_68ca3cd9b1879.png\",\"iconImage\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1759231321_68dbbd59d0a3a.png\",\"reviewText\":\"Excellent!  5.0 Rating out of 5.0 based on 245 reviews\",\"items\":[{\"title\":\"Affordable Price\",\"subTitle\":\"An affordable price for a luxury car may be significantly higher than an affordable price for a budget car.\"},{\"title\":\"Money Back Guarantee\",\"subTitle\":\"An affordable price for a luxury car may be significantly higher than an affordable price for a budget car...\"},{\"title\":\"Outstanding Property\",\"subTitle\":\"An affordable price for a luxury car may be significantly higher than an affordable price for a budget car...\"}]}', 4, '2025-09-30 05:53:27', '2025-09-30 05:53:27'),
(440, 1, 8, '{\"title\":\"Featured Property\",\"subtitle\":null,\"subTitle\":\"Here are some of the featured Apartment in different categories\",\"onEachView\":\"6\"}', 5, '2025-09-30 05:53:27', '2025-09-30 05:53:27'),
(441, 1, 9, '{\"title\":\"How it Works\",\"subtitle\":null,\"shoulder\":\"How Does It Works\",\"underText\":\"Here are some of the featured Apartment in different categorie\",\"buttonName\":\"Watch Video\",\"buttonUrl\":\"#!\",\"items\":[{\"title\":\"Choose Any where\",\"text\":\"The real estate industry It plays a significant role in the as it contributes.\",\"image\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1759231583_68dbbe5f8bd83.png\",\"imagePreview\":\"blob:http:\\/\\/localhost:8000\\/092a6565-ba4f-4f5d-990f-92bdd6d351ad\"},{\"title\":\"Contact With Us\",\"text\":\"The real estate industry It plays a significant role in the as it contributes.\",\"image\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1758026386_68c95a929fb61.png\",\"imagePreview\":\"blob:http:\\/\\/localhost:8000\\/741f5245-fb56-4978-93b9-c667c1144efe\"},{\"title\":\"Pay For The Home\",\"text\":\"The real estate industry It plays a significant role in the as it contributes.\",\"image\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1759231609_68dbbe793591a.png\",\"imagePreview\":\"blob:http:\\/\\/localhost:8000\\/9a20189a-913f-41dc-a60b-a2061191c75b\"},{\"title\":\"Recieve The Home\",\"text\":\"The real estate industry It plays a significant role in the as it contributes.\",\"image\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1758026444_68c95accf3ba1.png\",\"imagePreview\":\"blob:http:\\/\\/localhost:8000\\/1a2bbb4e-7556-4dd6-8116-85181cd6d2ad\"}]}', 6, '2025-09-30 05:53:27', '2025-09-30 05:53:27'),
(442, 1, 10, '{\"title\":\"Recent Property\",\"subtitle\":null,\"subTitle\":\"Here are some of the featured Apartment in different categories\",\"onEachView\":\"8\"}', 7, '2025-09-30 05:53:27', '2025-09-30 05:53:27'),
(443, 1, 11, '{\"title\":\"Not Sure, Which Apartment is Best?\",\"subtitle\":null,\"details\":\"<p>A car that is dependable and has a low risk of breakdowns is highly desirable.<\\/p>\",\"subTitle\":\"Choose Apartment\",\"image\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1759232923_68dbc39bc2e68.png\",\"buttonText\":\"Show Best Home\",\"buttonLink\":\"#!\"}', 8, '2025-09-30 05:53:27', '2025-09-30 05:53:27'),
(444, 1, 12, '{\"title\":\"Our Customer Reviews\",\"subtitle\":null,\"subTitle\":\"Here are some of the featured Apartment in different categories\",\"ratingLogo\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1758088324_68ca4c84eaf8d.png\",\"items\":[{\"customerName\":\"Rakhab Uddin\",\"ratingPoint\":\"5\",\"customerReview\":\"neckle-Agencycustomer feedback is an invaluable source of information that can help businesses improve their offerings and provide                                         better experiences.\"},{\"customerName\":\"Nur Alam\",\"ratingPoint\":\"3\",\"customerReview\":\"Finest Company\"}]}', 9, '2025-09-30 05:53:27', '2025-09-30 05:53:27'),
(445, 1, 13, '{\"title\":\"Trusted Partners\",\"subtitle\":null,\"ratingLogo\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1758089032_68ca4f48347cf.png\",\"items\":[{\"partnersLogo\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1758090244_68ca54045a11c.png\"},{\"partnersLogo\":\"http:\\/\\/localhost:8000\\/page-contents\\/images\\/1758090283_68ca542bd50bb.png\"}]}', 10, '2025-09-30 05:53:27', '2025-09-30 05:53:27'),
(446, 1, 14, '{\"title\":\"The Latest Insight\",\"subtitle\":null,\"subTitle\":\"Here are some of the featured Apartment in different categories\",\"onEachView\":\"8\"}', 11, '2025-09-30 05:53:27', '2025-09-30 05:53:27');

-- --------------------------------------------------------

--
-- Table structure for table `password_reset_tokens`
--

CREATE TABLE `password_reset_tokens` (
  `email` varchar(255) NOT NULL,
  `token` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `payment_methods`
--

CREATE TABLE `payment_methods` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `payment_methods`
--

INSERT INTO `payment_methods` (`id`, `name`, `description`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'Cash', 'Payment made using physical currency', 1, 1, '2025-08-20 02:35:40', '2025-09-01 02:59:34'),
(2, 'Cheque', 'Payment made Cheque', 1, 2, '2025-08-20 02:35:40', '2025-08-20 03:29:03'),
(4, 'Bank Transfer', 'Direct payment made via bank transfer', 1, 4, '2025-08-20 02:35:40', '2025-08-20 02:35:40'),
(5, 'Other', 'Payment made using other wallets (e.g., bKash, Nagad, Rocket)', 1, 5, '2025-08-20 02:35:40', '2025-09-01 03:14:17');

-- --------------------------------------------------------

--
-- Table structure for table `payment_statuses`
--

CREATE TABLE `payment_statuses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `payment_statuses`
--

INSERT INTO `payment_statuses` (`id`, `name`, `description`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'Pending', 'Payment is awaiting confirmation', 1, 1, '2025-08-20 00:40:59', '2025-08-20 00:40:59'),
(2, 'Paid', 'Payment has been successfully completed', 1, 2, '2025-08-20 00:40:59', '2025-08-20 00:40:59'),
(3, 'Failed', 'Payment attempt was unsuccessful', 1, 3, '2025-08-20 00:40:59', '2025-08-20 00:40:59'),
(4, 'Refunded', 'Payment has been refunded to the customer', 1, 4, '2025-08-20 00:40:59', '2025-08-20 00:40:59'),
(5, 'Cancelled', 'Payment was cancelled by the customer or system', 1, 5, '2025-08-20 00:40:59', '2025-08-20 00:40:59');

-- --------------------------------------------------------

--
-- Table structure for table `payment_types`
--

CREATE TABLE `payment_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` enum('1','0') DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `payment_types`
--

INSERT INTO `payment_types` (`id`, `name`, `description`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'Full Payment', 'Cash payment on delivery', '1', 1, '2025-08-19 23:49:59', '2025-08-20 00:10:33'),
(2, 'Recurring Payment', 'Pay using Visa, MasterCard, or AMEX', '1', 2, '2025-08-19 23:49:59', '2025-08-20 00:10:43'),
(3, 'Installment', 'Direct bank transfer within 3 business days', '1', 3, '2025-08-19 23:49:59', '2025-08-20 00:10:56'),
(4, 'Partial Payment', 'Pay using mobile wallets (e.g., bKash, Nagad, Rocket)', '1', 4, '2025-08-19 23:49:59', '2025-08-20 00:11:10');

-- --------------------------------------------------------

--
-- Table structure for table `personal_access_tokens`
--

CREATE TABLE `personal_access_tokens` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tokenable_type` varchar(255) NOT NULL,
  `tokenable_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `token` varchar(64) NOT NULL,
  `abilities` text DEFAULT NULL,
  `last_used_at` timestamp NULL DEFAULT NULL,
  `expires_at` timestamp NULL DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `personal_access_tokens`
--

INSERT INTO `personal_access_tokens` (`id`, `tokenable_type`, `tokenable_id`, `name`, `token`, `abilities`, `last_used_at`, `expires_at`, `created_at`, `updated_at`) VALUES
(2, 'App\\Models\\User', 1, 'auth_token', '7209a36b79b3003d9fc812d24eac4056f17dbbbe5cb29d19f1c5873775d2475e', '[\"*\"]', '2025-07-18 06:35:38', NULL, '2025-07-18 06:35:25', '2025-07-18 06:35:38'),
(5, 'App\\Models\\User', 1, 'auth_token', 'c17e66d4e472307459488ba5d47cd5055b9c35e83269be72e9443f22fba5be84', '[\"*\"]', '2025-07-24 07:20:25', NULL, '2025-07-24 07:18:29', '2025-07-24 07:20:25'),
(8, 'App\\Models\\User', 1, 'auth_token', '709208ca67d95da317efa7bae00799859c956cafa6467fc1b357637dbec6f4a8', '[\"*\"]', '2025-07-25 03:59:27', NULL, '2025-07-24 23:15:17', '2025-07-25 03:59:27'),
(9, 'App\\Models\\User', 1, 'auth_token', '356e63bc687d2aa09b1c7dc0649c2a8dc4e43b31a9af6b02ada5077650b8a2fb', '[\"*\"]', NULL, NULL, '2025-07-25 00:32:56', '2025-07-25 00:32:56'),
(10, 'App\\Models\\User', 1, 'auth_token', 'a04cfdf7bdd7a56260425eb6b380257c99df48e53ad6bafbe8e5f6d643e430ad', '[\"*\"]', NULL, NULL, '2025-07-25 00:33:08', '2025-07-25 00:33:08'),
(11, 'App\\Models\\User', 1, 'auth_token', '95276cddae2be4b46a20079b9e191df0be9ae72eb350f40a5672fa5e714719f8', '[\"*\"]', '2025-07-27 23:16:29', NULL, '2025-07-25 03:59:31', '2025-07-27 23:16:29'),
(13, 'App\\Models\\User', 2, 'auth_token', '00fd09b3dae6ddf197025831357c69392a0fa1b6ef810e3e905f58086d99cbdf', '[\"*\"]', NULL, NULL, '2025-07-27 23:31:19', '2025-07-27 23:31:19'),
(14, 'App\\Models\\User', 2, 'auth_token', 'adb5206a5738604203d183eca4928982d927b78e2d781a1f12cd4600ea2eae1d', '[\"*\"]', '2025-07-27 23:31:51', NULL, '2025-07-27 23:31:34', '2025-07-27 23:31:51'),
(15, 'App\\Models\\User', 2, 'auth_token', '4b8f2215e89bafaf2bc880d5c4525dd37efdca78b5c21c6489357ac0e1162203', '[\"*\"]', '2025-07-27 23:43:51', NULL, '2025-07-27 23:33:58', '2025-07-27 23:43:51'),
(16, 'App\\Models\\User', 7, 'auth_token', '268bc0e35956975bccc76ce564ee9529354d8766adeb9f0617d04736d175a564', '[\"*\"]', '2025-07-27 23:57:17', NULL, '2025-07-27 23:51:08', '2025-07-27 23:57:17'),
(17, 'App\\Models\\User', 1, 'auth_token', '163465d8fc032513ea82ce81e2d69172f38b86b8900b155916d2bd502868fa5b', '[\"*\"]', '2025-07-28 00:09:45', NULL, '2025-07-28 00:09:36', '2025-07-28 00:09:45'),
(18, 'App\\Models\\User', 1, 'auth_token', '027dd6c50906eaa4f0cc0fcbb1424f86b36ff5a5cb409370a2191835611114b0', '[\"*\"]', '2025-07-28 00:18:28', NULL, '2025-07-28 00:18:18', '2025-07-28 00:18:28'),
(20, 'App\\Models\\User', 1, 'auth_token', 'c7738c5d1f78c09eb123b72a341a378dd0a829a6b5641d0556943e0460dd0fbb', '[\"*\"]', NULL, NULL, '2025-07-28 01:00:45', '2025-07-28 01:00:45'),
(21, 'App\\Models\\User', 1, 'auth_token', '0b2c0e33662b25ee0c9ce44a4703c7251a47eb492ac4c4a97fe9685f1ef0f30e', '[\"*\"]', NULL, NULL, '2025-07-28 01:38:45', '2025-07-28 01:38:45'),
(22, 'App\\Models\\User', 1, 'auth_token', '52eab5a2b989fd64a53ac7b43fe36e409a7eaa9bb38afe2b5832263ae68d977f', '[\"*\"]', NULL, NULL, '2025-07-28 01:39:15', '2025-07-28 01:39:15'),
(23, 'App\\Models\\User', 1, 'auth_token', '68e8d4af8a0d869e638023925781f304cb5a1a25bdf815befd6b2c64c9a4ba41', '[\"*\"]', '2025-07-28 02:25:41', NULL, '2025-07-28 02:25:35', '2025-07-28 02:25:41'),
(25, 'App\\Models\\User', 1, 'auth_token', 'c6f092227f942d7960cdf61dc7e7ebf68654dcc8011877b42dd3b8ae3a8eb444', '[\"*\"]', '2025-07-28 04:31:12', NULL, '2025-07-28 03:08:44', '2025-07-28 04:31:12'),
(29, 'App\\Models\\User', 1, 'auth_token', '50e4f56a8842d15af63d3116e4e4332427c3debbb7760bd243ddb0b33a1f9175', '[\"*\"]', '2025-07-29 00:31:54', NULL, '2025-07-29 00:31:38', '2025-07-29 00:31:54'),
(30, 'App\\Models\\User', 1, 'auth_token', '9926141187f4eaa4bf7bd1fd93f1bda7a5285e824118e19bb4ed3d1335415311', '[\"*\"]', '2025-07-29 00:36:43', NULL, '2025-07-29 00:36:41', '2025-07-29 00:36:43'),
(32, 'App\\Models\\User', 2, 'auth_token', 'f169c8a406d676656b374ed264d9ba4fba773910cefe3adccdb36650635da6e7', '[\"*\"]', NULL, NULL, '2025-07-29 00:38:53', '2025-07-29 00:38:53'),
(33, 'App\\Models\\User', 1, 'auth_token', 'ddcc5d96b5c0bace3b780c566bee7f823d3da6c0009e30b718d514b2268a9e3a', '[\"*\"]', '2025-07-31 23:23:51', NULL, '2025-07-29 00:56:51', '2025-07-31 23:23:51'),
(35, 'App\\Models\\User', 1, 'auth_token', 'b17c5869263c6aca25a16092a048574516b8b9e6d72934a82807554f877e67bd', '[\"*\"]', NULL, NULL, '2025-07-30 00:17:21', '2025-07-30 00:17:21'),
(36, 'App\\Models\\User', 1, 'auth_token', 'c41d6f03f8b8baea1eae17df5632748ebc5f65f2f35653c07d9c4421f1a71556', '[\"*\"]', NULL, NULL, '2025-07-30 00:18:30', '2025-07-30 00:18:30'),
(39, 'App\\Models\\User', 1, 'auth_token', '7a18688b06c4cfe8d78d5cd4ec61dbe763b03ca2203241946f1845662863296d', '[\"*\"]', '2025-08-01 04:07:35', NULL, '2025-08-01 04:06:44', '2025-08-01 04:07:35'),
(41, 'App\\Models\\User', 1, 'auth_token', '204ddc0b63a0f9f759a436defe7c32659e7dda9381b3bf4d69814f0094176909', '[\"*\"]', '2025-08-04 00:15:19', NULL, '2025-08-03 23:17:58', '2025-08-04 00:15:19'),
(42, 'App\\Models\\User', 1, 'auth_token', '47fec9c9a907ca94460e95f5e7d428abd5f6ebe2eb403a1c47dfe0cda09155b2', '[\"*\"]', '2025-08-05 07:27:54', NULL, '2025-08-04 00:15:32', '2025-08-05 07:27:54'),
(43, 'App\\Models\\User', 1, 'auth_token', '84806ad5d4e5376e66f32a17daf91506746aba0ea2e5de6db3875ae3b6b98995', '[\"*\"]', '2025-08-25 05:48:30', NULL, '2025-08-04 08:51:28', '2025-08-25 05:48:30'),
(44, 'App\\Models\\User', 1, 'test', '3a8e950113244c9d4bb71c83a11ed005e179bdaae21826681d82d73f0ca074db', '[\"*\"]', NULL, NULL, '2025-08-05 01:12:20', '2025-08-05 01:12:20'),
(45, 'App\\Models\\User', 1, 'test', '8513ada2c64b3ff83f303c0b56e533e7194eb47d4382476423016818376fbb4e', '[\"*\"]', NULL, NULL, '2025-08-05 01:12:34', '2025-08-05 01:12:34'),
(46, 'App\\Models\\User', 1, 'auth_token', 'b932982985357f7208a8bf1d61db86a9bb6a8e5fd5403d52ef14960037ee7a34', '[\"*\"]', '2025-09-16 12:43:39', NULL, '2025-08-06 06:44:27', '2025-09-16 12:43:39'),
(47, 'App\\Models\\User', 1, 'auth_token', 'aecb046b40581b1d5a941aecc276d65461fce2e8628cac22df72d110c982c109', '[\"*\"]', '2025-08-06 12:51:38', NULL, '2025-08-06 12:51:38', '2025-08-06 12:51:38'),
(48, 'App\\Models\\User', 1, 'auth_token', 'dae2776f1a6cebbe8531c91a838138276a05afe9e6d6e901df59f5467ed3f4bf', '[\"*\"]', '2025-08-13 00:45:25', NULL, '2025-08-06 23:11:02', '2025-08-13 00:45:25'),
(49, 'App\\Models\\User', 1, 'test', 'd5c0e158f71cbf7d019ee27e17fda7a7d238e5339af73366296aabe50555a206', '[\"*\"]', NULL, NULL, '2025-08-07 01:58:37', '2025-08-07 01:58:37'),
(50, 'App\\Models\\User', 1, 'auth_token', 'b6a9cf800cb0ef3dc7ab339da04c381225cfc16bde76416759283398acf03154', '[\"*\"]', '2025-08-08 03:25:09', NULL, '2025-08-08 03:15:28', '2025-08-08 03:25:09'),
(51, 'App\\Models\\User', 1, 'auth_token', '94ed85e2b474fe00225005ed077961daf921edc15b5cf211a440c21a032b9de1', '[\"*\"]', '2025-08-08 05:58:37', NULL, '2025-08-08 05:58:33', '2025-08-08 05:58:37'),
(52, 'App\\Models\\User', 1, 'auth_token', '56109953f6bae99f2ad294e5e4d647c2ffc785f9bd84228a2368b9de834f1dca', '[\"*\"]', '2025-08-13 01:31:22', NULL, '2025-08-13 00:45:30', '2025-08-13 01:31:22'),
(53, 'App\\Models\\User', 1, 'auth_token', '171f7e048efdd0d5e87b94f44b6ddfe3864c4a830e10122a875c82e7c060f272', '[\"*\"]', '2025-08-13 01:42:17', NULL, '2025-08-13 01:42:16', '2025-08-13 01:42:17'),
(54, 'App\\Models\\User', 1, 'auth_token', 'ea2a8a99cc5549e83dd8211d612917616719b5e2f20320631d79100b2127e55c', '[\"*\"]', '2025-08-13 01:44:00', NULL, '2025-08-13 01:43:59', '2025-08-13 01:44:00'),
(55, 'App\\Models\\User', 1, 'auth_token', 'd5a02abb6d21634f1b6b90a15f01bd2a4759dba810bd92d516514743396a7fcd', '[\"*\"]', '2025-08-13 01:44:07', NULL, '2025-08-13 01:44:05', '2025-08-13 01:44:07'),
(56, 'App\\Models\\User', 1, 'auth_token', 'd8b0feab4d58e311099b0330fd12890bc3780fcc6dbfcca3da27f8f103d990a6', '[\"*\"]', '2025-08-13 01:46:41', NULL, '2025-08-13 01:46:40', '2025-08-13 01:46:41'),
(58, 'App\\Models\\User', 1, 'auth_token', 'cd739f5cc2068c5a504bf658f0a00d4d628686d074de1f50d62cb0aac4430418', '[\"*\"]', '2025-08-13 05:34:34', NULL, '2025-08-13 02:29:56', '2025-08-13 05:34:34'),
(59, 'App\\Models\\User', 1, 'auth_token', '0c7446b9b25c61772851c2ede0624a2d26e0366fbfdfdd6c79c4427fa7bd6fe5', '[\"*\"]', NULL, NULL, '2025-08-13 05:34:38', '2025-08-13 05:34:38'),
(60, 'App\\Models\\User', 1, 'auth_token', 'd092523a9334c492c251f7dcfc61aa8e33bf52b6add77a7a636902bf975ac77f', '[\"*\"]', NULL, NULL, '2025-08-13 05:34:41', '2025-08-13 05:34:41'),
(61, 'App\\Models\\User', 1, 'auth_token', 'ad1453ccda0ab802ebf52ab561e8b0cd460006e130b8764ab7c64cf393bec2fd', '[\"*\"]', NULL, NULL, '2025-08-13 05:34:45', '2025-08-13 05:34:45'),
(62, 'App\\Models\\User', 1, 'auth_token', '42e1d0e6c555479e10c10af7927d8c80bb70701449005116f8af64da14ed74be', '[\"*\"]', '2025-08-13 07:40:50', NULL, '2025-08-13 05:35:20', '2025-08-13 07:40:50'),
(63, 'App\\Models\\User', 1, 'auth_token', 'e46c7d7b28940dbd8e8dda6c9b8e71a89c850f084849bb09bd272e75e41bd6eb', '[\"*\"]', NULL, NULL, '2025-08-13 07:40:52', '2025-08-13 07:40:52'),
(64, 'App\\Models\\User', 1, 'auth_token', '2bcf48fea07eab2f7adc81a2feca9a7af6f230381176193ff6da47dbca3f5a71', '[\"*\"]', NULL, NULL, '2025-08-13 07:40:54', '2025-08-13 07:40:54'),
(65, 'App\\Models\\User', 1, 'auth_token', '8d7f12bb7eeb8e7c96c16339afcfa8b28bc479d44e4d7c2b844a3f4fec2de254', '[\"*\"]', NULL, NULL, '2025-08-13 07:40:55', '2025-08-13 07:40:55'),
(66, 'App\\Models\\User', 1, 'auth_token', '15c41a15cdf8d72828e44c40f328360238d4bf47e4e48bf63db0f6798cbde6a1', '[\"*\"]', '2025-08-13 07:42:12', NULL, '2025-08-13 07:42:02', '2025-08-13 07:42:12'),
(67, 'App\\Models\\User', 1, 'auth_token', 'c3fd66ea4878d2b06775ba5ebc2e61c97f45ea8c67756bde4946c8bcbbf04cdf', '[\"*\"]', '2025-08-15 00:02:33', NULL, '2025-08-13 22:31:35', '2025-08-15 00:02:33'),
(68, 'App\\Models\\User', 1, 'auth_token', '3b2e8b722564ad1a94fbe5202f54415fa52f0f5b2889ad5dbbc27d25257794d8', '[\"*\"]', '2025-08-15 09:44:22', NULL, '2025-08-15 09:43:19', '2025-08-15 09:44:22'),
(69, 'App\\Models\\User', 1, 'auth_token', 'd5af569ae0c05e78daa2242db43fb17129c12610a30afd9290cfb623dc1c59d1', '[\"*\"]', '2025-08-21 03:04:33', NULL, '2025-08-15 04:23:05', '2025-08-21 03:04:33'),
(70, 'App\\Models\\User', 1, 'auth_token', 'a9582e16ec668cf7a9172ac67fcae00073bcf9d6e0e85942ca600335b29bebbd', '[\"*\"]', '2025-08-22 12:10:26', NULL, '2025-08-22 09:46:18', '2025-08-22 12:10:26'),
(71, 'App\\Models\\User', 1, 'auth_token', '6c83ea44716c146a03cfd0632b10fa4c94274bf7bb425057c4119873657e1aa5', '[\"*\"]', '2025-08-26 07:06:17', NULL, '2025-08-25 00:00:33', '2025-08-26 07:06:17'),
(72, 'App\\Models\\User', 1, 'auth_token', '79843949920390df4cb288e813d708442e665c697e0720d42b2e348c8d535593', '[\"*\"]', '2025-08-28 06:59:14', NULL, '2025-08-26 07:06:20', '2025-08-28 06:59:14'),
(73, 'App\\Models\\User', 1, 'auth_token', 'fc74d904175f062841922b37dd8ca2547a45579d17bf878f9812d7ca30771a70', '[\"*\"]', '2025-08-29 00:41:53', NULL, '2025-08-28 06:59:23', '2025-08-29 00:41:53'),
(74, 'App\\Models\\User', 1, 'auth_token', '1f07f2bf7d80cf5573879ed45eeb78af362b3972633a3f34ce55eed5d9863e0a', '[\"*\"]', '2025-08-29 02:54:09', NULL, '2025-08-29 00:43:02', '2025-08-29 02:54:09'),
(75, 'App\\Models\\User', 1, 'auth_token', 'a347b4034c815ecccec2fa87633b50f70c829722bd8a8e8b510d3c3696082243', '[\"*\"]', '2025-08-29 02:56:07', NULL, '2025-08-29 02:55:52', '2025-08-29 02:56:07'),
(76, 'App\\Models\\User', 1, 'auth_token', 'acfcead08251c0d20479d0f3155be2568a13ea8f5fbca9358badfe3129d24616', '[\"*\"]', '2025-08-29 03:24:11', NULL, '2025-08-29 02:56:10', '2025-08-29 03:24:11'),
(77, 'App\\Models\\User', 1, 'auth_token', '315e80d270cd530bfb922b7ef2af1df9678fe3e05a74e583a2268d1551217c23', '[\"*\"]', '2025-08-29 03:25:59', NULL, '2025-08-29 03:25:48', '2025-08-29 03:25:59'),
(78, 'App\\Models\\User', 1, 'auth_token', '942049ea389df8a21e09b60ba33bebda422f761b63345c0086e20fa4b7284c35', '[\"*\"]', '2025-08-29 03:50:33', NULL, '2025-08-29 03:27:55', '2025-08-29 03:50:33'),
(79, 'App\\Models\\User', 1, 'auth_token', '3a23ae1cb1a3b5550d55b2cf7cf04713e379871a8b26edc2365c0a08d1b34463', '[\"*\"]', '2025-08-29 04:30:56', NULL, '2025-08-29 03:50:44', '2025-08-29 04:30:56'),
(80, 'App\\Models\\User', 1, 'auth_token', 'fd794f8d0f38f5382a8283d16f269b17cb52a609079c015f0d4dc8d63287b15f', '[\"*\"]', '2025-08-29 04:59:49', NULL, '2025-08-29 04:33:16', '2025-08-29 04:59:49'),
(81, 'App\\Models\\User', 1, 'auth_token', 'cf9e1c38eeb7693d8704384303786f077c7980715a5e2efa8327143ac41e107a', '[\"*\"]', '2025-08-29 05:28:52', NULL, '2025-08-29 05:00:09', '2025-08-29 05:28:52'),
(82, 'App\\Models\\User', 1, 'auth_token', 'd89cc767ece654bf5c80113e862662e4a6c68c166e1c2f809f065f6475be47d0', '[\"*\"]', '2025-09-01 00:59:14', NULL, '2025-08-29 05:28:56', '2025-09-01 00:59:14'),
(83, 'App\\Models\\User', 1, 'auth_token', 'a3e45f00d511354db702447daed6367859e613d0cd7c7db36346c5ac735b5af7', '[\"*\"]', '2025-09-01 01:50:21', NULL, '2025-09-01 01:00:13', '2025-09-01 01:50:21'),
(84, 'App\\Models\\User', 1, 'auth_token', '2405200108471f9a48f95ba48204981da7f9d05bbf43854e01e6d9d8567ff8e9', '[\"*\"]', '2025-09-01 02:49:58', NULL, '2025-09-01 01:50:37', '2025-09-01 02:49:58'),
(85, 'App\\Models\\User', 1, 'auth_token', '69cffb82046b695a70fd18e43d6b22638951cd903c776c0ba7fc8613dd41c50e', '[\"*\"]', '2025-09-01 22:57:35', NULL, '2025-09-01 02:50:01', '2025-09-01 22:57:35'),
(86, 'App\\Models\\User', 1, 'auth_token', '5d0edcdc6f0d3b99f11aa84e0caf29737113a08201a257f339dbb16df4d5e99d', '[\"*\"]', '2025-09-03 01:01:25', NULL, '2025-09-01 23:00:53', '2025-09-03 01:01:25'),
(87, 'App\\Models\\User', 1, 'auth_token', '2954b086866ebe6aa1cbd669d56f7f4245c9260fd6ed6a9c0d415a461ed2894d', '[\"*\"]', '2025-09-03 03:52:12', NULL, '2025-09-03 01:06:36', '2025-09-03 03:52:12'),
(88, 'App\\Models\\User', 1, 'auth_token', '1db74601e8c119a69ff917b7454596dd76c09ef7b32f191fb0345f6d7e17299a', '[\"*\"]', '2025-09-03 03:53:57', NULL, '2025-09-03 03:53:53', '2025-09-03 03:53:57'),
(89, 'App\\Models\\User', 1, 'auth_token', '10ac9be870cc2fe5b8c5438ab70ba8492e2a102a6ee5c5b8eb31b57da5d37c6f', '[\"*\"]', '2025-09-03 04:01:09', NULL, '2025-09-03 03:54:14', '2025-09-03 04:01:09'),
(90, 'App\\Models\\User', 1, 'auth_token', 'e78c71af922b95c2909a55639ae89d131e2ccaa5586b2351209507d08a1a9806', '[\"*\"]', '2025-09-03 04:55:40', NULL, '2025-09-03 04:07:31', '2025-09-03 04:55:40'),
(91, 'App\\Models\\User', 1, 'auth_token', '69a3ce6716ea49283a9020daff97b6ff6aa53003fac797ea5e25ac0b49df1964', '[\"*\"]', '2025-09-03 05:33:51', NULL, '2025-09-03 05:02:08', '2025-09-03 05:33:51'),
(92, 'App\\Models\\User', 1, 'auth_token', '83951fb4454e082fdb5aeeb66ef2cccb164f1ffa4e097c249b0507590ce5d246', '[\"*\"]', '2025-09-03 05:41:06', NULL, '2025-09-03 05:36:49', '2025-09-03 05:41:06'),
(93, 'App\\Models\\User', 1, 'auth_token', 'e2380c353b9e99f1ab4480084190e4fbe19698707462f77494403664a6abf1bf', '[\"*\"]', '2025-09-03 05:46:52', NULL, '2025-09-03 05:41:16', '2025-09-03 05:46:52'),
(94, 'App\\Models\\User', 1, 'auth_token', '4d10d64e276f011f4b79b0ca8a405ad50143b8e898bd82cb5b9cb19ee4d3e1ac', '[\"*\"]', '2025-09-03 06:01:08', NULL, '2025-09-03 05:47:51', '2025-09-03 06:01:08'),
(95, 'App\\Models\\User', 1, 'auth_token', '43cd47945977facc2878386c25d946c4343e21f556f9886ff2699bdc365674af', '[\"*\"]', '2025-09-12 03:52:40', NULL, '2025-09-03 06:03:19', '2025-09-12 03:52:40'),
(96, 'App\\Models\\User', 1, 'auth_token', 'f776ccafc7a6422d963ce04f7c9b8bc0e9d4b169b52e7159c13f9c76e93a7e21', '[\"*\"]', '2025-09-05 05:50:20', NULL, '2025-09-05 05:50:20', '2025-09-05 05:50:20'),
(97, 'App\\Models\\User', 1, 'auth_token', 'fceaccdc402c4fc6c925619c59e3f4374a40213d61c84f2293b6fa35c08d8c2a', '[\"*\"]', '2025-09-15 01:12:50', NULL, '2025-09-12 03:53:46', '2025-09-15 01:12:50'),
(98, 'App\\Models\\User', 1, 'auth_token', '9df5b5dd08eb7b01d2d476eb244baae89aab0e6a1621efe51b8d17c487cb6459', '[\"*\"]', '2025-09-15 04:28:27', NULL, '2025-09-15 01:17:53', '2025-09-15 04:28:27'),
(100, 'App\\Models\\User', 1, 'auth_token', '74bc7b799ac7d43dd3363658b6116b1a896d2d5c6b2ceb7fd275d6bc4177adbd', '[\"*\"]', '2025-09-16 06:53:05', NULL, '2025-09-16 06:53:04', '2025-09-16 06:53:05'),
(101, 'App\\Models\\User', 1, 'auth_token', '71f4eb75d6d2cc2aeec152b6528ae3a705cbe1b787952fdb8d68e1ff2372dcc8', '[\"*\"]', '2025-09-17 06:55:19', NULL, '2025-09-16 07:16:38', '2025-09-17 06:55:19'),
(103, 'App\\Models\\User', 1, 'auth_token', '8ea5b590ed393d693b41196891fc5d5e8b8ce9dc716136ac09868a6ea723be04', '[\"*\"]', '2025-09-21 23:13:34', NULL, '2025-09-21 23:00:36', '2025-09-21 23:13:34'),
(104, 'App\\Models\\User', 1, 'auth_token', '79aa9e184215a66d8d28cd4b6801879ec2f06709c727a9011549ffc4c5704995', '[\"*\"]', '2025-09-21 23:21:29', NULL, '2025-09-21 23:21:05', '2025-09-21 23:21:29'),
(105, 'App\\Models\\User', 1, 'auth_token', '9af5cd4f3306bde0b4882e8141a05c4be4c6bf6ae4eae0d83140c1958704d951', '[\"*\"]', '2025-09-21 23:21:34', NULL, '2025-09-21 23:21:33', '2025-09-21 23:21:34'),
(108, 'App\\Models\\User', 1, 'auth_token', '48d9e0eb22b472ef8def34c449a79607354bd608cfe424912be96c100f9ccdba', '[\"*\"]', '2025-09-22 00:04:25', NULL, '2025-09-21 23:50:46', '2025-09-22 00:04:25'),
(109, 'App\\Models\\User', 1, 'auth_token', '2f1be802dc5a6793df72e916b10be5f8030a37dbbb0f928f81c416aacd987f02', '[\"*\"]', '2025-09-22 01:09:47', NULL, '2025-09-22 00:04:27', '2025-09-22 01:09:47'),
(113, 'App\\Models\\User', 1, 'auth_token', '9e3c8e974b95e30e3fb731f0b34856938508bf40fe85e1d9e68f43843a52c46c', '[\"*\"]', '2025-09-22 23:32:47', NULL, '2025-09-22 03:44:04', '2025-09-22 23:32:47'),
(114, 'App\\Models\\User', 1, 'auth_token', 'df7c740553214295133d193e5e8442acde37fdb2e338d03b9547154c08db933a', '[\"*\"]', '2025-09-25 22:45:36', NULL, '2025-09-22 23:37:47', '2025-09-25 22:45:36'),
(115, 'App\\Models\\User', 1, 'auth_token', '7f7c970e3e43311755eff17126c74e1ec1af13a582912f47d7d51835911c8d1a', '[\"*\"]', '2025-10-05 23:10:13', NULL, '2025-09-26 00:02:24', '2025-10-05 23:10:13'),
(116, 'App\\Models\\User', 1, 'auth_token', '905d10cb94984faf7be734da21c1a121e23a1eede8e36f6f301799a4591b0ecc', '[\"*\"]', '2025-10-06 03:43:55', NULL, '2025-10-05 23:10:16', '2025-10-06 03:43:55');

-- --------------------------------------------------------

--
-- Table structure for table `projects`
--

CREATE TABLE `projects` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `description` text NOT NULL,
  `property_type_id` bigint(20) UNSIGNED DEFAULT NULL,
  `property_status_id` bigint(20) UNSIGNED DEFAULT NULL,
  `property_stage_id` bigint(20) NOT NULL,
  `status` enum('planning','under_construction','completed','sold','rented','maintenance') NOT NULL,
  `location` varchar(255) NOT NULL,
  `address` varchar(500) NOT NULL,
  `state_id` bigint(20) UNSIGNED DEFAULT NULL,
  `country_id` bigint(20) UNSIGNED DEFAULT NULL,
  `location_id` int(11) NOT NULL,
  `area_sqft` decimal(10,2) DEFAULT NULL,
  `price_per_area_sqft` decimal(10,2) DEFAULT NULL,
  `total_price` decimal(10,2) NOT NULL,
  `bedrooms` int(11) DEFAULT NULL,
  `bathrooms` int(11) DEFAULT NULL,
  `floors` int(11) DEFAULT NULL,
  `parking_spaces` int(11) DEFAULT NULL,
  `year_built` int(11) DEFAULT NULL,
  `amenities` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`amenities`)),
  `features` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`features`)),
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `is_available` tinyint(1) NOT NULL DEFAULT 1,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `projects`
--

INSERT INTO `projects` (`id`, `title`, `description`, `property_type_id`, `property_status_id`, `property_stage_id`, `status`, `location`, `address`, `state_id`, `country_id`, `location_id`, `area_sqft`, `price_per_area_sqft`, `total_price`, `bedrooms`, `bathrooms`, `floors`, `parking_spaces`, `year_built`, `amenities`, `features`, `is_featured`, `is_available`, `created_by`, `updated_by`, `created_at`, `updated_at`, `deleted_at`) VALUES
(9, 'adsf', '<p>adfad</p>', 4, 2, 20, 'planning', 'Gulshan', '<p>adfa</p>', 1, 23, 16, 23.00, 23.00, 529.00, 2, 3, 23, NULL, NULL, NULL, NULL, 1, 1, 1, 1, '2025-09-17 04:18:11', '2025-09-30 06:26:11', '2025-09-30 06:26:11'),
(10, 'adsf', '<p>adfad</p>', 4, 2, 20, 'planning', 'Gulshan', '<p>adfa</p>', 1, 23, 16, 23.00, 23.00, 529.00, 2, 3, 23, NULL, NULL, NULL, NULL, 1, 1, 1, 1, '2025-09-17 04:18:46', '2025-09-30 06:26:07', '2025-09-30 06:26:07'),
(11, 'adfa', '<p>adsfadsf</p>', 4, 3, 20, 'planning', 'Gulshan', '<p>adfadsf</p>', 1, 23, 16, 23.00, 23.00, 529.00, 23, 23, 23, NULL, NULL, NULL, NULL, 1, 1, 1, 1, '2025-09-17 04:20:12', '2025-09-30 06:26:03', '2025-09-30 06:26:03'),
(12, 'Mountain View Villa', '<p>The living room features large windows that allow ample natural light to illuminate the room, creating a warm and inviting ambiance. The plush sofas and cozy armchairs provide a comfortable seating arrangement, ideal for unwinding after a long day. A sleek flat-screen TV mounted on the wall offers entertainment options for your viewing pleasure.</p><p>Throughout the apartment, you\'ll find tasteful decor, modern lighting fixtures, and high-quality finishes, creating a sophisticated and contemporary atmosphere. Air conditioning and heating systems ensure a comfortable climate year-round, while high-speed internet connectivity keeps you connected to the digital world.</p>', 3, 2, 19, 'planning', 'Gulshan', '<p>asdfadf</p>', 1, 23, 16, 23234.00, 14.00, 325276.00, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 1, 1, 1, '2025-09-30 06:44:42', '2025-10-03 07:15:43', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `project_amenities`
--

CREATE TABLE `project_amenities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `property_amenity_id` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_contractors`
--

CREATE TABLE `project_contractors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `contractor_id` bigint(20) UNSIGNED NOT NULL,
  `assignment_role` varchar(255) DEFAULT NULL,
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('active','inactive','completed','terminated') NOT NULL DEFAULT 'active',
  `contract_amount` decimal(15,2) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `contract_documents` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`contract_documents`)),
  `assigned_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `project_contractors`
--

INSERT INTO `project_contractors` (`id`, `project_id`, `contractor_id`, `assignment_role`, `start_date`, `end_date`, `status`, `contract_amount`, `notes`, `contract_documents`, `assigned_by`, `created_at`, `updated_at`) VALUES
(4, 2, 4, 'General Contractor', '2025-07-17', NULL, 'active', 10000000.00, 'Updated test', '[]', 1, '2025-07-29 00:41:18', '2025-07-29 00:49:01');

-- --------------------------------------------------------

--
-- Table structure for table `project_documents`
--

CREATE TABLE `project_documents` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `file_path` varchar(255) NOT NULL,
  `show_on_frontend` tinyint(1) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `project_employees`
--

CREATE TABLE `project_employees` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `employee_id` bigint(20) UNSIGNED NOT NULL,
  `assigned_by` bigint(20) UNSIGNED NOT NULL,
  `role` varchar(255) DEFAULT NULL,
  `status` enum('active','inactive','completed','suspended') NOT NULL DEFAULT 'active',
  `responsibilities` text DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `project_employees`
--

INSERT INTO `project_employees` (`id`, `project_id`, `employee_id`, `assigned_by`, `role`, `status`, `responsibilities`, `notes`, `created_at`, `updated_at`) VALUES
(1, 2, 3, 1, 'Suppervision', 'active', 'adfadf', NULL, '2025-07-29 02:40:43', '2025-07-29 02:40:43');

-- --------------------------------------------------------

--
-- Table structure for table `project_images`
--

CREATE TABLE `project_images` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `image_path` varchar(255) NOT NULL,
  `image_name` varchar(255) NOT NULL,
  `image_type` varchar(255) DEFAULT NULL,
  `image_size` bigint(20) DEFAULT NULL,
  `alt_text` varchar(255) DEFAULT NULL,
  `caption` text DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `project_images`
--

INSERT INTO `project_images` (`id`, `project_id`, `image_path`, `image_name`, `image_type`, `image_size`, `alt_text`, `caption`, `sort_order`, `is_featured`, `created_by`, `updated_by`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 9, 'Project/1758104291_0_recieve.png', 'recieve.png', 'gallery', 1065, 'recieve.png', NULL, 1, 1, 1, NULL, '2025-09-17 04:18:11', '2025-09-17 04:18:11', NULL),
(2, 9, 'Project/1758104291_1_loaction.png', 'loaction.png', 'gallery', 1512, 'loaction.png', NULL, 2, 0, 1, NULL, '2025-09-17 04:18:11', '2025-09-17 04:18:11', NULL),
(3, 9, 'Project/1758104291_2_contact.png', 'contact.png', 'gallery', 1303, 'contact.png', NULL, 3, 0, 1, NULL, '2025-09-17 04:18:11', '2025-09-17 04:18:11', NULL),
(4, 10, 'Project/1758104326_0_recieve.png', 'recieve.png', 'gallery', 1065, 'recieve.png', NULL, 1, 1, 1, NULL, '2025-09-17 04:18:46', '2025-09-17 04:18:46', NULL),
(5, 10, 'Project/1758104326_1_loaction.png', 'loaction.png', 'gallery', 1512, 'loaction.png', NULL, 2, 0, 1, NULL, '2025-09-17 04:18:46', '2025-09-17 04:18:46', NULL),
(6, 10, 'Project/1758104326_2_contact.png', 'contact.png', 'gallery', 1303, 'contact.png', NULL, 3, 0, 1, NULL, '2025-09-17 04:18:46', '2025-09-17 04:18:46', NULL),
(7, 11, 'Project/1758104412_0_download (36).jpg', 'download (36).jpg', 'gallery', 6967, 'download (36).jpg', NULL, 1, 1, 1, NULL, '2025-09-17 04:20:12', '2025-09-17 04:20:12', NULL),
(8, 11, 'Project/1758104412_1_download (35).jpg', 'download (35).jpg', 'gallery', 14348, 'download (35).jpg', NULL, 2, 0, 1, NULL, '2025-09-17 04:20:12', '2025-09-17 04:20:12', NULL),
(9, 11, 'Project/1758104412_2_download (33).jpg', 'download (33).jpg', 'gallery', 6631, 'download (33).jpg', NULL, 3, 0, 1, NULL, '2025-09-17 04:20:12', '2025-09-17 04:20:12', NULL),
(16, 12, 'Project/1759726282_0_poperty-img-02.png', 'poperty-img-02.png', 'gallery', 34230, 'poperty-img-02.png', NULL, 1, 1, 1, NULL, '2025-10-05 22:51:22', '2025-10-05 22:51:22', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `project_units`
--

CREATE TABLE `project_units` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `unit_number` varchar(50) NOT NULL,
  `rent_type` varchar(50) DEFAULT NULL,
  `lease_for` varchar(50) DEFAULT NULL,
  `unit_type_id` bigint(20) NOT NULL,
  `unit_size` decimal(10,2) NOT NULL,
  `floor_number` int(11) DEFAULT NULL,
  `area_sqft` decimal(10,2) DEFAULT NULL,
  `bedrooms` int(11) DEFAULT NULL,
  `bathrooms` int(11) DEFAULT NULL,
  `property_service_id` bigint(20) DEFAULT 0,
  `rent_price` decimal(12,2) DEFAULT NULL,
  `sell_price` decimal(12,2) DEFAULT NULL,
  `lease_price` decimal(12,2) DEFAULT NULL,
  `propertyService` varchar(20) NOT NULL,
  `currency` varchar(3) NOT NULL DEFAULT 'USD',
  `status` varchar(50) NOT NULL DEFAULT 'available',
  `description` text DEFAULT NULL,
  `features` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`features`)),
  `is_available` tinyint(1) NOT NULL DEFAULT 1,
  `rent_type_id` int(11) DEFAULT NULL,
  `lease_type_id` int(11) DEFAULT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `project_units`
--

INSERT INTO `project_units` (`id`, `project_id`, `unit_number`, `rent_type`, `lease_for`, `unit_type_id`, `unit_size`, `floor_number`, `area_sqft`, `bedrooms`, `bathrooms`, `property_service_id`, `rent_price`, `sell_price`, `lease_price`, `propertyService`, `currency`, `status`, `description`, `features`, `is_available`, `rent_type_id`, `lease_type_id`, `created_by`, `updated_by`, `created_at`, `updated_at`, `deleted_at`) VALUES
(3, 9, '23', NULL, NULL, 4, 23.00, 23, NULL, NULL, NULL, 0, 529.00, 529.00, 529.00, 'lease', 'USD', 'available', '<p>asdfasdf</p>', '[]', 1, NULL, 18, 1, NULL, '2025-09-17 04:18:11', '2025-09-17 04:18:11', NULL),
(4, 10, '23', NULL, NULL, 4, 23.00, 23, NULL, NULL, NULL, 0, 529.00, 529.00, 529.00, 'lease', 'USD', 'available', '<p>asdfasdf</p>', '[]', 1, NULL, 18, 1, 1, '2025-09-17 04:18:46', '2025-09-17 04:19:22', NULL),
(5, 11, '23', NULL, NULL, 2, 23.00, 32, NULL, NULL, NULL, 0, 529.00, 529.00, 529.00, 'rent', 'USD', 'available', '<p>asdfad</p>', '[]', 1, NULL, NULL, 1, 1, '2025-09-17 04:36:59', '2025-09-17 04:36:59', NULL);

-- --------------------------------------------------------

--
-- Table structure for table `project_vendors`
--

CREATE TABLE `project_vendors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `vendor_id` bigint(20) UNSIGNED NOT NULL,
  `assignment_role` varchar(255) NOT NULL DEFAULT 'General Vendor',
  `start_date` date DEFAULT NULL,
  `end_date` date DEFAULT NULL,
  `status` enum('active','inactive','completed','terminated') NOT NULL DEFAULT 'active',
  `contract_amount` decimal(15,2) DEFAULT NULL,
  `notes` text DEFAULT NULL,
  `contract_documents` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`contract_documents`)),
  `assigned_by` bigint(20) UNSIGNED NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `project_vendors`
--

INSERT INTO `project_vendors` (`id`, `project_id`, `vendor_id`, `assignment_role`, `start_date`, `end_date`, `status`, `contract_amount`, `notes`, `contract_documents`, `assigned_by`, `created_at`, `updated_at`) VALUES
(2, 2, 1, 'General Vendor', '2025-07-01', '2025-08-09', 'active', 1000.00, NULL, '[]', 1, '2025-07-30 00:13:15', '2025-07-30 00:13:15');

-- --------------------------------------------------------

--
-- Table structure for table `project_videos`
--

CREATE TABLE `project_videos` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `project_id` bigint(20) UNSIGNED NOT NULL,
  `video_type` enum('upload','youtube','url') NOT NULL,
  `video_path` varchar(255) DEFAULT NULL,
  `video_url` varchar(255) DEFAULT NULL,
  `youtube_url` varchar(255) DEFAULT NULL,
  `title` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `duration` int(11) DEFAULT NULL,
  `thumbnail` varchar(255) DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `is_featured` tinyint(1) NOT NULL DEFAULT 0,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- --------------------------------------------------------

--
-- Table structure for table `property_amenities`
--

CREATE TABLE `property_amenities` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `category` enum('basic','recreational','security','parking','utilities','maintenance','other') NOT NULL DEFAULT 'basic',
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `deleted_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `property_amenities`
--

INSERT INTO `property_amenities` (`id`, `name`, `description`, `icon`, `category`, `is_active`, `sort_order`, `created_by`, `updated_by`, `created_at`, `updated_at`, `deleted_at`) VALUES
(1, 'Power Backup', '24/7 power backup with generator', 'power-backup', 'basic', 0, 1, NULL, 1, '2025-07-31 22:55:45', '2025-08-04 09:48:17', NULL),
(2, 'Water Supply', '24/7 water supply with backup tank', 'water-supply', 'basic', 1, 2, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(3, 'Elevator', 'High-speed elevators with backup power', 'elevator', 'basic', 1, 3, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(4, 'WiFi', 'High-speed internet connectivity', 'wifi', 'basic', 1, 4, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(5, 'Swimming Pool', 'Olympic size swimming pool with lifeguard', 'swimming-pool', 'recreational', 1, 5, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(6, 'Gymnasium', 'Fully equipped modern gymnasium', 'gym', 'recreational', 1, 6, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(7, 'Clubhouse', 'Community clubhouse for events', 'clubhouse', 'recreational', 1, 7, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(8, 'Children Playground', 'Safe and secure playground for children', 'playground', 'recreational', 1, 8, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(9, 'Garden', 'Landscaped gardens and green spaces', 'garden', 'recreational', 1, 9, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(10, '24/7 Security', 'Round the clock security personnel', 'security', 'security', 1, 10, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(11, 'CCTV Surveillance', 'Complete CCTV coverage of all areas', 'cctv', 'security', 1, 11, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(12, 'Intercom Facility', 'Video intercom system for each unit', 'intercom', 'security', 1, 12, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(13, 'Covered Parking', 'Covered parking spaces for all residents', 'parking', 'parking', 1, 13, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(14, 'Visitor Parking', 'Dedicated parking spaces for visitors', 'parking', 'parking', 1, 14, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(15, 'Gas Pipeline', 'Piped gas connection to all units', 'utilities', 'utilities', 1, 15, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(16, 'Waste Management', 'Organized waste collection and disposal', 'utilities', 'utilities', 1, 16, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(17, 'Housekeeping', 'Regular housekeeping of common areas', 'maintenance', 'maintenance', 1, 17, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(18, 'Maintenance Service', '24/7 maintenance and repair services', 'maintenance', 'maintenance', 1, 18, NULL, NULL, '2025-07-31 22:55:45', '2025-07-31 22:55:45', NULL),
(19, 'New Amenity', 'test', 'intercom', 'basic', 1, 50, 2, NULL, '2025-07-31 23:25:43', '2025-07-31 23:25:58', '2025-07-31 23:25:58');

-- --------------------------------------------------------

--
-- Table structure for table `property_items`
--

CREATE TABLE `property_items` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `invoice_id` bigint(20) UNSIGNED NOT NULL,
  `property_id` bigint(20) UNSIGNED NOT NULL,
  `unit_id` bigint(20) UNSIGNED DEFAULT NULL,
  `amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `tax_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `property_items`
--

INSERT INTO `property_items` (`id`, `invoice_id`, `property_id`, `unit_id`, `amount`, `tax_amount`, `total_amount`, `created_at`, `updated_at`) VALUES
(1, 1, 1, NULL, 1156.00, 5.00, 1213.80, '2025-08-26 00:17:06', '2025-08-26 00:17:06'),
(2, 2, 2, 1, 529.00, 3.00, 544.87, '2025-08-26 00:31:37', '2025-08-26 00:31:37'),
(3, 3, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-26 04:58:06', '2025-08-26 04:58:06'),
(4, 4, 2, 1, 529.00, 2.00, 539.58, '2025-08-26 04:58:36', '2025-08-26 04:58:36'),
(5, 5, 1, NULL, 1156.00, 3.00, 1190.68, '2025-08-26 05:05:14', '2025-08-26 05:05:14'),
(7, 7, 1, NULL, 1156.00, 4.00, 1202.24, '2025-08-26 05:27:49', '2025-08-26 05:27:49'),
(8, 8, 1, NULL, 1156.00, 5.00, 1213.80, '2025-08-26 06:02:15', '2025-08-26 06:02:15'),
(9, 9, 1, NULL, 1156.00, 5.00, 1213.80, '2025-08-26 06:02:35', '2025-08-26 06:02:35'),
(10, 9, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-26 06:02:35', '2025-08-26 06:02:35'),
(11, 11, 2, 1, 529.00, 3.00, 544.87, '2025-08-26 06:20:36', '2025-08-26 06:20:36'),
(12, 12, 1, NULL, 1156.00, 3.00, 1190.68, '2025-08-26 06:41:14', '2025-08-26 06:41:14'),
(13, 13, 1, NULL, 1156.00, 4.00, 1202.24, '2025-08-26 06:53:39', '2025-08-26 06:53:39'),
(14, 14, 1, NULL, 1156.00, 1.99, 1179.00, '2025-08-26 06:59:27', '2025-08-26 06:59:27'),
(15, 15, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-26 07:15:41', '2025-08-26 07:15:41'),
(16, 15, 2, 1, 529.00, 21.00, 640.09, '2025-08-26 07:15:41', '2025-08-26 07:15:41'),
(19, 16, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-26 22:49:40', '2025-08-26 22:49:40'),
(20, 17, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-26 22:51:27', '2025-08-26 22:51:27'),
(21, 17, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-26 22:51:27', '2025-08-26 22:51:27'),
(22, 18, 1, NULL, 1156.00, 1.99, 1179.00, '2025-08-26 23:01:05', '2025-08-26 23:01:05'),
(23, 21, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-26 23:12:41', '2025-08-26 23:12:41'),
(28, 23, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-26 23:25:20', '2025-08-26 23:25:20'),
(39, 24, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-27 01:05:46', '2025-08-27 01:05:46'),
(40, 24, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-27 01:05:46', '2025-08-27 01:05:46'),
(41, 25, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-27 01:35:46', '2025-08-27 01:35:46'),
(42, 26, 1, NULL, 1156.00, 3.00, 1190.68, '2025-08-27 01:58:43', '2025-08-27 01:58:43'),
(44, 27, 1, NULL, 1156.00, 23.00, 1421.88, '2025-08-27 02:05:49', '2025-08-27 02:05:49'),
(45, 28, 1, NULL, 1156.00, 2.99, 1190.56, '2025-08-27 03:47:56', '2025-08-27 03:47:56'),
(46, 29, 1, NULL, 1156.00, 2.00, 1179.12, '2025-08-27 04:15:15', '2025-08-27 04:15:15'),
(47, 30, 1, NULL, 1156.00, 5.00, 1213.80, '2025-08-27 23:04:06', '2025-08-27 23:04:06'),
(48, 31, 1, NULL, 1156.00, 5.00, 1213.80, '2025-08-28 03:52:24', '2025-08-28 03:52:24'),
(49, 32, 1, NULL, 1156.00, 5.00, 1213.80, '2025-09-03 23:40:48', '2025-09-03 23:40:48');

-- --------------------------------------------------------

--
-- Table structure for table `property_services`
--

CREATE TABLE `property_services` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `icon` varchar(255) DEFAULT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` enum('1','0') DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `property_services`
--

INSERT INTO `property_services` (`id`, `icon`, `name`, `description`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, '/propertyServices/icon/Rent.svg', 'Rent', 'Property available for rental purposes.', '1', 2, '2025-08-13 23:59:39', '2025-08-14 01:44:56'),
(2, '/propertyServices/icon/Sell.svg', 'Sell', 'Property available for sale.', '1', 1, '2025-08-13 23:59:39', '2025-08-13 23:59:39'),
(3, '/propertyServices/icon/Lease.svg', 'Lease', 'Property available for long-term lease agreements.', '1', 3, '2025-08-13 23:59:39', '2025-08-13 23:59:39'),
(8, '/propertyServices/icon/Others.svg', 'Others', 'Other Facilities', '1', 4, '2025-08-19 03:30:37', '2025-08-19 06:33:42');

-- --------------------------------------------------------

--
-- Table structure for table `property_stages`
--

CREATE TABLE `property_stages` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` tinyint(1) NOT NULL DEFAULT 1,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `property_stages`
--

INSERT INTO `property_stages` (`id`, `name`, `description`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(19, 'New', 'Newly Developed', 1, 0, '2025-08-13 03:38:27', '2025-08-13 03:38:27'),
(20, 'Used', 'Properly Renovated', 1, 0, '2025-08-13 03:38:42', '2025-08-13 03:38:42');

-- --------------------------------------------------------

--
-- Table structure for table `property_statuses`
--

CREATE TABLE `property_statuses` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(100) NOT NULL,
  `slug` varchar(100) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` enum('1','0') DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `property_statuses`
--

INSERT INTO `property_statuses` (`id`, `name`, `slug`, `description`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'Planning', 'planning', 'Property is in planning phase', '1', 1, '2025-08-08 02:35:31', '2025-08-08 02:35:31'),
(2, 'Under Construction', 'under_construction', 'Property is currently under construction', '1', 2, '2025-08-08 02:35:31', '2025-08-08 02:35:31'),
(3, 'Completed', 'completed', 'Property construction is completed', '1', 3, '2025-08-08 02:35:31', '2025-08-08 02:35:31'),
(4, 'Sold', 'sold', 'Property has been sold', '1', 4, '2025-08-08 02:35:31', '2025-08-08 02:35:31'),
(5, 'Rented', 'rented', 'Property is currently rented', '1', 5, '2025-08-08 02:35:31', '2025-08-08 02:35:31'),
(6, 'Under Maintenance', 'maintenance', 'Property is under maintenance', '1', 6, '2025-08-08 02:35:31', '2025-08-08 02:35:31');

-- --------------------------------------------------------

--
-- Table structure for table `property_types`
--

CREATE TABLE `property_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `base_price_multiplier` decimal(5,2) NOT NULL DEFAULT 1.00,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL,
  `is_active` int(11) NOT NULL DEFAULT 1
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `property_types`
--

INSERT INTO `property_types` (`id`, `name`, `slug`, `description`, `base_price_multiplier`, `sort_order`, `created_at`, `updated_at`, `is_active`) VALUES
(2, 'Commercial', 'commercial', 'Properties used for business purposes including offices, retail spaces, and warehouses.', 1.50, 2, '2025-08-07 00:34:02', '2025-09-03 06:14:07', 0),
(3, 'Industrial', 'industrial', 'Properties designed for manufacturing, production, and heavy industrial activities.', 1.75, 3, '2025-08-07 00:34:03', '2025-08-07 00:34:03', 1),
(4, 'Mixed Use', 'mixed-use', 'Properties that combine residential, commercial, and other uses in a single development.', 1.25, 4, '2025-08-07 00:34:03', '2025-08-07 00:34:03', 1),
(5, 'Land', 'land', 'Undeveloped land parcels suitable for various development purposes.', 0.75, 5, '2025-08-07 00:34:03', '2025-08-07 00:34:03', 1),
(6, 'Retail', 'retail', 'Properties specifically designed for retail businesses and shopping centers.', 1.40, 6, '2025-08-07 00:34:03', '2025-08-07 00:34:03', 1),
(7, 'Hospitality', 'hospitality', 'Properties for hotels, resorts, and other hospitality-related businesses.', 2.00, 7, '2025-08-07 00:34:03', '2025-08-07 00:34:03', 1);

-- --------------------------------------------------------

--
-- Table structure for table `rent_types`
--

CREATE TABLE `rent_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` enum('1','0') DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `rent_types`
--

INSERT INTO `rent_types` (`id`, `name`, `description`, `is_active`, `sort_order`, `created_at`, `updated_at`) VALUES
(1, 'Hourly', 'Rent charged per hour', '1', 1, '2025-08-14 06:02:52', '2025-08-14 06:02:52'),
(2, 'Daily', 'Rent charged per day', '1', 2, '2025-08-14 06:02:52', '2025-08-14 06:02:52'),
(3, 'Weekly', 'Rent charged per week', '1', 3, '2025-08-14 06:02:52', '2025-08-14 06:02:52'),
(4, 'Monthly', 'Rent charged per month', '1', 4, '2025-08-14 06:02:52', '2025-08-14 06:02:52'),
(5, 'Yearly', 'Rent charged per year', '1', 5, '2025-08-14 06:02:52', '2025-09-01 00:22:42'),
(6, 'fdgsd', 'sfgsd', NULL, 0, '2025-09-03 00:06:00', '2025-09-03 00:06:00'),
(7, 'hjkh', 'hjkh', '1', 0, '2025-09-03 00:50:48', '2025-09-03 00:50:48');

-- --------------------------------------------------------

--
-- Table structure for table `roles`
--

CREATE TABLE `roles` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `accessible_modules` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`accessible_modules`)),
  `module_permissions` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL CHECK (json_valid(`module_permissions`)),
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `users_count` int(11) NOT NULL DEFAULT 0,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `roles`
--

INSERT INTO `roles` (`id`, `name`, `description`, `accessible_modules`, `module_permissions`, `status`, `users_count`, `created_at`, `updated_at`) VALUES
(1, 'Super Admin', 'Full system access with all permissions', '[\"dashboard\",\"analytics\",\"landowners\",\"land-owners\",\"land-acquisition\",\"project\",\"lifecycle\",\"employees\",\"contractors\",\"assign-vendor\",\"assign-employee\",\"vendor-type\",\"vendor\",\"property-amenity\",\"customer\",\"orders\",\"components\",\"reports\",\"role\",\"word-assistant\",\"settings\",\"country\",\"state\",\"city\",\"location\",\"language\",\"currency\",\"property-type\",\"property-status\",\"unit-type\",\"property-stage\",\"tanant\",\"tenants\",\"property-service\",\"rent-type\",\"lease-type\",\"invoice\",\"payment-type\",\"payment-status\",\"payment-method\",\"assign-contractor\",\"all-types\",\"menu\",\"pages\",\"widgets\",\"frontend\",\"backend\",\"blog\",\"menu-manage\"]', '{\"dashboard\":[\"read\"],\"analytics\":[\"read\"],\"landowners\":[\"create\",\"read\",\"update\",\"delete\"],\"land-owners\":[\"create\",\"read\",\"update\",\"delete\"],\"land-acquisition\":[\"create\",\"read\",\"update\",\"delete\"],\"project\":[\"create\",\"read\",\"update\",\"delete\"],\"lifecycle\":[\"create\",\"read\",\"update\",\"delete\"],\"employees\":[\"create\",\"read\",\"update\",\"delete\"],\"contractors\":[\"create\",\"read\",\"update\",\"delete\"],\"assign-vendor\":[\"create\",\"read\",\"update\",\"delete\"],\"assign-employee\":[\"create\",\"read\",\"update\",\"delete\"],\"vendor-type\":[\"create\",\"read\",\"update\",\"delete\"],\"vendor\":[\"create\",\"read\",\"update\",\"delete\"],\"property-amenity\":[\"create\",\"read\",\"update\",\"delete\"],\"customer\":[\"create\",\"read\",\"update\",\"delete\"],\"orders\":[\"create\",\"read\",\"update\",\"delete\"],\"components\":[\"read\"],\"reports\":[\"read\",\"export\"],\"role\":[\"create\",\"read\",\"update\",\"delete\"],\"word-assistant\":[\"read\",\"use\"],\"settings\":[\"read\",\"update\"],\"country\":[\"create\",\"read\",\"update\",\"delete\"],\"state\":[\"create\",\"read\",\"update\",\"delete\"],\"city\":[\"create\",\"read\",\"update\",\"delete\"],\"location\":[\"create\",\"read\",\"update\",\"delete\"],\"language\":[\"create\",\"read\",\"update\",\"delete\"],\"currency\":[\"create\",\"read\",\"update\",\"delete\"],\"property-type\":[\"create\",\"read\",\"update\",\"delete\"],\"property-status\":[\"create\",\"read\",\"update\",\"delete\"],\"unit-type\":[\"create\",\"read\",\"update\",\"delete\"],\"property-stage\":[\"create\",\"read\",\"update\",\"delete\"],\"tanant\":[\"create\",\"read\",\"update\",\"delete\"],\"tenants\":[\"create\",\"delete\",\"read\",\"update\"],\"property-service\":[\"create\",\"delete\",\"read\",\"update\"],\"rent-type\":[\"create\",\"delete\",\"read\",\"update\"],\"lease-type\":[\"create\",\"delete\",\"read\",\"update\"],\"invoice\":[\"create\",\"delete\",\"read\",\"update\"],\"payment-type\":[\"create\",\"delete\",\"read\",\"update\"],\"payment-status\":[\"create\",\"delete\",\"read\",\"update\"],\"payment-method\":[\"create\",\"delete\",\"read\",\"update\"],\"assign-contractor\":[\"create\",\"delete\",\"read\",\"update\"],\"all-types\":[\"create\",\"delete\",\"read\",\"update\"],\"menu\":[\"create\",\"delete\",\"read\",\"update\"],\"pages\":[\"create\",\"delete\",\"read\",\"update\"],\"widgets\":[\"create\",\"delete\",\"read\",\"update\"],\"frontend\":[\"create\",\"delete\",\"read\",\"update\"],\"backend\":[\"create\",\"delete\",\"read\",\"update\"],\"blog\":[\"create\",\"delete\",\"read\",\"update\"],\"menu-manage\":[\"create\",\"delete\",\"read\",\"update\"]}', 'active', 2, '2025-07-18 04:46:23', '2025-10-06 00:20:04'),
(2, 'Admin', 'Administrative access with limited system settings', '[\"dashboard\",\"analytics\",\"landowners\",\"land-owners\",\"land-acquisition\",\"project\",\"lifecycle\",\"employees\",\"contractors\",\"assign-contractor\",\"assign-vendor\",\"assign-employee\",\"vendor-type\",\"vendor\",\"property-amenity\",\"customer\",\"orders\",\"components\",\"reports\",\"role\",\"settings\",\"country\",\"state\",\"city\",\"location\",\"language\",\"currency\"]', '{\"dashboard\":[\"read\"],\"analytics\":[\"read\"],\"landowners\":[\"create\",\"read\",\"update\",\"delete\"],\"land-owners\":[\"create\",\"read\",\"update\",\"delete\"],\"land-acquisition\":[\"create\",\"read\",\"update\",\"delete\"],\"project\":[\"create\",\"read\",\"update\",\"delete\"],\"lifecycle\":[\"read\",\"update\"],\"employees\":[\"create\",\"read\",\"update\",\"delete\"],\"contractors\":[\"create\",\"read\",\"update\",\"delete\"],\"assign-contractor\":[\"create\",\"read\",\"update\"],\"assign-vendor\":[\"create\",\"read\",\"update\"],\"assign-employee\":[\"create\",\"read\",\"update\"],\"vendor-type\":[\"create\",\"read\",\"update\",\"delete\"],\"vendor\":[\"create\",\"read\",\"update\",\"delete\"],\"property-amenity\":[\"create\",\"read\",\"update\",\"delete\"],\"customer\":[\"create\",\"read\",\"update\"],\"orders\":[\"read\",\"update\"],\"components\":[\"read\"],\"reports\":[\"read\",\"export\"],\"role\":[\"read\"],\"settings\":[\"read\"],\"country\":[\"create\",\"read\",\"update\",\"delete\"],\"state\":[\"create\",\"read\",\"update\",\"delete\"],\"city\":[\"create\",\"read\",\"update\",\"delete\"],\"location\":[\"create\",\"read\",\"update\",\"delete\"],\"language\":[\"create\",\"read\",\"update\",\"delete\"],\"currency\":[\"create\",\"read\",\"update\",\"delete\"]}', 'active', 5, '2025-07-18 04:46:23', '2025-08-12 02:34:20'),
(3, 'Manager', 'Management level access to land and owner records', '[\"dashboard\",\"analytics\",\"landowners\",\"land-owners\",\"land-acquisition\",\"project\",\"lifecycle\",\"employees\",\"contractors\",\"vendor-type\",\"vendor\",\"property-amenity\",\"customer\",\"reports\",\"country\",\"state\",\"city\",\"location\",\"language\",\"currency\"]', '{\"dashboard\":[\"read\"],\"analytics\":[\"read\"],\"landowners\":[\"create\",\"read\",\"update\"],\"land-owners\":[\"create\",\"read\",\"update\"],\"land-acquisition\":[\"create\",\"read\",\"update\"],\"project\":[\"create\",\"read\",\"update\"],\"lifecycle\":[\"read\"],\"employees\":[\"create\",\"read\",\"update\"],\"contractors\":[\"create\",\"read\",\"update\"],\"vendor-type\":[\"create\",\"read\",\"update\"],\"vendor\":[\"create\",\"read\",\"update\"],\"property-amenity\":[\"create\",\"read\",\"update\"],\"customer\":[\"read\",\"update\"],\"reports\":[\"read\"],\"country\":[\"create\",\"read\",\"update\"],\"state\":[\"create\",\"read\",\"update\"],\"city\":[\"create\",\"read\",\"update\"],\"location\":[\"create\",\"read\",\"update\"],\"language\":[\"create\",\"read\",\"update\"],\"currency\":[\"create\",\"read\",\"update\"]}', 'active', 8, '2025-07-18 04:46:23', '2025-08-12 02:34:20'),
(4, 'Editor', 'Can create and edit records but not delete', '[\"dashboard\",\"land-owners\",\"land-acquisition\",\"customer\",\"orders\"]', '{\"dashboard\":[\"read\"],\"land-owners\":[\"create\",\"read\",\"update\"],\"land-acquisition\":[\"create\",\"read\",\"update\"],\"customer\":[\"create\",\"read\",\"update\"],\"orders\":[\"create\",\"read\",\"update\"]}', 'active', 12, '2025-07-18 04:46:23', '2025-08-12 02:34:20'),
(5, 'Viewer', 'Read-only access to view records and reports', '[\"dashboard\",\"analytics\",\"land-owners\",\"land-acquisition\",\"customer\",\"reports\"]', '{\"dashboard\":[\"read\"],\"analytics\":[\"read\"],\"land-owners\":[\"read\"],\"land-acquisition\":[\"read\"],\"customer\":[\"read\"],\"reports\":[\"read\"]}', 'active', 25, '2025-07-18 04:46:23', '2025-08-12 02:34:20'),
(6, 'Guest', 'Limited read access to basic information', '[\"dashboard\"]', '{\"dashboard\":[\"read\"]}', 'inactive', 3, '2025-07-18 04:46:23', '2025-08-12 02:34:20');

-- --------------------------------------------------------

--
-- Table structure for table `sessions`
--

CREATE TABLE `sessions` (
  `id` varchar(255) NOT NULL,
  `user_id` bigint(20) UNSIGNED DEFAULT NULL,
  `ip_address` varchar(45) DEFAULT NULL,
  `user_agent` text DEFAULT NULL,
  `payload` longtext NOT NULL,
  `last_activity` int(11) NOT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `sessions`
--

INSERT INTO `sessions` (`id`, `user_id`, `ip_address`, `user_agent`, `payload`, `last_activity`) VALUES
('MZPSESfXMX2dvPtDxGcEU1YNpflYKwXD0GF9nJP9', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiNXFwTFc0ckI1OUZqb1ZJZDJRcnY5OVJadVJ0c0h3MEZSU0xzeGZGSiI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6MzY6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9wcm9wZXJ0eSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1759497814),
('oyoqy2V26p8MhfSkXBWhavUSquekElw8xLPbLE8A', NULL, '127.0.0.1', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36', 'YTozOntzOjY6Il90b2tlbiI7czo0MDoiOFZzWHFIVjlrZDlSd1VaaDRpb3A1WG5IeUVCaFhSQ3ZxT2VHVU55ZyI7czo5OiJfcHJldmlvdXMiO2E6MTp7czozOiJ1cmwiO3M6Mzk6Imh0dHA6Ly9sb2NhbGhvc3Q6ODAwMC9hZG1pbi9tZW51LW1hbmFnZSI7fXM6NjoiX2ZsYXNoIjthOjI6e3M6Mzoib2xkIjthOjA6e31zOjM6Im5ldyI7YTowOnt9fX0=', 1759743678);

-- --------------------------------------------------------

--
-- Table structure for table `states`
--

CREATE TABLE `states` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `image` varchar(255) DEFAULT NULL,
  `country_id` bigint(20) UNSIGNED NOT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `states`
--

INSERT INTO `states` (`id`, `name`, `image`, `country_id`, `status`, `created_at`, `updated_at`) VALUES
(1, 'Dhaka', '', 23, 'active', '2025-07-28 02:36:21', '2025-07-28 02:36:21'),
(2, 'California', '', 235, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(3, 'Texas', '', 235, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(4, 'New York', '', 235, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(5, 'Florida', '', 235, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(6, 'Ontario', '', 40, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(7, 'Quebec', '', 40, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(8, 'British Columbia', '1759726317_68e34aed57733.png', 40, 'active', '2025-08-05 06:12:42', '2025-10-05 22:51:57'),
(9, 'New South Wales', '', 15, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(10, 'Victoria', '', 15, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(11, 'Queensland', '', 15, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(12, 'Maharashtra', '', 105, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(13, 'Karnataka', '', 105, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(14, 'England', '', 80, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(15, 'Scotland', '', 80, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(16, 'Metro Manila', '', 177, 'active', '2025-08-05 06:12:42', '2025-08-05 06:12:42'),
(17, 'Chittagong', '', 23, 'active', '2025-08-05 06:19:35', '2025-08-05 06:19:35'),
(18, 'Rajshahi', '', 23, 'active', '2025-08-05 06:19:35', '2025-08-05 06:19:35'),
(19, 'Khulna', '', 23, 'active', '2025-08-05 06:19:35', '2025-08-05 06:19:35'),
(20, 'Barisal', '1759494557_68dfc19d50163.png', 23, 'active', '2025-08-05 06:19:35', '2025-10-03 06:29:17'),
(21, 'Sylhet', '', 23, 'active', '2025-08-05 06:19:35', '2025-08-05 06:19:35'),
(22, 'Rangpur', '', 23, 'active', '2025-08-05 06:19:35', '2025-08-05 06:19:35'),
(23, 'Mymensingh', '', 23, 'active', '2025-08-05 06:19:35', '2025-08-05 06:19:35'),
(32, 'adfadf', '1759494104_68dfbfd8ac828.png', 23, 'active', '2025-10-03 06:21:44', '2025-10-03 06:21:44');

-- --------------------------------------------------------

--
-- Table structure for table `tanants`
--

CREATE TABLE `tanants` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `photo` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `previous_address` text DEFAULT NULL,
  `permanent_address` text DEFAULT NULL,
  `note` text DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `tanants`
--

INSERT INTO `tanants` (`id`, `first_name`, `last_name`, `photo`, `email`, `phone`, `previous_address`, `permanent_address`, `note`, `status`, `created_at`, `updated_at`) VALUES
(5, 'Shifat E', 'Rasul', NULL, '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', '51,Arjotpara,Mohakhali Dhaka', 'adfads', 'active', '2025-08-15 04:47:25', '2025-08-15 05:37:20'),
(6, 'Shifat E', 'Rasul', 'tenant_photos/Ncp0PWTR3iRzmPE9Zm5gvWPPYEQCKyqiFVaiB4xf.jpg', '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', '51,Arjotpara,Mohakhali Dhaka', NULL, 'active', '2025-08-15 05:15:21', '2025-08-15 05:15:21'),
(7, 'Shifat E', 'Rasul', 'tenant_photos/T62lD0BnhBpezc0ssFe1clfckYTQPiCQtXJBqRnc.jpg', '<EMAIL>', '01871769835', '51,Arjotpara,Mohakhali Dhaka', '51,Arjotpara,Mohakhali Dhaka', NULL, 'active', '2025-08-15 05:16:27', '2025-08-15 05:16:27');

-- --------------------------------------------------------

--
-- Table structure for table `tenants_documents`
--

CREATE TABLE `tenants_documents` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tenant_id` bigint(20) UNSIGNED NOT NULL,
  `title` varchar(255) NOT NULL,
  `file` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `tenants_documents`
--

INSERT INTO `tenants_documents` (`id`, `tenant_id`, `title`, `file`, `created_at`, `updated_at`) VALUES
(1, 8, 'adfasdfadsf', 'tenant_documents/ZzWbBXQwCWOUNLYDebQECIGLVaiFBvReB4CBv7f6.jpg', '2025-08-15 05:17:49', '2025-08-15 05:17:49');

-- --------------------------------------------------------

--
-- Table structure for table `tenant_emergency_contacts`
--

CREATE TABLE `tenant_emergency_contacts` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `tenant_id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `relation` varchar(255) NOT NULL,
  `phone` varchar(255) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `tenant_emergency_contacts`
--

INSERT INTO `tenant_emergency_contacts` (`id`, `tenant_id`, `name`, `relation`, `phone`, `created_at`, `updated_at`) VALUES
(1, 4, 'Shifat E Rasul', 'afadsf', '01871769835', '2025-08-15 04:46:31', '2025-08-15 04:46:31'),
(2, 6, 'Merchant Test', 'adsd', '01811111111', '2025-08-15 05:15:21', '2025-08-15 05:15:21'),
(3, 8, 'Merchant Test', 'adsd', '01811111111', '2025-08-15 05:17:49', '2025-08-15 05:17:49'),
(4, 5, '', '', '', '2025-08-15 05:37:13', '2025-08-15 05:37:13');

-- --------------------------------------------------------

--
-- Table structure for table `unit_types`
--

CREATE TABLE `unit_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `slug` varchar(255) NOT NULL,
  `description` text DEFAULT NULL,
  `is_active` enum('1','0') DEFAULT NULL,
  `sort_order` int(11) NOT NULL DEFAULT 0,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `unit_types`
--

INSERT INTO `unit_types` (`id`, `name`, `slug`, `description`, `is_active`, `sort_order`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Studio', 'studio', 'Studio apartment', '1', 1, NULL, NULL, '2025-08-12 04:06:56', '2025-08-31 23:14:45'),
(2, '1 BHK', '1-bhk', '1 Bedroom Hall Kitchen', '1', 2, NULL, NULL, '2025-08-12 04:07:01', '2025-08-12 04:07:01'),
(3, '2 BHK', '2-bhk', '2 Bedroom Hall Kitchen', '1', 3, NULL, NULL, '2025-08-12 04:07:06', '2025-08-12 04:07:06'),
(4, '3 BHK', '3-bhk', '3 Bedroom Hall Kitchen', '1', 4, NULL, NULL, '2025-08-12 04:07:11', '2025-08-12 04:07:11'),
(5, 'Penthouse', 'penthouse', 'Luxury penthouse unit', '1', 5, NULL, NULL, '2025-08-12 04:07:16', '2025-08-12 05:15:48'),
(14, 'hgjhgjh', 'hgjhgjh', 'hjkhjkh', '1', 0, NULL, NULL, '2025-09-03 06:44:53', '2025-09-03 06:44:53');

-- --------------------------------------------------------

--
-- Table structure for table `users`
--

CREATE TABLE `users` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `first_name` varchar(255) DEFAULT NULL,
  `last_name` varchar(255) DEFAULT NULL,
  `email` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `company` varchar(255) DEFAULT NULL,
  `bio` text DEFAULT NULL,
  `timezone` varchar(255) NOT NULL DEFAULT 'UTC',
  `language` varchar(255) NOT NULL DEFAULT 'English',
  `role_id` bigint(20) UNSIGNED DEFAULT NULL,
  `email_verified_at` timestamp NULL DEFAULT NULL,
  `password` varchar(255) NOT NULL,
  `remember_token` varchar(100) DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `users`
--

INSERT INTO `users` (`id`, `name`, `first_name`, `last_name`, `email`, `phone`, `company`, `bio`, `timezone`, `language`, `role_id`, `email_verified_at`, `password`, `remember_token`, `created_at`, `updated_at`) VALUES
(1, 'Super Admin', 'Super', 'Admin', '<EMAIL>', '******-0001', 'Real Estate Management System', 'System super administrator with full access.', 'America/New_York', 'en', 1, '2025-07-18 04:46:23', '$2y$12$3Tln9zHkWokykBWR.kaGAucPlYVwykk1qD8ns6nBPSLZQMpugbrWK', NULL, '2025-07-18 04:46:25', '2025-09-21 23:00:36'),
(2, 'Admin User', 'Admin', 'User', '<EMAIL>', '******-0002', 'Real Estate Management System', 'System administrator for daily operations.', 'America/New_York', 'en', 2, '2025-07-18 04:46:24', '$2y$12$RqsbJ9kC6o9hM.SXSXRK/OG.b5pzZE5W5EiBhrAJ29Fo8PTXh3duq', NULL, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(3, 'Manager User', 'Manager', 'User', '<EMAIL>', '******-0003', 'Real Estate Group', 'Land acquisition manager.', 'America/Los_Angeles', 'en', 3, '2025-07-18 04:46:24', '$2y$12$.92cy3KpSaeZbzZlgNNYd.ZZNH6AuS1vB89UichvJ3i.zpp5qdYDS', NULL, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(4, 'Editor User', 'Editor', 'User', '<EMAIL>', '******-0004', 'Real Estate Group', 'Content editor and documentation specialist.', 'America/Chicago', 'en', 4, '2025-07-18 04:46:24', '$2y$12$l4Faps5YKtyk4Y/y90yzZeW.sRAUzdkZY97GRw6KCBRm9ykVMG5Fq', NULL, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(5, 'Viewer User', 'Viewer', 'User', '<EMAIL>', '******-0005', 'Real Estate Group', 'Read-only access user for reports and viewing.', 'America/Denver', 'en', 5, '2025-07-18 04:46:24', '$2y$12$9Xf4v2on80T5J8M2rkeLl.ojm4WMNm9pOlVkbuGkMPHRIMFO2A7Ii', NULL, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(6, 'Demo User', 'Demo', 'User', '<EMAIL>', '******-0006', 'Demo Real Estate', 'Demo account for testing and presentations.', 'America/Los_Angeles', 'en', 3, '2025-07-18 04:46:25', '$2y$12$G0j6LIJ8IwhPx2xFgE02nOmcKj3ZqiB5EABqCx1ziQb1VgCxqbBk.', NULL, '2025-07-18 04:46:25', '2025-07-18 04:46:25'),
(7, 'Test User', NULL, NULL, '<EMAIL>', NULL, NULL, NULL, 'UTC', 'English', 1, NULL, '$2y$12$ht5maPFoxDjsSyP.fC4XLuLzV3ANlSyQCMUFkeMVAYycmk8qC8uKS', NULL, '2025-07-27 23:50:35', '2025-07-27 23:50:35'),
(8, 'Admin User', 'Admin', 'User', '<EMAIL>', NULL, NULL, NULL, 'UTC', 'English', 1, NULL, '$2y$12$TXeD2qB765BoZrZWhh6akeopBBPeEPKPkXslkVDUKz6s/8arhNwyS', NULL, '2025-07-30 01:47:36', '2025-07-30 01:47:36');

-- --------------------------------------------------------

--
-- Table structure for table `vendors`
--

CREATE TABLE `vendors` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `phone` varchar(255) DEFAULT NULL,
  `email` varchar(255) DEFAULT NULL,
  `vendor_type_id` bigint(20) UNSIGNED NOT NULL,
  `address` text DEFAULT NULL,
  `status` enum('active','inactive') NOT NULL DEFAULT 'active',
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `vendors`
--

INSERT INTO `vendors` (`id`, `name`, `phone`, `email`, `vendor_type_id`, `address`, `status`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(1, 'Shifat E Rasul', '01871769835', '<EMAIL>', 2, '51,Arjotpara,Mohakhali Dhaka', 'active', 1, 1, '2025-07-28 22:55:08', '2025-09-05 05:50:43'),
(2, 'afasdf', '01871769835', '<EMAIL>', 2, '51,Arjotpara,Mohakhali Dhaka', 'active', 1, 1, '2025-08-28 06:58:15', '2025-09-05 05:50:43');

-- --------------------------------------------------------

--
-- Table structure for table `vendor_types`
--

CREATE TABLE `vendor_types` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `name` varchar(255) NOT NULL,
  `is_active` enum('1','0') NOT NULL,
  `created_by` bigint(20) UNSIGNED DEFAULT NULL,
  `updated_by` bigint(20) UNSIGNED DEFAULT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `vendor_types`
--

INSERT INTO `vendor_types` (`id`, `name`, `is_active`, `created_by`, `updated_by`, `created_at`, `updated_at`) VALUES
(2, 'Cement', '1', 1, 1, '2025-07-28 22:54:38', '2025-07-28 22:54:38'),
(8, 'new', '1', 1, 1, '2025-09-05 02:17:46', '2025-09-05 02:17:46');

-- --------------------------------------------------------

--
-- Table structure for table `widgets`
--

CREATE TABLE `widgets` (
  `id` bigint(20) UNSIGNED NOT NULL,
  `image` varchar(255) NOT NULL,
  `name` varchar(255) NOT NULL,
  `is_active` int(11) NOT NULL,
  `created_at` timestamp NULL DEFAULT NULL,
  `updated_at` timestamp NULL DEFAULT NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

--
-- Dumping data for table `widgets`
--

INSERT INTO `widgets` (`id`, `image`, `name`, `is_active`, `created_at`, `updated_at`) VALUES
(1, '/widget/slider.svg', 'Slider', 1, NULL, NULL),
(2, '/widget/search.svg', 'Search', 1, NULL, NULL),
(3, '/widget/subNavigation.svg', 'Sub Navigation', 0, NULL, NULL),
(4, '/widget/aboutUs.svg', 'About Us', 1, NULL, NULL),
(5, '/widget/property.svg', 'Property Type', 1, NULL, NULL),
(6, '/widget/propertyByCity.svg', 'Property By City', 1, NULL, NULL),
(7, '/widget/whyChooseUs.svg', 'Why Choose Us', 1, NULL, NULL),
(8, '/widget/featuredProperty.svg', 'Feature Property', 1, NULL, NULL),
(9, '/widget/howItWorks.svg', 'How it Works', 1, NULL, NULL),
(10, '/widget/recentProperty.svg', 'Recent Property', 1, NULL, NULL),
(11, '/widget/advertisement.svg', 'Advertisement', 1, NULL, NULL),
(12, '/widget/testimonial.svg', 'Testimonial', 1, NULL, NULL),
(13, '/widget/partners.svg', 'Trusted Partners', 1, NULL, NULL),
(14, '/widget/blog.svg', 'Blog\n', 1, NULL, NULL);

--
-- Indexes for dumped tables
--

--
-- Indexes for table `backend_settings`
--
ALTER TABLE `backend_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `blogs`
--
ALTER TABLE `blogs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `blogs_slug_unique` (`slug`),
  ADD KEY `blogs_status_published_at_index` (`status`,`published_at`),
  ADD KEY `blogs_category_id_status_index` (`category_id`,`status`),
  ADD KEY `blogs_is_featured_status_index` (`is_featured`,`status`),
  ADD KEY `blogs_slug_index` (`slug`);

--
-- Indexes for table `cache`
--
ALTER TABLE `cache`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `cache_locks`
--
ALTER TABLE `cache_locks`
  ADD PRIMARY KEY (`key`);

--
-- Indexes for table `commission_agents`
--
ALTER TABLE `commission_agents`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `contractors`
--
ALTER TABLE `contractors`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `contractors_phone_unique` (`phone`),
  ADD UNIQUE KEY `contractors_trade_license_unique` (`trade_license`),
  ADD UNIQUE KEY `contractors_email_unique` (`email`),
  ADD KEY `contractors_created_by_foreign` (`created_by`),
  ADD KEY `contractors_updated_by_foreign` (`updated_by`);

--
-- Indexes for table `countries`
--
ALTER TABLE `countries`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `countries_name_unique` (`name`),
  ADD UNIQUE KEY `countries_code_unique` (`code`),
  ADD UNIQUE KEY `countries_iso_code_unique` (`iso_code`),
  ADD KEY `countries_status_index` (`status`),
  ADD KEY `countries_continent_index` (`continent`);

--
-- Indexes for table `currencies`
--
ALTER TABLE `currencies`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `currencies_code_unique` (`code`);

--
-- Indexes for table `customers`
--
ALTER TABLE `customers`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `customers_email_unique` (`email`);

--
-- Indexes for table `development_costings`
--
ALTER TABLE `development_costings`
  ADD PRIMARY KEY (`id`),
  ADD KEY `development_costings_created_by_foreign` (`created_by`),
  ADD KEY `development_costings_updated_by_foreign` (`updated_by`),
  ADD KEY `development_costings_approved_by_foreign` (`approved_by`),
  ADD KEY `development_costings_project_id_cost_category_index` (`project_id`,`cost_category`),
  ADD KEY `development_costings_cost_date_index` (`cost_date`),
  ADD KEY `development_costings_payment_status_index` (`payment_status`),
  ADD KEY `development_costings_approval_status_index` (`approval_status`),
  ADD KEY `development_costings_due_date_index` (`due_date`);

--
-- Indexes for table `employees`
--
ALTER TABLE `employees`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `employees_phone_unique` (`phone`),
  ADD UNIQUE KEY `employees_email_unique` (`email`),
  ADD KEY `employees_created_by_foreign` (`created_by`),
  ADD KEY `employees_updated_by_foreign` (`updated_by`);

--
-- Indexes for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `failed_jobs_uuid_unique` (`uuid`);

--
-- Indexes for table `frontend_settings`
--
ALTER TABLE `frontend_settings`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `invoices`
--
ALTER TABLE `invoices`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `invoice_items`
--
ALTER TABLE `invoice_items`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `jobs`
--
ALTER TABLE `jobs`
  ADD PRIMARY KEY (`id`),
  ADD KEY `jobs_queue_index` (`queue`);

--
-- Indexes for table `job_batches`
--
ALTER TABLE `job_batches`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `land_acquisitions`
--
ALTER TABLE `land_acquisitions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `land_acquisitions_landowners_id_index` (`land_owner_id`),
  ADD KEY `land_acquisitions_mouza_index` (`mauza`),
  ADD KEY `land_acquisitions_khatian_number_index` (`khatian_number`),
  ADD KEY `land_acquisitions_dag_number_index` (`dag_number`),
  ADD KEY `land_acquisitions_status_index` (`status`),
  ADD KEY `land_acquisitions_created_by_index` (`created_by`),
  ADD KEY `land_acquisitions_updated_by_index` (`updated_by`);

--
-- Indexes for table `land_addresses`
--
ALTER TABLE `land_addresses`
  ADD PRIMARY KEY (`id`),
  ADD KEY `land_addresses_land_acquisition_id_index` (`land_acquisition_id`),
  ADD KEY `land_addresses_address_type_index` (`address_type`),
  ADD KEY `land_addresses_latitude_longitude_index` (`latitude`,`longitude`),
  ADD KEY `land_addresses_district_index` (`district`),
  ADD KEY `land_addresses_upazila_thana_index` (`upazila_thana`),
  ADD KEY `land_addresses_is_verified_index` (`is_verified`),
  ADD KEY `land_addresses_status_index` (`status`),
  ADD KEY `land_addresses_country_id_foreign` (`country_id`),
  ADD KEY `land_addresses_state_id_foreign` (`state_id`);

--
-- Indexes for table `land_documents`
--
ALTER TABLE `land_documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `land_documents_land_acquisition_id_index` (`land_acquisition_id`),
  ADD KEY `land_documents_document_type_index` (`document_type`),
  ADD KEY `land_documents_document_number_index` (`document_number`),
  ADD KEY `land_documents_verification_status_index` (`verification_status`),
  ADD KEY `land_documents_status_index` (`status`),
  ADD KEY `land_documents_document_date_index` (`document_date`),
  ADD KEY `land_documents_uploaded_by_index` (`uploaded_by`),
  ADD KEY `land_documents_verified_by_index` (`verified_by`);

--
-- Indexes for table `land_owners`
--
ALTER TABLE `land_owners`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `land_owners_phone_unique` (`phone`),
  ADD UNIQUE KEY `land_owners_nid_number_unique` (`nid_number`),
  ADD KEY `land_owners_first_name_last_name_index` (`first_name`,`last_name`),
  ADD KEY `land_owners_phone_index` (`phone`),
  ADD KEY `land_owners_nid_number_index` (`nid_number`),
  ADD KEY `land_owners_status_index` (`status`),
  ADD KEY `land_owners_created_by_index` (`created_by`),
  ADD KEY `land_owners_updated_by_index` (`updated_by`);

--
-- Indexes for table `land_owner_audits`
--
ALTER TABLE `land_owner_audits`
  ADD PRIMARY KEY (`id`),
  ADD KEY `land_owner_audits_land_owner_id_index` (`land_owner_id`),
  ADD KEY `land_owner_audits_action_index` (`action`),
  ADD KEY `land_owner_audits_user_id_index` (`user_id`),
  ADD KEY `land_owner_audits_created_at_index` (`created_at`);

--
-- Indexes for table `languages`
--
ALTER TABLE `languages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `languages_name_unique` (`name`),
  ADD UNIQUE KEY `languages_code_unique` (`code`),
  ADD KEY `languages_status_index` (`status`);

--
-- Indexes for table `lease_types`
--
ALTER TABLE `lease_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `lease_types_name_unique` (`name`);

--
-- Indexes for table `locations`
--
ALTER TABLE `locations`
  ADD PRIMARY KEY (`id`),
  ADD KEY `locations_state_id_status_index` (`state_id`,`status`),
  ADD KEY `locations_country_id_status_index` (`country_id`,`status`),
  ADD KEY `locations_name_state_id_index` (`name`,`state_id`);

--
-- Indexes for table `menus`
--
ALTER TABLE `menus`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `menus_name_unique` (`name`);

--
-- Indexes for table `menu_manages`
--
ALTER TABLE `menu_manages`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `menu_manages_slug_unique` (`slug`);

--
-- Indexes for table `menu_positions`
--
ALTER TABLE `menu_positions`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `migrations`
--
ALTER TABLE `migrations`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `page_contents`
--
ALTER TABLE `page_contents`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `password_reset_tokens`
--
ALTER TABLE `password_reset_tokens`
  ADD PRIMARY KEY (`email`);

--
-- Indexes for table `payment_methods`
--
ALTER TABLE `payment_methods`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `payment_methods_name_unique` (`name`);

--
-- Indexes for table `payment_statuses`
--
ALTER TABLE `payment_statuses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `payment_statuses_name_unique` (`name`);

--
-- Indexes for table `payment_types`
--
ALTER TABLE `payment_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `payment_types_name_unique` (`name`);

--
-- Indexes for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `personal_access_tokens_token_unique` (`token`),
  ADD KEY `personal_access_tokens_tokenable_type_tokenable_id_index` (`tokenable_type`,`tokenable_id`);

--
-- Indexes for table `projects`
--
ALTER TABLE `projects`
  ADD PRIMARY KEY (`id`),
  ADD KEY `projects_created_by_foreign` (`created_by`),
  ADD KEY `projects_updated_by_foreign` (`updated_by`),
  ADD KEY `projects_property_type_status_index` (`status`),
  ADD KEY `projects_is_featured_is_available_index` (`is_featured`,`is_available`),
  ADD KEY `projects_country_id_foreign` (`country_id`),
  ADD KEY `projects_state_id_foreign` (`state_id`),
  ADD KEY `projects_city_state_id_country_id_index` (`state_id`,`country_id`),
  ADD KEY `projects_property_type_id_index` (`property_type_id`),
  ADD KEY `projects_property_status_id_index` (`property_status_id`);

--
-- Indexes for table `project_amenities`
--
ALTER TABLE `project_amenities`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `project_amenities_project_id_property_amenity_id_unique` (`project_id`,`property_amenity_id`),
  ADD KEY `project_amenities_project_id_index` (`project_id`),
  ADD KEY `project_amenities_property_amenity_id_index` (`property_amenity_id`);

--
-- Indexes for table `project_contractors`
--
ALTER TABLE `project_contractors`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_project_contractor_role` (`project_id`,`contractor_id`,`assignment_role`),
  ADD KEY `project_contractors_assigned_by_foreign` (`assigned_by`),
  ADD KEY `project_contractors_project_id_status_index` (`project_id`,`status`),
  ADD KEY `project_contractors_contractor_id_status_index` (`contractor_id`,`status`);

--
-- Indexes for table `project_documents`
--
ALTER TABLE `project_documents`
  ADD PRIMARY KEY (`id`),
  ADD KEY `project_documents_project_id_foreign` (`project_id`);

--
-- Indexes for table `project_employees`
--
ALTER TABLE `project_employees`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unique_project_employee` (`project_id`,`employee_id`),
  ADD KEY `project_employees_project_id_status_index` (`project_id`,`status`),
  ADD KEY `project_employees_employee_id_status_index` (`employee_id`,`status`),
  ADD KEY `project_employees_assigned_by_index` (`assigned_by`);

--
-- Indexes for table `project_images`
--
ALTER TABLE `project_images`
  ADD PRIMARY KEY (`id`),
  ADD KEY `project_images_created_by_foreign` (`created_by`),
  ADD KEY `project_images_updated_by_foreign` (`updated_by`),
  ADD KEY `project_images_project_id_sort_order_index` (`project_id`,`sort_order`),
  ADD KEY `project_images_project_id_is_featured_index` (`project_id`,`is_featured`);

--
-- Indexes for table `project_units`
--
ALTER TABLE `project_units`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `project_units_project_id_unit_number_unique` (`project_id`,`unit_number`),
  ADD KEY `project_units_created_by_foreign` (`created_by`),
  ADD KEY `project_units_updated_by_foreign` (`updated_by`),
  ADD KEY `project_units_unit_type_status_index` (`unit_type_id`,`status`),
  ADD KEY `project_units_floor_number_index` (`floor_number`);

--
-- Indexes for table `project_vendors`
--
ALTER TABLE `project_vendors`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `project_vendor_unique` (`project_id`,`vendor_id`,`assignment_role`),
  ADD KEY `project_vendors_vendor_id_foreign` (`vendor_id`),
  ADD KEY `project_vendors_project_id_vendor_id_index` (`project_id`,`vendor_id`),
  ADD KEY `project_vendors_status_index` (`status`),
  ADD KEY `project_vendors_assigned_by_index` (`assigned_by`);

--
-- Indexes for table `project_videos`
--
ALTER TABLE `project_videos`
  ADD PRIMARY KEY (`id`),
  ADD KEY `project_videos_created_by_foreign` (`created_by`),
  ADD KEY `project_videos_updated_by_foreign` (`updated_by`),
  ADD KEY `project_videos_project_id_sort_order_index` (`project_id`,`sort_order`),
  ADD KEY `project_videos_video_type_is_featured_index` (`video_type`,`is_featured`);

--
-- Indexes for table `property_amenities`
--
ALTER TABLE `property_amenities`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `property_amenities_name_unique` (`name`),
  ADD KEY `property_amenities_is_active_sort_order_index` (`is_active`,`sort_order`),
  ADD KEY `property_amenities_category_index` (`category`),
  ADD KEY `property_amenities_created_by_index` (`created_by`),
  ADD KEY `property_amenities_updated_by_index` (`updated_by`);

--
-- Indexes for table `property_items`
--
ALTER TABLE `property_items`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `property_services`
--
ALTER TABLE `property_services`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `property_sevices_name_unique` (`name`);

--
-- Indexes for table `property_stages`
--
ALTER TABLE `property_stages`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `property_statuses`
--
ALTER TABLE `property_statuses`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `property_statuses_name_unique` (`name`),
  ADD UNIQUE KEY `property_statuses_slug_unique` (`slug`),
  ADD KEY `property_statuses_status_sort_order_index` (`is_active`,`sort_order`),
  ADD KEY `property_statuses_slug_index` (`slug`);

--
-- Indexes for table `property_types`
--
ALTER TABLE `property_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `property_types_name_unique` (`name`),
  ADD UNIQUE KEY `property_types_slug_unique` (`slug`),
  ADD KEY `property_types_status_sort_order_index` (`sort_order`),
  ADD KEY `property_types_slug_index` (`slug`);

--
-- Indexes for table `rent_types`
--
ALTER TABLE `rent_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `rent_types_name_unique` (`name`);

--
-- Indexes for table `roles`
--
ALTER TABLE `roles`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `roles_name_unique` (`name`);

--
-- Indexes for table `sessions`
--
ALTER TABLE `sessions`
  ADD PRIMARY KEY (`id`),
  ADD KEY `sessions_user_id_index` (`user_id`),
  ADD KEY `sessions_last_activity_index` (`last_activity`);

--
-- Indexes for table `states`
--
ALTER TABLE `states`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `states_name_country_id_unique` (`name`,`country_id`),
  ADD KEY `states_country_id_index` (`country_id`),
  ADD KEY `states_status_index` (`status`),
  ADD KEY `states_name_index` (`name`);

--
-- Indexes for table `tanants`
--
ALTER TABLE `tanants`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `tanants_email_unique` (`email`);

--
-- Indexes for table `tenants_documents`
--
ALTER TABLE `tenants_documents`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `tenant_emergency_contacts`
--
ALTER TABLE `tenant_emergency_contacts`
  ADD PRIMARY KEY (`id`);

--
-- Indexes for table `unit_types`
--
ALTER TABLE `unit_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `unit_types_name_unique` (`name`),
  ADD UNIQUE KEY `unit_types_slug_unique` (`slug`);

--
-- Indexes for table `users`
--
ALTER TABLE `users`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `users_email_unique` (`email`),
  ADD KEY `users_role_id_foreign` (`role_id`);

--
-- Indexes for table `vendors`
--
ALTER TABLE `vendors`
  ADD PRIMARY KEY (`id`),
  ADD KEY `vendors_status_index` (`status`),
  ADD KEY `vendors_vendor_type_id_index` (`vendor_type_id`),
  ADD KEY `vendors_created_by_index` (`created_by`),
  ADD KEY `vendors_updated_by_index` (`updated_by`),
  ADD KEY `vendors_name_index` (`name`);

--
-- Indexes for table `vendor_types`
--
ALTER TABLE `vendor_types`
  ADD PRIMARY KEY (`id`),
  ADD UNIQUE KEY `vendor_types_name_unique` (`name`),
  ADD KEY `vendor_types_created_by_foreign` (`created_by`),
  ADD KEY `vendor_types_updated_by_foreign` (`updated_by`);

--
-- Indexes for table `widgets`
--
ALTER TABLE `widgets`
  ADD PRIMARY KEY (`id`);

--
-- AUTO_INCREMENT for dumped tables
--

--
-- AUTO_INCREMENT for table `backend_settings`
--
ALTER TABLE `backend_settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `blogs`
--
ALTER TABLE `blogs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=19;

--
-- AUTO_INCREMENT for table `commission_agents`
--
ALTER TABLE `commission_agents`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `contractors`
--
ALTER TABLE `contractors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `countries`
--
ALTER TABLE `countries`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=251;

--
-- AUTO_INCREMENT for table `currencies`
--
ALTER TABLE `currencies`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `customers`
--
ALTER TABLE `customers`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=25;

--
-- AUTO_INCREMENT for table `development_costings`
--
ALTER TABLE `development_costings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `employees`
--
ALTER TABLE `employees`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `failed_jobs`
--
ALTER TABLE `failed_jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `frontend_settings`
--
ALTER TABLE `frontend_settings`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `invoices`
--
ALTER TABLE `invoices`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `invoice_items`
--
ALTER TABLE `invoice_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `jobs`
--
ALTER TABLE `jobs`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `land_acquisitions`
--
ALTER TABLE `land_acquisitions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `land_addresses`
--
ALTER TABLE `land_addresses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `land_documents`
--
ALTER TABLE `land_documents`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `land_owners`
--
ALTER TABLE `land_owners`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=23;

--
-- AUTO_INCREMENT for table `land_owner_audits`
--
ALTER TABLE `land_owner_audits`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=67;

--
-- AUTO_INCREMENT for table `languages`
--
ALTER TABLE `languages`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=16;

--
-- AUTO_INCREMENT for table `lease_types`
--
ALTER TABLE `lease_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `locations`
--
ALTER TABLE `locations`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `menus`
--
ALTER TABLE `menus`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=12;

--
-- AUTO_INCREMENT for table `menu_manages`
--
ALTER TABLE `menu_manages`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `menu_positions`
--
ALTER TABLE `menu_positions`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `migrations`
--
ALTER TABLE `migrations`
  MODIFY `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=139;

--
-- AUTO_INCREMENT for table `page_contents`
--
ALTER TABLE `page_contents`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=447;

--
-- AUTO_INCREMENT for table `payment_methods`
--
ALTER TABLE `payment_methods`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=10;

--
-- AUTO_INCREMENT for table `payment_statuses`
--
ALTER TABLE `payment_statuses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `payment_types`
--
ALTER TABLE `payment_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `personal_access_tokens`
--
ALTER TABLE `personal_access_tokens`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=117;

--
-- AUTO_INCREMENT for table `projects`
--
ALTER TABLE `projects`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `project_amenities`
--
ALTER TABLE `project_amenities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_contractors`
--
ALTER TABLE `project_contractors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `project_documents`
--
ALTER TABLE `project_documents`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `project_employees`
--
ALTER TABLE `project_employees`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `project_images`
--
ALTER TABLE `project_images`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=17;

--
-- AUTO_INCREMENT for table `project_units`
--
ALTER TABLE `project_units`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=6;

--
-- AUTO_INCREMENT for table `project_vendors`
--
ALTER TABLE `project_vendors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=3;

--
-- AUTO_INCREMENT for table `project_videos`
--
ALTER TABLE `project_videos`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT;

--
-- AUTO_INCREMENT for table `property_amenities`
--
ALTER TABLE `property_amenities`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `property_items`
--
ALTER TABLE `property_items`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=50;

--
-- AUTO_INCREMENT for table `property_services`
--
ALTER TABLE `property_services`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=13;

--
-- AUTO_INCREMENT for table `property_stages`
--
ALTER TABLE `property_stages`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=26;

--
-- AUTO_INCREMENT for table `property_statuses`
--
ALTER TABLE `property_statuses`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=11;

--
-- AUTO_INCREMENT for table `property_types`
--
ALTER TABLE `property_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=20;

--
-- AUTO_INCREMENT for table `rent_types`
--
ALTER TABLE `rent_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=8;

--
-- AUTO_INCREMENT for table `roles`
--
ALTER TABLE `roles`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=7;

--
-- AUTO_INCREMENT for table `states`
--
ALTER TABLE `states`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=33;

--
-- AUTO_INCREMENT for table `tanants`
--
ALTER TABLE `tanants`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `tenants_documents`
--
ALTER TABLE `tenants_documents`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=2;

--
-- AUTO_INCREMENT for table `tenant_emergency_contacts`
--
ALTER TABLE `tenant_emergency_contacts`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=5;

--
-- AUTO_INCREMENT for table `unit_types`
--
ALTER TABLE `unit_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- AUTO_INCREMENT for table `users`
--
ALTER TABLE `users`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `vendors`
--
ALTER TABLE `vendors`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=4;

--
-- AUTO_INCREMENT for table `vendor_types`
--
ALTER TABLE `vendor_types`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=9;

--
-- AUTO_INCREMENT for table `widgets`
--
ALTER TABLE `widgets`
  MODIFY `id` bigint(20) UNSIGNED NOT NULL AUTO_INCREMENT, AUTO_INCREMENT=15;

--
-- Constraints for dumped tables
--

--
-- Constraints for table `blogs`
--
ALTER TABLE `blogs`
  ADD CONSTRAINT `blogs_category_id_foreign` FOREIGN KEY (`category_id`) REFERENCES `property_types` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `contractors`
--
ALTER TABLE `contractors`
  ADD CONSTRAINT `contractors_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `contractors_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `development_costings`
--
ALTER TABLE `development_costings`
  ADD CONSTRAINT `development_costings_approved_by_foreign` FOREIGN KEY (`approved_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `development_costings_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `development_costings_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `development_costings_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `employees`
--
ALTER TABLE `employees`
  ADD CONSTRAINT `employees_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `employees_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `land_acquisitions`
--
ALTER TABLE `land_acquisitions`
  ADD CONSTRAINT `land_acquisitions_landowners_id_foreign` FOREIGN KEY (`land_owner_id`) REFERENCES `land_owners` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `land_addresses`
--
ALTER TABLE `land_addresses`
  ADD CONSTRAINT `land_addresses_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `land_addresses_land_acquisition_id_foreign` FOREIGN KEY (`land_acquisition_id`) REFERENCES `land_acquisitions` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `land_addresses_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `land_documents`
--
ALTER TABLE `land_documents`
  ADD CONSTRAINT `land_documents_land_acquisition_id_foreign` FOREIGN KEY (`land_acquisition_id`) REFERENCES `land_acquisitions` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `land_owner_audits`
--
ALTER TABLE `land_owner_audits`
  ADD CONSTRAINT `land_owner_audits_land_owner_id_foreign` FOREIGN KEY (`land_owner_id`) REFERENCES `land_owners` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `locations`
--
ALTER TABLE `locations`
  ADD CONSTRAINT `locations_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `locations_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `projects`
--
ALTER TABLE `projects`
  ADD CONSTRAINT `projects_country_id_foreign` FOREIGN KEY (`country_id`) REFERENCES `countries` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `projects_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `projects_property_status_id_foreign` FOREIGN KEY (`property_status_id`) REFERENCES `property_statuses` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `projects_property_type_id_foreign` FOREIGN KEY (`property_type_id`) REFERENCES `property_types` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `projects_state_id_foreign` FOREIGN KEY (`state_id`) REFERENCES `states` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `projects_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;

--
-- Constraints for table `project_amenities`
--
ALTER TABLE `project_amenities`
  ADD CONSTRAINT `project_amenities_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `project_amenities_property_amenity_id_foreign` FOREIGN KEY (`property_amenity_id`) REFERENCES `property_amenities` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `project_contractors`
--
ALTER TABLE `project_contractors`
  ADD CONSTRAINT `project_contractors_assigned_by_foreign` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `project_contractors_contractor_id_foreign` FOREIGN KEY (`contractor_id`) REFERENCES `contractors` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `project_contractors_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `project_documents`
--
ALTER TABLE `project_documents`
  ADD CONSTRAINT `project_documents_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `project_employees`
--
ALTER TABLE `project_employees`
  ADD CONSTRAINT `project_employees_assigned_by_foreign` FOREIGN KEY (`assigned_by`) REFERENCES `users` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `project_employees_employee_id_foreign` FOREIGN KEY (`employee_id`) REFERENCES `employees` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `project_employees_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE;

--
-- Constraints for table `project_units`
--
ALTER TABLE `project_units`
  ADD CONSTRAINT `project_units_created_by_foreign` FOREIGN KEY (`created_by`) REFERENCES `users` (`id`) ON DELETE SET NULL,
  ADD CONSTRAINT `project_units_project_id_foreign` FOREIGN KEY (`project_id`) REFERENCES `projects` (`id`) ON DELETE CASCADE,
  ADD CONSTRAINT `project_units_updated_by_foreign` FOREIGN KEY (`updated_by`) REFERENCES `users` (`id`) ON DELETE SET NULL;
COMMIT;

/*!40101 SET CHARACTER_SET_CLIENT=@OLD_CHARACTER_SET_CLIENT */;
/*!40101 SET CHARACTER_SET_RESULTS=@OLD_CHARACTER_SET_RESULTS */;
/*!40101 SET COLLATION_CONNECTION=@OLD_COLLATION_CONNECTION */;
