<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class PaymentTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('payment_types')->insert([
            [
                'name' => 'Cash',
                'description' => 'Cash payment on delivery or in person',
                'is_active' => true,
                'sort_order' => 1,
                'icon' => 'banknotes',
                'color' => '#10b981',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Credit Card',
                'description' => 'Payment via Visa, MasterCard, or American Express',
                'is_active' => true,
                'sort_order' => 2,
                'icon' => 'credit-card',
                'color' => '#3b82f6',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Bank Transfer',
                'description' => 'Direct bank transfer or wire transfer',
                'is_active' => true,
                'sort_order' => 3,
                'icon' => 'building-library',
                'color' => '#8b5cf6',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Digital Wallet',
                'description' => 'PayPal, Apple Pay, Google Pay, etc.',
                'is_active' => true,
                'sort_order' => 4,
                'icon' => 'device-phone-mobile',
                'color' => '#f59e0b',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Cryptocurrency',
                'description' => 'Bitcoin, Ethereum, and other cryptocurrencies',
                'is_active' => false,
                'sort_order' => 5,
                'icon' => 'currency-bitcoin',
                'color' => '#ef4444',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'name' => 'Check',
                'description' => 'Personal or business check payment',
                'is_active' => true,
                'sort_order' => 6,
                'icon' => 'document-text',
                'color' => '#6b7280',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ]);
    }
}
