import React,{useState,useEffect} from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import Preloader from "./preloader";
import Modals from "./modal";
import Sidebar from "./sidebar";
import SearchBar from "./searchBar";
import HeaderMenu from "./headerMenu";
import frontendSettingsAPI from "../../services/frontendSettingsAPI";

const Header = () => {

  const [frontendSettings, setFrontendSettings] = useState({ });
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    fetchFrontendSettings();
  }, []);

  const fetchFrontendSettings = async () => {
    try {
      const response = await frontendSettingsAPI.getAll();
      if (response.success && response.data) {
        setFrontendSettings(prevSettings => ({
          ...prevSettings,
          ...response.data
        }));
      }
    } catch (error) {
      console.error('Error fetching frontend settings:', error);
      // Keep the default settings if fetch fails
    } finally {
      setLoading(false);
    }
  };
  const location = useLocation();
  const navigate = useNavigate();

  const handleLogout = () => {
    // Add your logout logic here
    navigate("/login");
  };

  return (
    <div>
      {frontendSettings.showshowPreloader=== 1&& (
     <Preloader/>
     )}
     <Modals/>
     <Sidebar/>
     <SearchBar/>
     <HeaderMenu
     frontendSettings={frontendSettings}
     />
    </div>
  );
};

export default Header;
