import React from 'react';
import { useDrop } from 'react-dnd';

const HorizontalDropZone = ({ onNest, parentItem, isVisible, draggedItem }) => {
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'MENU_ITEM',
    canDrop: (item) => {
      // Prevent dropping on itself
      if (item.id === parentItem.id) return false;
      // Prevent dropping a parent onto its own child
      if (item.parent_id === parentItem.id) return false;
      // Prevent circular nesting
      if (parentItem.parent_id === item.id) return false;
      return true;
    },
    drop: (draggedItem) => {
      onNest(draggedItem.id, parentItem.id);
    },
    collect: (monitor) => ({
      isOver: monitor.isOver(),
      canDrop: monitor.canDrop(),
    }),
  }), [onNest, parentItem.id]);

  if (!isVisible) return null;

  const isValidDrop = canDrop;
  const isActive = isOver && canDrop;
  const isInvalidDrop = isOver && !canDrop;

  return (
    <div
      ref={drop}
      className={`ml-8 transition-all duration-200 ${
        isActive
          ? 'h-12 my-1 bg-green-50 border-2 border-dashed border-green-400 rounded-lg flex items-center justify-center shadow-md'
          : isInvalidDrop
            ? 'h-12 my-1 bg-red-50 border-2 border-dashed border-red-400 rounded-lg flex items-center justify-center shadow-md'
            : isVisible 
              ? 'h-6 my-1 bg-green-50/30 border border-dashed border-green-300 rounded-lg flex items-center justify-center'
              : 'h-0 overflow-hidden'
      }`}
    >
      {isVisible && (
        <div className={`flex items-center gap-2 text-sm ${
          isActive 
            ? 'text-green-600 font-medium' 
            : isInvalidDrop 
              ? 'text-red-600 font-medium'
              : 'text-green-600 font-normal opacity-70'
        }`}>
          <div className={`w-2 h-2 rounded-full ${
            isActive 
              ? 'bg-green-500 animate-pulse' 
              : isInvalidDrop 
                ? 'bg-red-500 animate-pulse'
                : 'bg-green-500'
          }`}></div>
          <span>
            {isActive 
              ? `Drop here to make it a child of "${parentItem.title}"` 
              : isInvalidDrop
                ? `Cannot nest here - invalid relationship`
                : `Drop zone for "${parentItem.title}" children`
            }
          </span>
          <div className={`w-2 h-2 rounded-full ${
            isActive 
              ? 'bg-green-500 animate-pulse' 
              : isInvalidDrop 
                ? 'bg-red-500 animate-pulse'
                : 'bg-green-500'
          }`}></div>
        </div>
      )}
    </div>
  );
};

export default HorizontalDropZone;