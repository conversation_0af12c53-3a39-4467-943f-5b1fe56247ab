import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu";
import { 
  Users, User, UserX, Calendar, FileText, Phone, Mail, MapPin, 
  MoreHorizontal, Edit, Trash2, UserCheck, ChevronLeft, ChevronRight 
} from "lucide-react";
import { useTranslation } from '@/hooks/useTranslation';
import DocumentViewer from '@/components/ui/DocumentViewer';

const LandOwnersTable = ({
  landOwners,
  loading,
  currentPage,
  totalPages,
  totalRecords,
  handleEditOwner,
  handleDeleteOwner,
  handleActivateOwner,
  handleDeactivateOwner,
  handleImageClick,
  getImageUrl,
  searchTerm,
  perPage,
  handlePageChange
}) => {
    const { t } = useTranslation();
   
    return (
        <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5" />
                {t('landOwners.title')} ({totalRecords})
              </CardTitle>
              <CardDescription>
                {t('landOwners.description')}
              </CardDescription>
            </div>
            
          </div>
        </CardHeader>
        <CardContent>
          {loading ? (
            <div className="flex justify-center items-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"></div>
            </div>
          ) : landOwners.length === 0 ? (
            <div className="text-center py-8">
              <Users className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No land owners found</h3>
              <p className="mt-1 text-sm text-gray-500">
                {searchTerm ? 'Try adjusting your search criteria.' : 'Get started by creating a new land owner.'}
              </p>
            </div>
          ) : (
            <>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>{t('landOwners.table.serialNumber')}</TableHead>
                    <TableHead>{t('landOwners.table.photo')}</TableHead>
                    <TableHead>{t('landOwners.table.firstName')}</TableHead>
                    <TableHead>{t('landOwners.table.lastName')}</TableHead>
                    <TableHead>{t('landOwners.table.fatherName')}</TableHead>
                    <TableHead>{t('landOwners.table.nidNumber')}</TableHead>
                    <TableHead>{t('landOwners.table.contact')}</TableHead>
                    <TableHead>{t('landOwners.table.address')}</TableHead>
                    <TableHead>{t('landOwners.table.status')}</TableHead>
                    <TableHead>{t('landOwners.table.documents')}</TableHead>
                    <TableHead className="text-right">{t('landOwners.table.actions')}</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {landOwners.map((owner, index) => (
                    <TableRow key={owner.id}>
                     <TableCell className="text-center text-muted-foreground">
                        {((currentPage - 1) * perPage) + index + 1}
                      </TableCell>
                      <TableCell>
                        {owner.photo ? (
                          <img
                            src={getImageUrl(owner.photo)}
                            alt={`${owner.first_name} ${owner.last_name}`}
                            style={{ width: '60px', height: '60px', objectFit: 'cover', borderRadius: '8px', cursor: 'pointer' }}
                            onClick={() => handleImageClick(owner.photo, `${owner.first_name} ${owner.last_name}`)}
                          />
                        ) : (
                          <div 
                            style={{ 
                              width: '60px', 
                              height: '60px', 
                              backgroundColor: '#f3f4f6', 
                              borderRadius: '8px', 
                              display: 'flex', 
                              alignItems: 'center', 
                              justifyContent: 'center',
                              fontSize: '12px',
                              color: '#6b7280'
                            }}
                          >
                            {t('landOwners.table.noImage')}
                          </div>
                        )}
                      </TableCell>
                      <TableCell className="font-medium">{owner.first_name}</TableCell>
                      <TableCell className="font-medium">{owner.last_name}</TableCell>
                      <TableCell>{owner.father_name}</TableCell>
                      <TableCell>{owner.nid_number || '-'}</TableCell>
                      <TableCell>
                        <div className="space-y-1">
                          {owner.phone && (
                            <div className="flex items-center gap-1 text-sm">
                              <Phone className="h-3 w-3" />
                              {owner.phone}
                            </div>
                          )}
                          {owner.email && (
                            <div className="flex items-center gap-1 text-sm">
                              <Mail className="h-3 w-3" />
                              {owner.email}
                            </div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1 text-sm">
                          <MapPin className="h-3 w-3" />
                          <span className="truncate max-w-[200px]">{owner.address}</span>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge 
                          variant={owner.status === 'active' ? 'default' : 'secondary'}
                          className={owner.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                        >
                          {owner.status === 'active' ? t('landOwners.table.active') : t('landOwners.table.inactive')}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <DocumentViewer 
                          owner={owner}
                          onViewImage={handleImageClick}
                        />
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEditOwner(owner)}>
                              <Edit className="mr-2 h-4 w-4" />
                              {t('landOwners.table.edit')}
                            </DropdownMenuItem>
                            {owner.status === 'active' ? (
                              <DropdownMenuItem
                                onClick={() => handleDeactivateOwner(owner)}
                                className="text-orange-600"
                              >
                                <UserX className="mr-2 h-4 w-4" />
                                {t('landOwners.table.deactivate')}
                              </DropdownMenuItem>
                            ) : (
                              <DropdownMenuItem
                                onClick={() => handleActivateOwner(owner)}
                                className="text-green-600"
                              >
                                <UserCheck className="mr-2 h-4 w-4" />
                                {t('landOwners.table.activate')}
                              </DropdownMenuItem>
                            )}
                            <DropdownMenuItem
                              onClick={() => handleDeleteOwner(owner)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              {t('landOwners.table.delete')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {/* Pagination */}
              {totalPages > 1 && (
                <div className="flex items-center justify-between space-x-2 py-4">
                  <div className="text-sm text-muted-foreground">
                    Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, totalRecords)} of {totalRecords} entries
                  </div>
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage <= 1}
                    >
                      <ChevronLeft className="h-4 w-4" />
                      Previous
                    </Button>
                    <div className="flex items-center space-x-1">
                      {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                        const page = i + 1;
                        return (
                          <Button
                            key={page}
                            variant={currentPage === page ? "default" : "outline"}
                            size="sm"
                            onClick={() => handlePageChange(page)}
                          >
                            {page}
                          </Button>
                        );
                      })}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage >= totalPages}
                    >
                      Next
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </>
          )}
        </CardContent>
      </Card>
    );
};
export default LandOwnersTable;
