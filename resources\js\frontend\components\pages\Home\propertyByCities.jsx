
import React, { useEffect, useState } from "react";

const PropertyByCities = () => {
  const [states, setStates] = useState([]);

  useEffect(() => {
    fetch("/api/states")
      .then((res) => res.json())
      .then((data) => {
        if (data.success && Array.isArray(data.data)) {
          setStates(data.data);
        }
      });
  }, []);

  return (
    <div className="recent-launched-project mb-100">
      <div className="container">
        <div className="row mb-50 wow fadeInUp" data-wow-delay="200ms">
          <div className="col-lg-12 d-flex align-items-end justify-content-between gap-3 flex-wrap">
            <div className="section-title-2">
              <h2>Properties By Cities</h2>
              <p>Here are some of the featured Apartment in different categories</p>
            </div>
            <div className="slider-btn-group2">
              <div className="slider-btn prev-5">
                <svg width="9" height="15" viewBox="0 0 8 13" xmlns="http://www.w3.org/2000/svg">
                  <path d="M0 6.50008L8 0L2.90909 6.50008L8 13L0 6.50008Z"></path>
                </svg>
              </div>
              <div className="slider-btn next-5">
                <svg width="9" height="15" viewBox="0 0 8 13" xmlns="http://www.w3.org/2000/svg">
                  <path d="M8 6.50008L0 0L5.09091 6.50008L0 13L8 6.50008Z"></path>
                </svg>
              </div>
            </div>
          </div>
        </div>
        <div className="row wow fadeInUp" data-wow-delay="300ms">
          <div className="col-lg-12">
            <div className="swiper recent-launch-car-slider">
              <div className="swiper-wrapper">
                {states.map((state) => (
                  <div className="swiper-slide" key={state.id}>
                    <div className="product-card2">
                      <div className="product-img">
                        <img src={state.image_url || "/Frontend/assets/img/home2/city-01.png"} alt={state.name} />
                      </div>
                      <div className="product-content">
                        <h6>{state.name}</h6>
                        <span>{state.property_count || 0} Property</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PropertyByCities;
