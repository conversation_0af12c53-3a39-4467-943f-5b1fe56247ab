<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class InvoiceItem extends Model
{
    //
    protected $fillable = [
        'invoice_id',
        'item_name',
        'qty',
        'item_price',
        'item_tax',
        'item_total_price',
    ];

    protected $casts = [
        'qty' => 'integer',
        'item_price' => 'decimal:2',
        'item_tax' => 'decimal:2',
        'item_total_price' => 'decimal:2',
    ];

    // Relationships
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }
}
