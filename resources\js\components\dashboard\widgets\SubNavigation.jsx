import React from "react";
const SearchNavigation = ({ widget, handleConfigUpdate }) => {

return(
     <div className="border p-4 rounded-md space-y-4">
                         
                            <div className="flex items-center space-x-2">
                              <label htmlFor={`subNavigation-switch-${widget.id}`} className="text-sm font-medium text-gray-700">
                                Browse Rent Home
                              </label>
                              <input
                                id={`subNavigation-switch-${widget.id}`}
                                type="checkbox"
                                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-black"
                                checked={widget.config?.BrowseRentHomeEnabled || false}
                                onChange={(e) => handleConfigUpdate(widget.id, "BrowseRentHomeEnabled", e.target.checked)}
                              />
                              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1 checked:translate-x-6"></span>
                            </div>

                            <div className="flex items-center space-x-2">
                              <label htmlFor={`subNavigation-switch-${widget.id}`} className="text-sm font-medium text-gray-700">
                                For Home Sale
                              </label>
                              <input
                                id={`subNavigation-switch-${widget.id}`}
                                type="checkbox"
                                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-black"
                                checked={widget.config?.homeSaleEnabled || false}
                                onChange={(e) => handleConfigUpdate(widget.id, "homeSaleEnabled", e.target.checked)}
                              />
                              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1 checked:translate-x-6"></span>
                            </div>

                            <div className="flex items-center space-x-2">
                              <label htmlFor={`subNavigation-switch-${widget.id}`} className="text-sm font-medium text-gray-700">
                                Browse Offer
                              </label>
                              <input
                                id={`subNavigation-switch-${widget.id}`}
                                type="checkbox"
                                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-black"
                                checked={widget.config?.browseOfferEnabled || false}
                                onChange={(e) => handleConfigUpdate(widget.id, "browseOfferEnabled", e.target.checked)}
                              />
                              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1 checked:translate-x-6"></span>
                            </div>

                            <div className="flex items-center space-x-2">
                              <label htmlFor={`subNavigation-switch-${widget.id}`} className="text-sm font-medium text-gray-700">
                                Get In Touch
                              </label>
                              <input
                                id={`subNavigation-switch-${widget.id}`}
                                type="checkbox"
                                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-black"
                                checked={widget.config?.getInTouchEnabled || false}
                                onChange={(e) => handleConfigUpdate(widget.id, "getInTouchEnabled", e.target.checked)}
                              />
                              <span className="inline-block h-4 w-4 transform rounded-full bg-white transition-transform translate-x-1 checked:translate-x-6"></span>
                            </div>
                          </div>
)};


export default SearchNavigation;
