import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Plus, Search, Edit, Trash2, Eye } from 'lucide-react';
import { landAcquisitionAPI } from '@/services/landAcquisitionAPI';
import { getForDropdown as getLandOwnersDropdown } from '@/services/landOwnerAPI';
import LandAcquisitionModal from './LandAcquisitionModal';
import Swal from 'sweetalert2';

const LandAcquisitionPage = () => {
    // State management
    const [landAcquisitions, setLandAcquisitions] = useState([]);
    const [landOwners, setLandOwners] = useState([]);
    const [loading, setLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [currentPage, setCurrentPage] = useState(1);
    const [totalPages, setTotalPages] = useState(1);
    const [perPage] = useState(10);
    
    // Modal state
    const [showModal, setShowModal] = useState(false);
    const [editingRecord, setEditingRecord] = useState(null);
    const [modalMode, setModalMode] = useState('create'); // 'create', 'edit', 'view'

    // Statistics state
    const [statistics, setStatistics] = useState({
        total_records: 0,
        total_land_size: 0,
        total_acquisition_value: 0,
        average_price_per_decimal: 0
    });

    // Load data on component mount
    useEffect(() => {
        loadLandAcquisitions();
        loadLandOwners();
        loadStatistics();
    }, [currentPage, searchTerm]);

    // Load land acquisitions
    const loadLandAcquisitions = async () => {
        try {
            setLoading(true);
            const params = {
                page: currentPage,
                per_page: perPage,
                search: searchTerm
            };
            
            const response = await landAcquisitionAPI.getAll(params);
            if (response.success) {
                setLandAcquisitions(response.data.data);
                setTotalPages(response.data.last_page);
            }
        } catch (error) {
            console.error('Error loading land acquisitions:', error);
            Swal.fire('Error', 'Failed to load land acquisitions', 'error');
        } finally {
            setLoading(false);
        }
    };

    // Load land owners for dropdown
    const loadLandOwners = async () => {
        try {
            const response = await getLandOwnersDropdown();
            if (response.success) {
                setLandOwners(response.data);
            }
        } catch (error) {
            console.error('Error loading land owners:', error);
        }
    };

    // Load statistics
    const loadStatistics = async () => {
        try {
            const response = await landAcquisitionAPI.getStatistics();
            if (response.success) {
                setStatistics(response.data);
            }
        } catch (error) {
            console.error('Error loading statistics:', error);
        }
    };

    // Handle search
    const handleSearch = (e) => {
        setSearchTerm(e.target.value);
        setCurrentPage(1);
    };

    // Handle create new record
    const handleCreate = () => {
        setEditingRecord(null);
        setModalMode('create');
        setShowModal(true);
    };

    // Handle edit record
    const handleEdit = (record) => {
        setEditingRecord(record);
        setModalMode('edit');
        setShowModal(true);
    };

    // Handle view record
    const handleView = (record) => {
        setEditingRecord(record);
        setModalMode('view');
        setShowModal(true);
    };

    // Handle delete record
    const handleDelete = async (id) => {
        const result = await Swal.fire({
            title: 'Are you sure?',
            text: 'This action cannot be undone!',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it!'
        });

        if (result.isConfirmed) {
            try {
                const response = await landAcquisitionAPI.delete(id);
                if (response.success) {
                    Swal.fire('Deleted!', 'Land acquisition has been deleted.', 'success');
                    loadLandAcquisitions();
                    loadStatistics();
                } else {
                    Swal.fire('Error', response.message || 'Failed to delete record', 'error');
                }
            } catch (error) {
                console.error('Error deleting record:', error);
                Swal.fire('Error', 'Failed to delete record', 'error');
            }
        }
    };

    // Handle modal save
    const handleModalSave = () => {
        setShowModal(false);
        loadLandAcquisitions();
        loadStatistics();
    };

    // Format currency
    const formatCurrency = (amount) => {
        return new Intl.NumberFormat('en-BD', {
            style: 'currency',
            currency: 'BDT',
            minimumFractionDigits: 0
        }).format(amount);
    };

    // Format land size
    const formatLandSize = (size) => {
        return `${parseFloat(size).toFixed(2)} decimal`;
    };

    return (
        <div className="space-y-6">
            {/* Header */}
            <div className="flex justify-between items-center">
                <h1 className="text-3xl font-bold">Land Acquisitions</h1>
                <Button onClick={handleCreate} className="flex items-center gap-2">
                    <Plus className="h-4 w-4" />
                    Add New Land Acquisition
                </Button>
            </div>

            {/* Statistics Cards */}
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">Total Records</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{statistics.total_records}</div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">Total Land Size</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{statistics.total_land_size} decimal</div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">Total Value</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{formatCurrency(statistics.total_acquisition_value)}</div>
                    </CardContent>
                </Card>
                <Card>
                    <CardHeader className="pb-2">
                        <CardTitle className="text-sm font-medium text-gray-600">Avg Price/Decimal</CardTitle>
                    </CardHeader>
                    <CardContent>
                        <div className="text-2xl font-bold">{formatCurrency(statistics.average_price_per_decimal)}</div>
                    </CardContent>
                </Card>
            </div>

            {/* Search and Filters */}
            <Card>
                <CardHeader>
                    <div className="flex items-center gap-4">
                        <div className="relative flex-1">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                            <Input
                                placeholder="Search by DAG number, Khatian, Mauza, or Land Owner..."
                                value={searchTerm}
                                onChange={handleSearch}
                                className="pl-10"
                            />
                        </div>
                    </div>
                </CardHeader>
            </Card>

            {/* Data Table */}
            <Card>
                <CardHeader>
                    <CardTitle>Land Acquisition Records</CardTitle>
                </CardHeader>
                <CardContent>
                    {loading ? (
                        <div className="flex justify-center items-center py-8">
                            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                        </div>
                    ) : landAcquisitions.length === 0 ? (
                        <div className="text-center py-8 text-gray-500">
                            No land acquisition records found
                        </div>
                    ) : (
                        <div className="overflow-x-auto">
                            <table className="w-full border-collapse">
                                <thead>
                                    <tr className="border-b">
                                        <th className="text-left p-3 font-medium">DAG Number</th>
                                        <th className="text-left p-3 font-medium">Khatian</th>
                                        <th className="text-left p-3 font-medium">Mauza</th>
                                        <th className="text-left p-3 font-medium">Land Owner</th>
                                        <th className="text-left p-3 font-medium">Land Size</th>
                                        <th className="text-left p-3 font-medium">Price</th>
                                        <th className="text-left p-3 font-medium">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {landAcquisitions.map((record) => (
                                        <tr key={record.id} className="border-b hover:bg-gray-50">
                                            <td className="p-3">
                                                <Badge variant="outline">{record.dag_number}</Badge>
                                            </td>
                                            <td className="p-3">{record.khatian_number}</td>
                                            <td className="p-3">{record.mauza}</td>
                                            <td className="p-3">
                                                {record.land_owner ? 
                                                    `${record.land_owner.first_name} ${record.land_owner.last_name}` : 
                                                    'N/A'
                                                }
                                            </td>
                                            <td className="p-3">{formatLandSize(record.land_size)}</td>
                                            <td className="p-3">{formatCurrency(record.acquisition_price)}</td>
                                            <td className="p-3">
                                                <div className="flex items-center gap-2">
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleView(record)}
                                                        className="h-8 w-8 p-0"
                                                    >
                                                        <Eye className="h-4 w-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleEdit(record)}
                                                        className="h-8 w-8 p-0"
                                                    >
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                    <Button
                                                        variant="ghost"
                                                        size="sm"
                                                        onClick={() => handleDelete(record.id)}
                                                        className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                                                    >
                                                        <Trash2 className="h-4 w-4" />
                                                    </Button>
                                                </div>
                                            </td>
                                        </tr>
                                    ))}
                                </tbody>
                            </table>
                        </div>
                    )}

                    {/* Pagination */}
                    {totalPages > 1 && (
                        <div className="flex justify-center items-center gap-2 mt-4">
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                                disabled={currentPage === 1}
                            >
                                Previous
                            </Button>
                            <span className="text-sm text-gray-600">
                                Page {currentPage} of {totalPages}
                            </span>
                            <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                                disabled={currentPage === totalPages}
                            >
                                Next
                            </Button>
                        </div>
                    )}
                </CardContent>
            </Card>

            {/* Modal */}
            {showModal && (
                <LandAcquisitionModal
                    isOpen={showModal}
                    onClose={() => setShowModal(false)}
                    onSave={handleModalSave}
                    editingRecord={editingRecord}
                    mode={modalMode}
                    landOwners={landOwners}
                />
            )}
        </div>
    );
};

export default LandAcquisitionPage;
