import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Accept': 'application/json',
    // Removed 'Content-Type': 'application/json' to allow file uploads
  },
});

api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => Promise.reject(error)
);

const backendSettingsAPI = {
    // Get all backend settings
    getAll: async () => {
        try {
            const response = await api.get('/backend-settings');
            return response.data;
        } catch (error) {
            console.error('Error fetching backend settings:', error);
            throw error;
        }
    },
    // Create new backend settings
    create: async (data) => {
        try {
            const response = await api.post('/backend-settings', data);
            return response.data;
        } catch (error) {
            console.error('Error creating backend settings:', error);
            throw error;
        }
    },
    // Get specific backend settings
    get: async (id) => {
        try {
            const response = await api.get(`/backend-settings/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching backend settings:', error);
            throw error;
        }
    },
    // Update backend settings
    update: async (id, data) => {
        try {
            const response = await api.put(`/backend-settings/${id}`, data);
            return response.data;
        } catch (error) {
            console.error('Error updating backend settings:', error);
            throw error;
        }
    },
    // Delete backend settings
    delete: async (id) => {
        try {
            const response = await api.delete(`/backend-settings/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting backend settings:', error);
            throw error;
        }
    },
};
export default backendSettingsAPI;
