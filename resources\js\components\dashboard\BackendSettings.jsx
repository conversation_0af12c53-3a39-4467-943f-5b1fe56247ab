import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, <PERSON><PERSON><PERSON><PERSON>ger, Ta<PERSON>Content } from "@/components/ui/tabs";
import General from "./backendSettingsComponents/general";
import backendSettingsAPI from "../../services/backendSettingsAPI";
import EmailConfig from "./backendSettingsComponents/emailConfig";
import GoogleRecaptcha from "./backendSettingsComponents/googleRecaptcha";
import Whatsapp from "./backendSettingsComponents/whatsapp";

const BackendSettings = () => {
  const [formData, setFormData] = useState({
    applicationName: "",
    vatTaxRateForCustomers: "",
    vatTaxRateForMerchants: "",
    systemTimeZone: "",
    dateFormat: "",
    commissionFromMerchant: "",
    driver: "",
    host: "",
    port: "",
    username: "",
    password: "",
    fromAdress: "",
    fromName: "",
    encryption: "",
    enableRecaptcha: 0,
    recaptchaKey: "",
    recaptchaSecret: "",
    whatsappChat: 0,
    whatsappNumber: "",
  });
  const [loading, setLoading] = useState(false);
  const [selectedadminLogoFile, setSelectedadminLogoFile] = useState(null);
  const [adminLogoPreview, setadminLogoPreview] = useState(null);

  const [selectedinvoiceLogoFile, setSelectedinvoiceLogoFile] = useState(null);
  const [invoiceLogoPreview, setinvoiceLogoPreview] = useState(null);

  const [existingSettingsId, setExistingSettingsId] = useState(null);

  useEffect(() => {
    const fetchSettings = async () => {
      setLoading(true);
      try {
        const response = await backendSettingsAPI.getAll();
        if (response.success && response.data) {
          const settings = response.data;
          setExistingSettingsId(settings.id);
          setFormData({
            applicationName: settings.applicationName || "",
            vatTaxRateForCustomers: settings.vatTaxRateForCustomers || "",
            vatTaxRateForMerchants: settings.vatTaxRateForMerchants || "",
            systemTimeZone: settings.systemTimeZone || "",
            dateFormat: settings.dateFormat || "",
            commissionFromMerchant: settings.commissionFromMerchant || "",
            driver: settings.driver || "",
            host: settings.host || "",
            port: settings.port || "",
            username: settings.username || "",
            password: settings.password || "", 
            fromAdress: settings.fromAdress || "",
            fromName: settings.fromName || "",
            encryption: settings.encryption || "",
            enableRecaptcha: parseInt(settings.enableRecaptcha) || 0,
            recaptchaKey: settings.recaptchaKey || "",
            recaptchaSecret: settings.recaptchaSecret || "",
            whatsappChat: parseInt(settings.whatsappChat) || 0,
            whatsappNumber: settings.whatsappNumber || "",
          });
          if (settings.adminLogo) {
            setadminLogoPreview(settings.adminLogo);
          }
          if (settings.invoiceLogo) {
            setinvoiceLogoPreview(settings.invoiceLogo);
          }
        }
      } catch (error) {
        console.error("Error loading backend settings:", error);
      } finally {
        setLoading(false);
      }
    };
    fetchSettings();
  }, []);

  const handleadminLogoFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedadminLogoFile(file);
      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setadminLogoPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleinvoiceLogoFileChange = (event) => {
    const file = event.target.files[0];
    if (file) {
      setSelectedinvoiceLogoFile(file);
      // Create preview URL
      const reader = new FileReader();
      reader.onload = (e) => {
        setinvoiceLogoPreview(e.target.result);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Global handleSubmit function that can access all state
  const handleSubmit = async () => {
    setLoading(true);
    try {
      const formDataToSend = new FormData();

      // Append Admin logo if selected
      if (selectedadminLogoFile) {
        formDataToSend.append("adminLogo", selectedadminLogoFile);
      }
      // Append invoice logo if selected
      if (selectedinvoiceLogoFile) {
        formDataToSend.append("invoiceLogo", selectedinvoiceLogoFile);
      }

      // Append other form data
      formDataToSend.append("applicationName", formData.applicationName || "");
      formDataToSend.append(
        "vatTaxRateForCustomers",
        formData.vatTaxRateForCustomers || ""
      );
      formDataToSend.append("systemTimeZone", formData.systemTimeZone || "");
      formDataToSend.append("dateFormat", formData.dateFormat || "");
      formDataToSend.append("commissionFromMerchant", formData.commissionFromMerchant || "");
      formDataToSend.append("vatTaxRateForMerchants", formData.vatTaxRateForMerchants || "");
      formDataToSend.append("driver", formData.driver || "");
      formDataToSend.append("host", formData.host || "");
      formDataToSend.append("port", formData.port || "");
      formDataToSend.append("username", formData.username || "");
      formDataToSend.append("password", formData.password || "");
      formDataToSend.append("fromAdress", formData.fromAdress || "");
      formDataToSend.append("fromName", formData.fromName || "");
      formDataToSend.append("encryption", formData.encryption || "");
      formDataToSend.append("enableRecaptcha", formData.enableRecaptcha || "");
      formDataToSend.append("recaptchaKey", formData.recaptchaKey || "");
      formDataToSend.append("recaptchaSecret", formData.recaptchaSecret || "");
      formDataToSend.append("whatsappChat", formData.whatsappChat || "");
      formDataToSend.append("whatsappNumber", formData.whatsappNumber || "");
     
      const response = await backendSettingsAPI.create(formDataToSend);
      

      if (response.success) {
        alert("Settings saved successfully!");
        // Clear the selected logo files after successful save
        setSelectedadminLogoFile(null);
        setSelectedinvoiceLogoFile(null);
      } else {
        alert("Failed to save settings.");
      }
    } catch (error) {
      console.error("Error saving settings:", error);
      alert("Something went wrong. Check console for details.");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div  className="p-6 w-full max-w-6xl mx-auto">
      <div className="flex items-center justify-between pb-4 border-b mb-6">
        <h3 className="text-lg font-semibold text-gray-900">Backend Settings</h3>
      </div>
      <Tabs defaultValue="general" className="w-full">
        <TabsList className="grid w-full grid-cols-4 mb-6">
          <TabsTrigger value="general" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">General</TabsTrigger>
          <TabsTrigger value="email-configure" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">Email Configure</TabsTrigger>
          <TabsTrigger value="google-recaptcha" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">Google Recaptcha</TabsTrigger>
          <TabsTrigger value="whatsapp" className="data-[state=active]:bg-blue-500 data-[state=active]:text-white data-[state=active]:shadow-md">Whatsapp Chat</TabsTrigger>
        </TabsList>

        <General
          formData={formData}
          handleInputChange={handleInputChange}
          handleadminLogoFileChange={handleadminLogoFileChange}
          adminLogoPreview={adminLogoPreview}
          handleinvoiceLogoFileChange={handleinvoiceLogoFileChange}
          invoiceLogoPreview={invoiceLogoPreview}
          handleSubmit={handleSubmit}
          loading={loading}
        />
        
        <EmailConfig
          formData={formData}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          loading={loading}
        />
      
        <GoogleRecaptcha
          formData={formData}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          loading={loading}
        />

        <Whatsapp
          formData={formData}
          handleInputChange={handleInputChange}
          handleSubmit={handleSubmit}
          loading={loading}
        />
      </Tabs>
    </div>
  );
};
export default BackendSettings;
