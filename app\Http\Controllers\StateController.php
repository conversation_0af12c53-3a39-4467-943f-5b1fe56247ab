<?php

namespace App\Http\Controllers;

use App\Models\State;
use App\Models\Country;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class StateController extends Controller
{
    /**
     * Display a listing of the states.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = State::with('country:id,name,code');

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhereHas('country', function ($countryQuery) use ($search) {
                          $countryQuery->where('name', 'LIKE', "%{$search}%");
                      });
                });
            }

            // Filter by country
            if ($request->has('country_id') && $request->country_id) {
                $query->where('country_id', $request->country_id);
            }

            // Filter by status
            if ($request->has('status') && $request->status) {
                $query->where('status', $request->status);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'name');
            $sortOrder = $request->get('sort_order', 'asc');
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $states = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $states->items(),
                'pagination' => [
                    'current_page' => $states->currentPage(),
                    'last_page' => $states->lastPage(),
                    'per_page' => $states->perPage(),
                    'total' => $states->total()
                ],
                'message' => 'States retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving states: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created state in storage.
     */
    public function store(Request $request): JsonResponse
    {
        try {
          
           
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'country_id' => 'required|exists:countries,id',
                'status' => 'required|in:active,inactive'
            ]);

            // Check for unique combination of name and country
            $existingState = State::where('name', $validatedData['name'])
                ->where('country_id', $validatedData['country_id'])
                ->first();

            if ($existingState) {
                return response()->json([
                    'success' => false,
                    'message' => 'A state with this name already exists in the selected country',
                    'errors' => ['name' => ['This state name already exists in the selected country']]
                ], 422);
            }

           
           
            // Handle image upload
            // Check if image field exists and is not an empty array
            $imageField = $request->input('image');
            
            if ($request->hasFile('image') && $request->file('image') instanceof \Illuminate\Http\UploadedFile) {
                $image = $request->file('image');

                // Validate the uploaded file
                if (!$image->isValid()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid image file uploaded'
                    ], 422);
                }

                $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();

                // Create directory if it doesn't exist
                $publicPath = public_path('state');
                if (!file_exists($publicPath)) {
                    if (!mkdir($publicPath, 0755, true)) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Failed to create upload directory'
                        ], 500);
                    }
                }

                // Check if directory is writable
                if (!is_writable($publicPath)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Upload directory is not writable'
                    ], 500);
                }

                // Move the uploaded file to public/state directory
                try {
                    $image->move($publicPath, $imageName);
                    $validatedData['image'] = $imageName;
                } catch (\Exception $e) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to upload image: ' . $e->getMessage()
                    ], 500);
                }
            } 

            $state = State::create($validatedData);
            $state->load('country:id,name,code');

          

            return response()->json([
                'success' => true,
                'data' => $state,
                'message' => 'State created successfully'
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
           
            
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
           
            return response()->json([
                'success' => false,
                'message' => 'Error creating state: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified state.
     */
    public function show(State $state): JsonResponse
    {
        try {
            $state->load('country:id,name,code,continent');
            
            return response()->json([
                'success' => true,
                'data' => $state,
                'message' => 'State retrieved successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving state: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified state in storage.
     */
    public function update(Request $request, State $state): JsonResponse
    {
        try {
         
            $validatedData = $request->validate([
                'name' => 'required|string|max:255',
                'country_id' => 'required|integer|exists:countries,id',
                'status' => 'required|in:active,inactive'
            ]);

            // Check for unique combination of name and country (excluding current state)
            $existingState = State::where('name', $validatedData['name'])
                ->where('country_id', $validatedData['country_id'])
                ->where('id', '!=', $state->id)
                ->first();

            if ($existingState) {
                return response()->json([
                    'success' => false,
                    'message' => 'A state with this name already exists in the selected country',
                    'errors' => ['name' => ['This state name already exists in the selected country']]
                ], 422);
            }

          
            
          

            // Handle image upload
            // Check if image field exists and is not an empty array
            $imageField = $request->input('image');
            
            if ($request->hasFile('image') && $request->file('image') instanceof \Illuminate\Http\UploadedFile) {
                $image = $request->file('image');

                // Validate the uploaded file
                if (!$image->isValid()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Invalid image file uploaded'
                    ], 422);
                }

                // Delete old image if exists
                if ($state->image) {
                    $oldImagePath = public_path('state/' . $state->image);
                    if (file_exists($oldImagePath)) {
                        unlink($oldImagePath);
                    }
                }

                $imageName = time() . '_' . uniqid() . '.' . $image->getClientOriginalExtension();

                // Create directory if it doesn't exist
                $publicPath = public_path('state');
                if (!file_exists($publicPath)) {
                    if (!mkdir($publicPath, 0755, true)) {
                        return response()->json([
                            'success' => false,
                            'message' => 'Failed to create upload directory'
                        ], 500);
                    }
                }

                // Check if directory is writable
                if (!is_writable($publicPath)) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Upload directory is not writable'
                    ], 500);
                }

                // Move the uploaded file to public/state directory
                try {
                    $image->move($publicPath, $imageName);
                    $validatedData['image'] = $imageName;
                } catch (\Exception $e) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Failed to upload image: ' . $e->getMessage()
                    ], 500);
                }
            } 

            $state->update($validatedData);
            $state->load('country:id,name,code');

            return response()->json([
                'success' => true,
                'data' => $state,
                'message' => 'State updated successfully'
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating state: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified state from storage.
     */
    public function destroy(State $state): JsonResponse
    {
        try {
            $stateName = $state->name;
            
            // Delete associated image if exists
            if ($state->image) {
                $imagePath = public_path('state/' . $state->image);
                if (file_exists($imagePath)) {
                    unlink($imagePath);
                }
            }
            
            $state->delete();

            return response()->json([
                'success' => true,
                'message' => "State '{$stateName}' deleted successfully"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error deleting state: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get states by country for dropdown (public access)
     */
    public function byCountry(Request $request, $countryId): JsonResponse
    {
        try {
            $query = State::select('id', 'name')
                ->where('country_id', $countryId)
                ->where('status', 'active');

            // Search functionality for dropdown
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where('name', 'LIKE', "%{$search}%");
            }

            $states = $query->orderBy('name', 'asc')->get();

            return response()->json([
                'success' => true,
                'data' => $states,
                'total' => $states->count(),
                'message' => 'States retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving states: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get states for dropdown (public access)
     */
    public function dropdown(Request $request): JsonResponse
    {
        try {
            $query = State::select('id', 'name', 'country_id')
                ->with('country:id,name,code')
                ->where('status', 'active');

            // Search functionality for dropdown
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhereHas('country', function ($countryQuery) use ($search) {
                          $countryQuery->where('name', 'LIKE', "%{$search}%");
                      });
                });
            }

            // Filter by country if provided
            if ($request->has('country_id') && $request->country_id) {
                $query->where('country_id', $request->country_id);
            }

            // Limit results for dropdown
            $limit = $request->get('limit', 100);
            $states = $query->orderBy('name', 'asc')->limit($limit)->get();

            return response()->json([
                'success' => true,
                'data' => $states,
                'total' => $states->count(),
                'message' => 'States retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving states: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get statistics for states.
     */
    public function getStatistics(): JsonResponse
    {
        try {
            $statistics = [
                'total_states' => State::count(),
                'active_states' => State::where('status', 'active')->count(),
                'inactive_states' => State::where('status', 'inactive')->count(),
                'states_by_country' => State::select('country_id')
                    ->with('country:id,name')
                    ->get()
                    ->groupBy('country_id')
                    ->map(function ($states) {
                        return [
                            'country' => $states->first()->country->name,
                            'count' => $states->count()
                        ];
                    })
                    ->values()
            ];

            return response()->json([
                'success' => true,
                'data' => $statistics,
                'message' => 'Statistics retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Toggle state status.
     */
    public function toggleStatus(State $state): JsonResponse
    {
        try {
            $state->status = $state->status === 'active' ? 'inactive' : 'active';
            $state->save();

            return response()->json([
                'success' => true,
                'data' => $state,
                'message' => "State status updated to {$state->status}"
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error updating state status: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Public search for states (no authentication required)
     */
    public function search(Request $request): JsonResponse
    {
        try {
            $query = State::select('id', 'name', 'country_id')
                ->with('country:id,name,code')
                ->where('status', 'active');

            // Search functionality
            if ($request->has('q') && $request->q) {
                $search = $request->q;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhereHas('country', function ($countryQuery) use ($search) {
                          $countryQuery->where('name', 'LIKE', "%{$search}%");
                      });
                });
            }

            // Pagination for search results
            $perPage = $request->get('per_page', 20);
            $states = $query->orderBy('name', 'asc')->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $states->items(),
                'pagination' => [
                    'current_page' => $states->currentPage(),
                    'last_page' => $states->lastPage(),
                    'per_page' => $states->perPage(),
                    'total' => $states->total()
                ],
                'message' => 'States search completed successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error searching states: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Public index method for states (no authentication required)
     * Supports pagination and filtering for public access
     */
    public function publicIndex(Request $request): JsonResponse
    {
        try {
            $query = State::select('id', 'name', 'country_id', 'status', 'image', 'created_at', 'updated_at')
                ->with('country:id,name,code')
                ->where('status', 'active'); // Only show active states for public access

            // Search functionality
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'LIKE', "%{$search}%")
                      ->orWhereHas('country', function ($countryQuery) use ($search) {
                          $countryQuery->where('name', 'LIKE', "%{$search}%");
                      });
                });
            }

            // Filter by country
            if ($request->has('country_id') && $request->country_id) {
                $query->where('country_id', $request->country_id);
            }

            // Sorting
            $sortBy = $request->get('sort_by', 'name');
            $sortOrder = $request->get('sort_order', 'asc');
            
            // Only allow sorting by safe columns
            $allowedSortColumns = ['name', 'created_at', 'updated_at'];
            if (!in_array($sortBy, $allowedSortColumns)) {
                $sortBy = 'name';
            }
            
            $query->orderBy($sortBy, $sortOrder);

            // Pagination
            $perPage = $request->get('per_page', 15);
            $perPage = min($perPage, 100); // Limit max per page for public endpoint
            $states = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $states->items(),
                'pagination' => [
                    'current_page' => $states->currentPage(),
                    'last_page' => $states->lastPage(),
                    'per_page' => $states->perPage(),
                    'total' => $states->total()
                ],
                'message' => 'States retrieved successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving states: ' . $e->getMessage()
            ], 500);
        }
    }
}
