
import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { useTranslation } from '@/hooks/useTranslation';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Textarea
} from '@/components/ui/textarea';
import {
  Plus,
  Search,
  MoreHorizontal,
  Edit,
  Trash2,
  MapPin,
  Globe,
  Navigation,
  ChevronLeft,
  ChevronRight,
  Eye,
  EyeOff,
  Download,
  X
} from 'lucide-react';
import Swal from 'sweetalert2';
import { stateAPI } from '../../services/stateAPI';
import { countryAPI } from '../../services/countryAPI';

const StatePage = () => {
  const { t } = useTranslation();
  
  // Add error boundary for this component
  const [componentError, setComponentError] = useState(null);
  
  // Add fallback for translation function
  const translate = (key) => {
    try {
      return t ? t(key) : key;
    } catch (error) {
      console.warn('Translation error for key:', key);
      return key;
    }
  };

  // Add error handling
  React.useEffect(() => {
    const handleError = (error) => {
      setComponentError(error.message);
    };
    
    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  if (componentError) {
    return (
      <div className="p-6">
        <h2 className="text-2xl font-bold mb-4">State Management</h2>
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">Error: {componentError}</p>
          <button 
            onClick={() => setComponentError(null)} 
            className="mt-2 px-4 py-2 bg-red-600 text-white rounded"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }
  
  // State management
  const [states, setStates] = useState([]);
  const [countries, setCountries] = useState([]);
  const [countriesLoading, setCountriesLoading] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCountry, setSelectedCountry] = useState('all');
  const [selectedType, setSelectedType] = useState('all');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalItems, setTotalItems] = useState(0);
  const [itemsPerPage] = useState(15);
  
  // Dialog state
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingState, setEditingState] = useState(null);
  const [formData, setFormData] = useState({
    name: '',
    country_id: '',
    status: 'active',
    image: null
  });
  const [formErrors, setFormErrors] = useState({});
  const [submitLoading, setSubmitLoading] = useState(false);
  
  // Statistics
  const [statistics, setStatistics] = useState({
    total_states: 0,
    active_states: 0,
    inactive_states: 0,
    states_by_country: [],
    states_by_type: {}
  });

  // Load data on component mount
  useEffect(() => {
    console.log('StatePage: Component mounted, loading data...');
    try {
      loadStates();
      loadCountries();
      loadStatistics();
    } catch (error) {
      console.error('StatePage: Error in useEffect:', error);
      setComponentError(error.message);
    }
  }, [currentPage, searchTerm, selectedCountry, selectedType, selectedStatus]);

  // Load states
  const loadStates = async () => {
    try {
      setLoading(true);
      const params = {
        page: currentPage,
        per_page: itemsPerPage,
        search: searchTerm,
        country_id: selectedCountry === 'all' ? '' : selectedCountry,
        type: selectedType === 'all' ? '' : selectedType,
        status: selectedStatus === 'all' ? '' : selectedStatus
      };
      
      // Try public paginated endpoint first, fallback to dropdown or authenticated endpoint
      let response = null;
      let endpoint = '';
      
      try {
        console.log('StatePage: Trying public states index endpoint...');
        endpoint = 'public.getStates';
        response = await stateAPI.public.getStates(params);
        console.log('StatePage: Public states index response:', response);
      } catch (publicError) {
        console.warn('StatePage: Public states index endpoint failed, trying dropdown:', publicError);
        try {
          endpoint = 'public.getStatesDropdown';
          response = await stateAPI.public.getStatesDropdown(params);
          console.log('StatePage: Public states dropdown response:', response);
        } catch (dropdownError) {
          console.warn('StatePage: Public dropdown failed, trying authenticated:', dropdownError);
          endpoint = 'getStates';
          response = await stateAPI.getStates(params);
        }
      }
      
      // Handle the response structure safely
      if (response.success && response.data) {
        setStates(response.data || []);
        if (response.pagination) {
          setTotalPages(response.pagination.last_page || 1);
          setTotalItems(response.pagination.total || 0);
        } else {
          // For dropdown endpoints without pagination
          setTotalPages(1);
          setTotalItems(response.data.length || 0);
        }
        console.log(`StatePage: Loaded ${response.data.length} states using ${endpoint}`);
      } else {
        console.warn('StatePage: Response not successful:', response);
        setStates([]);
        setTotalPages(1);
        setTotalItems(0);
      }
      setError(null);
    } catch (err) {
      setError(translate('state.messages.loadError'));
      console.error('StatePage: Error loading states:', err);
      console.error('StatePage: Error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });
      setStates([]);
      setTotalPages(1);
      setTotalItems(0);
    } finally {
      setLoading(false);
    }
  };

  // Load countries for dropdown
  const loadCountries = async () => {
    try {
      setCountriesLoading(true);
      console.log('StatePage: Loading countries...');
      console.log('StatePage: Base URL:', window.location.origin);
      
      // Try multiple endpoints as fallback
      let response = null;
      let endpoint = '';
      
      try {
        // First try the public endpoint
        endpoint = 'public.getCountriesDropdown';
        console.log('StatePage: Trying public endpoint...');
        response = await countryAPI.public.getCountriesDropdown();
      } catch (publicError) {
        console.warn('StatePage: Public endpoint failed:', publicError);
        
        try {
          // Fallback to authenticated endpoint
          endpoint = 'getCountriesDropdown';
          console.log('StatePage: Trying authenticated endpoint...');
          response = await countryAPI.getCountriesDropdown();
        } catch (authError) {
          console.warn('StatePage: Authenticated endpoint failed:', authError);
          throw authError;
        }
      }
      
      console.log('StatePage: Countries response from', endpoint, ':', response);
      
      if (response && response.success && response.data) {
        setCountries(response.data || []);
        console.log('StatePage: Countries loaded successfully:', response.data.length, 'countries');
        console.log('StatePage: First few countries:', response.data.slice(0, 3));
      } else {
        console.warn('StatePage: Countries response not successful:', response);
        setCountries([]);
      }
    } catch (err) {
      console.error('Error loading countries:', err);
      console.error('Error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });
      setCountries([]);
    } finally {
      setCountriesLoading(false);
    }
  };

  // Load statistics
  const loadStatistics = async () => {
    try {
      const response = await stateAPI.getStatistics();
      if (response.success && response.data) {
        setStatistics({
          total_states: response.data.total_states || 0,
          active_states: response.data.active_states || 0,
          inactive_states: response.data.inactive_states || 0,
          states_by_country: response.data.states_by_country || [],
          states_by_type: response.data.states_by_type || {}
        });
      }
    } catch (err) {
      console.error('Error loading statistics:', err);
      setStatistics({
        total_states: 0,
        active_states: 0,
        inactive_states: 0,
        states_by_country: [],
        states_by_type: {}
      });
    }
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();
    setFormErrors({});
    setSubmitLoading(true);
    
    try {
      // Validate required fields on frontend
      const requiredFields = ['name', 'country_id', 'status'];
      const missingFields = requiredFields.filter(field => !formData[field] || formData[field] === '');
      
      if (missingFields.length > 0) {
        Swal.fire({
          icon: 'warning',
          title: 'Missing Required Fields',
          text: `Please fill in the following required fields: ${missingFields.join(', ')}`,
          confirmButtonText: 'OK'
        });
        return;
      }
      
      // Create FormData for file upload
      // Validate required fields before sending
      if (!formData.name?.trim()) {
        setFormErrors({name: ['Name is required']});
        return;
      }
      
      if (!formData.country_id || formData.country_id === '' || formData.country_id === 'no-data' || formData.country_id === 'loading') {
        setFormErrors({country_id: ['Please select a country']});
        return;
      }
      
      if (!formData.status) {
        setFormErrors({status: ['Status is required']});
        return;
      }

      // Convert country_id to integer and validate
      const countryId = parseInt(formData.country_id);
      if (isNaN(countryId) || countryId <= 0) {
        setFormErrors({country_id: ['Invalid country selection']});
        return;
      }

      // Prepare form data based on whether we have a file upload or not
      let dataToSend;
      let isFileUpload = false;
      
      // Check if we have a file to upload
      if (formData.image && 
          formData.image instanceof File && 
          formData.image.size > 0) {
        // Use FormData for file uploads
        dataToSend = new FormData();
        dataToSend.append('name', formData.name.trim());
        dataToSend.append('country_id', countryId);
        dataToSend.append('status', formData.status);
        dataToSend.append('image', formData.image);
        
        // Add _method for Laravel to handle PUT with FormData
        if (editingState) {
          dataToSend.append('_method', 'PUT');
        }
        
        isFileUpload = true;
        console.log('StatePage: Using FormData for file upload');
      } else {
        // Use regular JSON for updates without files
        dataToSend = {
          name: formData.name.trim(),
          country_id: countryId,
          status: formData.status
        };
        
        console.log('StatePage: Using JSON data (no file upload)');
      }
      
      // Debug form data being sent
      console.log('StatePage: Form data being sent:', {
        name: formData.name,
        country_id: formData.country_id,
        country_id_parsed: countryId,
        status: formData.status,
        image: formData.image,
        isFileUpload: isFileUpload
      });
      
      console.log('StatePage: EditingState:', editingState);
      console.log('StatePage: Countries available:', countries.length, countries.slice(0, 3));
      
      // Debug data being sent
      console.log('StatePage: Data to send:', dataToSend);
      if (isFileUpload) {
        console.log('StatePage: FormData contents:');
        for (let pair of dataToSend.entries()) {
          console.log(pair[0] + ': ' + (pair[1] instanceof File ? `File(${pair[1].name})` : pair[1]));
        }
      }
      
      let response;
      if (editingState) {
        if (isFileUpload) {
          // Use POST with _method override for file uploads
          response = await stateAPI.updateStateWithFile(editingState.id, dataToSend);
        } else {
          // Use regular PUT for JSON updates
          response = await stateAPI.updateState(editingState.id, dataToSend);
        }
        Swal.fire({
          icon: 'success',
          title: t('common.messages.success'),
          text: t('state.messages.updateSuccess')
        });
      } else {
        response = await stateAPI.createState(dataToSend);
        Swal.fire({
          icon: 'success',
          title: t('common.messages.success'),
          text: t('state.messages.createSuccess')
        });
      }
      
      setIsDialogOpen(false);
      resetForm();
      loadStates();
      loadStatistics();
    } catch (err) {
      console.error('StatePage: Submit error:', err);
      console.error('StatePage: Error response:', err.response?.data);
      console.error('StatePage: Error status:', err.response?.status);
      
      if (err.response?.status === 401) {
        Swal.fire({
          icon: 'error',
          title: 'Authentication Error',
          text: 'You are not authenticated. Please log in again.'
        });
      } else if (err.response?.status === 403) {
        Swal.fire({
          icon: 'error',
          title: 'Permission Error',
          text: 'You do not have permission to create states. Contact your administrator.'
        });
      } else if (err.response?.data?.errors) {
        setFormErrors(err.response.data.errors);
        console.log('StatePage: Validation errors:', err.response.data.errors);
        
        // Show validation errors in a more user-friendly way
        const errorMessages = Object.entries(err.response.data.errors)
          .map(([field, messages]) => `${field}: ${messages.join(', ')}`)
          .join('\n');
        
        Swal.fire({
          icon: 'error',
          title: 'Validation Error',
          text: errorMessages,
          confirmButtonText: 'OK'
        });
      } else {
        Swal.fire({
          icon: 'error',
          title: 'Error',
          text: err.response?.data?.message || err.message || 'An unexpected error occurred'
        });
      }
    } finally {
      setSubmitLoading(false);
    }
  };

  // Handle delete
  const handleDelete = async (state) => {
    const result = await Swal.fire({
      title: t('common.messages.confirmDelete'),
      text: t('state.messages.confirmDelete'),
      icon: 'warning',
      showCancelButton: true,
      confirmButtonColor: '#d33',
      cancelButtonColor: '#3085d6',
      confirmButtonText: t('common.buttons.delete'),
      cancelButtonText: t('common.buttons.cancel')
    });

    if (result.isConfirmed) {
      try {
        await stateAPI.deleteState(state.id);
        Swal.fire({
          icon: 'success',
          title: t('common.messages.success'),
          text: t('state.messages.deleteSuccess')
        });
        loadStates();
        loadStatistics();
      } catch (err) {
        Swal.fire({
          icon: 'error',
          title: t('common.messages.error'),
          text: t('state.messages.deleteError')
        });
      }
    }
  };

  // Handle edit
  const handleEdit = (state) => {
    console.log('StatePage: Editing state:', state);
    console.log('StatePage: State country_id:', state.country_id, 'type:', typeof state.country_id);
    
    setEditingState(state);
    
    // Ensure country_id is properly handled
    let countryId = '';
    if (state.country_id !== null && state.country_id !== undefined) {
      countryId = state.country_id.toString();
    } else if (state.country && state.country.id) {
      countryId = state.country.id.toString();
    }
    
    console.log('StatePage: Setting country_id to:', countryId);
    
    setFormData({
      name: state.name || '',
      code: state.code || '',
      type: state.type || '',
      country_id: countryId,
      capital: state.capital || '',
      timezone: state.timezone || '',
      latitude: state.latitude || '',
      longitude: state.longitude || '',
      description: state.description || '',
      status: state.status || 'active'
    });
    setIsDialogOpen(true);
    // Ensure countries are loaded when dialog opens
    if (countries.length === 0) {
      console.log('StatePage: No countries loaded, loading now...');
      loadCountries();
    }
  };

  // Handle toggle status
  const handleToggleStatus = async (state) => {
    try {
      await stateAPI.toggleStatus(state.id);
      loadStates();
      loadStatistics();
    } catch (err) {
      Swal.fire({
        icon: 'error',
        title: t('common.messages.error'),
        text: t('state.messages.updateError')
      });
    }
  };

  // Reset form
  const resetForm = () => {
    setFormData({
      name: '',
      country_id: '',
      status: 'active',
      image: null
    });
    setFormErrors({});
    setEditingState(null);
    
    // Reset file input
    const fileInput = document.getElementById('image');
    if (fileInput) {
      fileInput.value = '';
    }
  };

  // Handle add new state
  const handleAddNew = () => {
    resetForm();
    setIsDialogOpen(true);
    // Ensure countries are loaded when dialog opens
    if (countries.length === 0) {
      console.log('StatePage: No countries loaded, loading now...');
      loadCountries();
    }
  };

  // Handle search
  const handleSearch = (e) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1);
  };

  // Test authentication and permissions
  const testAuthAndPermissions = async () => {
    try {
      console.log('Testing authentication...');
      const token = localStorage.getItem('auth_token');
      console.log('Auth token exists:', !!token);
      console.log('Auth token preview:', token ? token.substring(0, 20) + '...' : 'No token');
      
      // Test if we can access a protected endpoint
      const response = await stateAPI.getStates({ page: 1, per_page: 1 });
      console.log('Auth test successful:', response);
      
      Swal.fire({
        icon: 'success',
        title: 'Authentication Test',
        html: `
          <p><strong>Status:</strong> Authenticated ✅</p>
          <p><strong>Token:</strong> ${token ? 'Present' : 'Missing'}</p>
          <p><strong>States access:</strong> Allowed</p>
        `,
        confirmButtonText: 'OK'
      });
    } catch (error) {
      console.error('Auth test failed:', error);
      Swal.fire({
        icon: 'error',
        title: 'Authentication Test Failed',
        html: `
          <p><strong>Error:</strong> ${error.message}</p>
          <p><strong>Status:</strong> ${error.response?.status || 'Unknown'}</p>
          <p><strong>Response:</strong> ${JSON.stringify(error.response?.data || {}, null, 2)}</p>
        `,
        confirmButtonText: 'OK'
      });
    }
  };

  // State types
  const stateTypes = [
    { value: 'state', label: t('state.types.state') },
    { value: 'province', label: t('state.types.province') },
    { value: 'territory', label: t('state.types.territory') },
    { value: 'region', label: t('state.types.region') },
    { value: 'district', label: t('state.types.district') },
    { value: 'federal_district', label: t('state.types.federal_district') }
  ];

  if (loading && (!states || states.length === 0)) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">{t('state.title')}</h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{t('state.title')}</h2>
          <p className="text-muted-foreground">{t('state.description')}</p>
        </div>
        <div className="flex gap-2">
          <Button 
            onClick={loadCountries} 
            variant="outline" 
            className="flex items-center gap-2"
            disabled={countriesLoading}
          >
            <Globe className="h-4 w-4" />
            {countriesLoading ? 'Loading...' : `Test Countries (${countries.length})`}
          </Button>
          <Button 
            onClick={async () => {
              try {
                const token = localStorage.getItem('auth_token');
                console.log('Auth token exists:', !!token);
                console.log('Auth token preview:', token ? token.substring(0, 10) + '...' : 'None');
                
                const response = await fetch('/api/auth/permissions', {
                  headers: {
                    'Authorization': `Bearer ${token}`,
                    'Accept': 'application/json'
                  }
                });
                const data = await response.json();
                console.log('User permissions:', data);
                alert(`Auth: ${!!token}, Permissions: ${JSON.stringify(data, null, 2)}`);
              } catch (error) {
                console.error('Permission check error:', error);
                alert('Error checking permissions: ' + error.message);
              }
            }} 
            variant="outline" 
            className="flex items-center gap-2"
          >
            <Navigation className="h-4 w-4" />
            Check Auth & Permissions
          </Button>
          <Button onClick={handleAddNew} className="flex items-center gap-2">
            <Plus className="h-4 w-4" />
            {t('state.addState')}
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-blue-800">{t('state.statistics.totalStates')}</CardTitle>
            <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
              <MapPin className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-900">{statistics.total_states}</div>
            <p className="text-xs text-blue-600 mt-1">All registered states</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-green-800">{t('state.statistics.activeStates')}</CardTitle>
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <Eye className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-900">{statistics.active_states}</div>
            <p className="text-xs text-green-600 mt-1">Currently active states</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-red-800">{t('state.statistics.inactiveStates')}</CardTitle>
            <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
              <EyeOff className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-900">{statistics.inactive_states}</div>
            <p className="text-xs text-red-600 mt-1">Not currently active</p>
          </CardContent>
        </Card>
        
        <Card className="bg-gradient-to-r from-purple-50 to-purple-100 border-purple-200">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium text-purple-800">{t('state.statistics.statesByCountry')}</CardTitle>
            <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
              <Globe className="h-4 w-4 text-white" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-900">{(statistics.states_by_country || []).length}</div>
            <p className="text-xs text-purple-600 mt-1">Countries with states</p>
          </CardContent>
        </Card>
      </div>

      {/* Search and Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="text-lg">{t('state.searchTitle')}</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={t('state.searchPlaceholder')}
                value={searchTerm}
                onChange={handleSearch}
                className="pl-10"
              />
            </div>
            <Select value={selectedCountry} onValueChange={(value) => handleFilterChange('country', value === 'all' ? '' : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by Country" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Countries</SelectItem>
                {countries.map(country => (
                  <SelectItem key={country.id} value={country.id.toString()}>
                    {country.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedType} onValueChange={(value) => handleFilterChange('type', value === 'all' ? '' : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                {stateTypes.map(type => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <Select value={selectedStatus} onValueChange={(value) => handleFilterChange('status', value === 'all' ? '' : value)}>
              <SelectTrigger>
                <SelectValue placeholder="Filter by Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="inactive">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* States Table */}
      <Card>
        <CardHeader>
          <CardTitle>
            {t('state.title')} ({totalItems} {totalItems === 1 ? 'item' : 'items'})
          </CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <div className="mb-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-600">{error}</p>
            </div>
          )}
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Image</TableHead>
                  <TableHead>{t('state.fields.name')}</TableHead>
                  <TableHead>{t('state.fields.country')}</TableHead>
                  <TableHead>{t('state.fields.status')}</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {loading ? (
                  [...Array(5)].map((_, i) => (
                    <TableRow key={i}>
                      <TableCell><div className="h-10 w-10 bg-gray-200 rounded animate-pulse"></div></TableCell>
                      <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                      <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                      <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                      <TableCell><div className="h-4 bg-gray-200 rounded animate-pulse"></div></TableCell>
                    </TableRow>
                  ))
                ) : (!states || states.length === 0) ? (
                  <TableRow>
                    <TableCell colSpan={5} className="text-center py-8">
                      <div className="flex flex-col items-center gap-2">
                        <MapPin className="h-12 w-12 text-muted-foreground" />
                        <p className="text-muted-foreground">{t('common.messages.noData')}</p>
                      </div>
                    </TableCell>
                  </TableRow>
                ) : (
                  (states || []).map((state) => (
                    <TableRow key={state.id}>
                      {/* Image Column */}
                      <TableCell>
                        <div className="w-10 h-10 rounded-lg overflow-hidden bg-gray-100 flex items-center justify-center">
                          {state.image ? (
                            <img 
                              src={`/state/${state.image}`} 
                              alt={state.name}
                              className="w-full h-full object-cover cursor-pointer hover:opacity-80 transition-opacity"
                              onClick={() => {
                                // Create a simple image preview modal
                                Swal.fire({
                                  imageUrl: `/state/${state.image}`,
                                  imageAlt: state.name,
                                  title: state.name,
                                  text: `State image for ${state.name}`,
                                  showConfirmButton: false,
                                  showCloseButton: true,
                                  imageWidth: 400,
                                  imageHeight: 300,
                                  customClass: {
                                    image: 'rounded-lg'
                                  }
                                });
                              }}
                              onError={(e) => {
                                e.target.style.display = 'none';
                                e.target.nextSibling.style.display = 'flex';
                              }}
                            />
                          ) : null}
                          <div className={`w-full h-full flex items-center justify-center text-gray-400 text-xs ${state.image ? 'hidden' : 'flex'}`}>
                            <MapPin className="h-4 w-4" />
                          </div>
                        </div>
                      </TableCell>
                      {/* Name Column */}
                      <TableCell className="font-medium">{state.name}</TableCell>
                      {/* Country Column */}
                      <TableCell>{state.country?.name || '-'}</TableCell>
                      {/* Status Column */}
                      <TableCell>
                        <Badge
                          variant={state.status === 'active' ? 'default' : 'secondary'}
                          className={state.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                        >
                          {state.status === 'active' ? t('common.status.active') : t('common.status.inactive')}
                        </Badge>
                      </TableCell>
                      {/* Actions Column */}
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" className="h-8 w-8 p-0">
                              <span className="sr-only">Open menu</span>
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem onClick={() => handleEdit(state)}>
                              <Edit className="mr-2 h-4 w-4" />
                              {t('common.buttons.edit')}
                            </DropdownMenuItem>
                            <DropdownMenuItem onClick={() => handleToggleStatus(state)}>
                              {state.status === 'active' ? (
                                <><EyeOff className="mr-2 h-4 w-4" />Deactivate</>
                              ) : (
                                <><Eye className="mr-2 h-4 w-4" />Activate</>
                              )}
                            </DropdownMenuItem>
                            <DropdownMenuItem 
                              onClick={() => handleDelete(state)}
                              className="text-red-600"
                            >
                              <Trash2 className="mr-2 h-4 w-4" />
                              {t('common.buttons.delete')}
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between space-x-2 py-4">
              <div className="text-sm text-muted-foreground">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems} items
              </div>
              <div className="flex items-center space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  <ChevronLeft className="h-4 w-4" />
                  Previous
                </Button>
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    const pageNumber = i + 1;
                    return (
                      <Button
                        key={pageNumber}
                        variant={currentPage === pageNumber ? "default" : "outline"}
                        size="sm"
                        onClick={() => setCurrentPage(pageNumber)}
                      >
                        {pageNumber}
                      </Button>
                    );
                  })}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Add/Edit Modal */}
      {isDialogOpen && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg shadow-2xl w-full max-w-2xl max-h-[90vh] flex flex-col overflow-hidden">
            {/* Modal Header */}
            <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
              <div className="flex items-center space-x-3">
                <MapPin className="h-6 w-6" />
                <div>
                  <h3 className="text-lg font-semibold">
                    {editingState ? t('state.editState') : t('state.addState')}
                  </h3>
                  <p className="text-blue-100 text-sm">
                    {editingState ? 'Edit the state/province information' : 'Add a new state/province to the system'}
                  </p>
                </div>
              </div>
              <button
                onClick={() => setIsDialogOpen(false)}
                className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-blue-600/50"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Modal Body */}
            <div className="flex-1 overflow-y-auto p-6">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name Field */}
                <div>
                  <Label htmlFor="name" className="text-sm font-medium text-gray-700">
                    {t('state.fields.name')} *
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => setFormData({...formData, name: e.target.value})}
                    className={`mt-1 ${formErrors.name ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'}`}
                    placeholder="Enter state/province name"
                  />
                  {formErrors.name && (
                    <p className="text-sm text-red-600 mt-1">{formErrors.name[0]}</p>
                  )}
                </div>

                {/* Country Dropdown */}
                <div>
                  <Label htmlFor="country_id" className="text-sm font-medium text-gray-700">
                    {t('state.fields.country')} *
                  </Label>
                  <Select value={formData.country_id} onValueChange={(value) => setFormData({...formData, country_id: value})}>
                    <SelectTrigger className={`mt-1 ${formErrors.country_id ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'}`}>
                      <SelectValue placeholder={countriesLoading ? "Loading countries..." : "Select country"} />
                    </SelectTrigger>
                    <SelectContent>
                      {countriesLoading ? (
                        <SelectItem value="loading" disabled>Loading countries...</SelectItem>
                      ) : countries.length === 0 ? (
                        <SelectItem value="no-data" disabled>No countries available</SelectItem>
                      ) : (
                        countries.map(country => (
                          <SelectItem key={country.id} value={country.id.toString()}>
                            {country.name}
                          </SelectItem>
                        ))
                      )}
                    </SelectContent>
                  </Select>
                  {countriesLoading && (
                    <p className="text-sm text-blue-600 mt-1">Loading countries...</p>
                  )}
                  {!countriesLoading && countries.length === 0 && (
                    <p className="text-sm text-orange-600 mt-1">No countries loaded. Check console for errors.</p>
                  )}
                  {formErrors.country_id && (
                    <p className="text-sm text-red-600 mt-1">{formErrors.country_id[0]}</p>
                  )}
                </div>

                {/* Status Field */}
                <div>
                  <Label htmlFor="status" className="text-sm font-medium text-gray-700">
                    {t('state.fields.status')} *
                  </Label>
                  <Select value={formData.status} onValueChange={(value) => setFormData({...formData, status: value})}>
                    <SelectTrigger className={`mt-1 ${formErrors.status ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'}`}>
                      <SelectValue placeholder="Select status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">{t('common.status.active')}</SelectItem>
                      <SelectItem value="inactive">{t('common.status.inactive')}</SelectItem>
                    </SelectContent>
                  </Select>
                  {formErrors.status && (
                    <p className="text-sm text-red-600 mt-1">{formErrors.status[0]}</p>
                  )}
                </div>

                {/* Image Upload Field */}
                <div>
                  <Label htmlFor="image" className="text-sm font-medium text-gray-700">
                    State Image
                  </Label>
                  <div className="mt-1">
                    <Input
                      id="image"
                      type="file"
                      accept="image/*"
                      onChange={(e) => {
                        const file = e.target.files[0] || null;
                        console.log('StateModal: File selected:', file);
                        console.log('StateModal: File details:', file ? {
                          name: file.name,
                          size: file.size,
                          type: file.type
                        } : 'No file');
                        setFormData({...formData, image: file});
                      }}
                      className={`${formErrors.image ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'}`}
                    />
                    {formErrors.image && (
                      <p className="text-sm text-red-600 mt-1">{formErrors.image[0]}</p>
                    )}
                    {formData.image && formData.image.name && (
                      <p className="text-sm text-green-600 mt-1">
                        Selected: {formData.image.name} ({(formData.image.size / 1024).toFixed(2)} KB)
                      </p>
                    )}
                    {editingState && editingState.image && !formData.image && (
                      <p className="text-sm text-blue-600 mt-1">Current image: {editingState.image}</p>
                    )}
                  </div>
                </div>
              </form>
            </div>

            {/* Modal Footer */}
            <div className="bg-gray-50 px-6 py-4 flex justify-end space-x-3 border-t">
              <Button 
                type="button" 
                variant="outline" 
                onClick={() => setIsDialogOpen(false)}
                className="px-4 py-2"
              >
                {t('common.buttons.cancel')}
              </Button>
              <Button 
                type="submit" 
                disabled={submitLoading}
                onClick={handleSubmit}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white"
              >
                {submitLoading ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  editingState ? t('common.buttons.save') : t('common.buttons.add')
                )}
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default StatePage;
