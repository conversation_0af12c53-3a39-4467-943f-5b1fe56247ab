import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { DndProvider } from 'react-dnd';
import { HTML5Backend } from 'react-dnd-html5-backend';
import { ThemeProvider } from './theme/ThemeProvider';
import { AuthProvider } from '../contexts/AuthContext';
import TranslationProvider from './TranslationProvider';
import AuthGate from './auth/AuthGate';
import DashboardLayout from './layout/DashboardLayout';
import DashboardOverview from './dashboard/DashboardOverview';
import SettingsPage from './dashboard/SettingsPage';
import CustomerPage from './dashboard/customerPage';
import LandOwnersPage from './dashboard/LandOwnersPage';
import LandAcquisitionPage from './dashboard/LandAcquisitionPage';
import RolePage from './dashboard/RolePage';;
import PlaceholderPage from './dashboard/PlaceholderPage';
import EmployeePage from './dashboard/EmployeePage';
import ContractorPage from './dashboard/ContractorPage';
import VendorPage from './dashboard/VendorPage';
import AssignContractorPage from './dashboard/AssignContractorPage';
import AssignVendorPage from './dashboard/AssignVendorPage';
import AssignEmployeePage from './dashboard/AssignEmployeePage';
import PropertyAmenitiesPage from './dashboard/PropertyAmenitiesPage';
import TenantsPage from './dashboard/TenantsPage';
import ProjectPage from './dashboard/ProjectPage';
import InvoicePage from './dashboard/InvoicePage';
import ManageTypesPage from './dashboard/ManageTypesPage';
import Pages from './dashboard/Pages';
import PageEdit from './dashboard/pageEdit';
import FrontendSettings from './dashboard/FrontendSettings';
import BackendSettings from './dashboard/BackendSettings';
import Blog from './dashboard/Blog';
import Menu_Position from './dashboard/Menu_position';
import MenuManage from './dashboard/Menu_manage';

// Error Boundary Component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('React Error Boundary caught an error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: '20px', color: 'red', fontFamily: 'Arial' }}>
          <h1>Something went wrong!</h1>
          <p>Error: {this.state.error?.message}</p>
          <p>Check browser console for details.</p>
        </div>
      );
    }

    return this.props.children;
  }
}

export default function App() {
  return (
    <ErrorBoundary>
      <DndProvider backend={HTML5Backend}>
        <AuthProvider>
          <TranslationProvider>
            <ThemeProvider defaultTheme="system" storageKey="dashboard-theme">
              <AuthGate>
                <Router>
                <DashboardLayout>
                <Routes>
                  {/* Default route redirects to dashboard */}
                  <Route path="/" element={<Navigate to="/dashboard" replace />} />

                  {/* Dashboard routes */}
                <Route path="/dashboard" element={<DashboardOverview />} />
                <Route path="/admin/land-owners" element={<LandOwnersPage />} />
                <Route path="/admin/land-acquisition" element={<LandAcquisitionPage />} />
                <Route path="/admin/contractors" element={<ContractorPage />} />
                <Route path="/admin/vendors" element={<VendorPage />} />
                <Route path="/admin/assign-vendor" element={<AssignVendorPage />} />
                <Route path="/admin/assign-contractor" element={<AssignContractorPage />} />
                <Route path="/admin/assign-employee" element={<AssignEmployeePage />} />
                <Route path="/admin/property" element={<ProjectPage />} />
                <Route path="/admin/ManageTypes" element={<ManageTypesPage />} />
                <Route path="/admin/property-amenities" element={<PropertyAmenitiesPage />} />
                <Route path="/admin/invoice" element={<InvoicePage />} />
                <Route path='/admin/pages' element={<Pages />} />
                <Route path="/admin/employees" element={<EmployeePage />} />
                {/* <Route path='/widgets' element={<Widgets />} /> */}
                <Route path="/admin/customer" element={<CustomerPage />} />
                <Route path="/admin/tenants" element={<TenantsPage />} />
                <Route path='/admin/pageEdit/:id' element={<PageEdit />} />
                <Route path="/admin/role" element={<RolePage />} />
                <Route path="/admin/settings" element={<SettingsPage />} />
                <Route path="/admin/frontend-settings" element={<FrontendSettings />} />
                <Route path="/admin/backend-settings" element={<BackendSettings />} />
                <Route path="/admin/blog" element={<Blog />} />
                <Route path="/admin/menu" element={<Menu_Position />} />
                <Route path="/admin/menu-manage" element={<MenuManage />} />

                {/* Catch-all route for 404 */}
                <Route
                  path="*"
                  element={<PlaceholderPage title="Page Not Found" description="The page you're looking for doesn't exist." />}
                />
              </Routes>
            </DashboardLayout>
          </Router>
            </AuthGate>
        </ThemeProvider>
        </TranslationProvider>
      </AuthProvider>
      </DndProvider>
    </ErrorBoundary>
  );
}
