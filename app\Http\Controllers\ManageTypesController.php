<?php

namespace App\Http\Controllers;

use App\Models\PropertyType;
use App\Models\PropertyStatus;
use App\Models\PropertyStage;
use App\Models\UnitType;
use App\Models\PropertyService;
use App\Models\RentType;
use App\Models\LeaseType;
use App\Models\PaymentType;
use App\Models\PaymentStatus;
use App\Models\PaymentMethod;
use App\Models\VendorType;
use App\Models\PropertyAmenity;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;

class ManageTypesController extends Controller
{
    /**
     * Display a listing of the resource.
     */    
    public function index()
    {
        //
        try{
             $propertyTypes = PropertyType::all();
             $propertyStatuses = PropertyStatus::all();
             $propertyStages = PropertyStage::all();
             $unitTypes = UnitType::all();
             $propertyServices = PropertyService::all();
             $rentTypes = RentType::all();
             $leaseTypes = LeaseType::all();
             $paymentTypes = PaymentType::all();
             $paymentStatuses = PaymentStatus::all();
             $paymentMethods = PaymentMethod::all();
             $vendorTypes = VendorType::all();
             $propertyAmenities = PropertyAmenity::all();
             return response()->json([
                'success' => true,
                'data' => [
                    'propertyTypes' => $propertyTypes,
                    'propertyStatuses' => $propertyStatuses,
                    'propertyStages' => $propertyStages,
                    'unitTypes' => $unitTypes,
                    'propertyServices' => $propertyServices,
                    'rentTypes' => $rentTypes,
                    'leaseTypes' => $leaseTypes,
                    'paymentTypes' => $paymentTypes,
                    'paymentStatuses' => $paymentStatuses,
                    'paymentMethods' => $paymentMethods,
                    'vendorTypes' => $vendorTypes
                ]
            ]);
        }catch( \Exception $e ){
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve property types: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string',
            'is_active' => 'nullable|in:0,1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $validated = $validator->validated();

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        try {
            $createdItem = null;
            $message = '';

            switch ($validated['type']) {
                case 'Property_type':
                    // Remove 'type' as it's not fillable in PropertyType
                    unset($validated['type']);
                    $createdItem = PropertyType::create($validated);
                    $message = 'Property type created successfully';
                    break;
                case 'Property_status':
                    unset($validated['type']);
                    $createdItem = PropertyStatus::create($validated);
                    $message = 'Property status created successfully';
                    break;
                case 'Property_stage':
                    unset($validated['type']);
                    $createdItem = PropertyStage::create($validated);
                    $message = 'Property stage created successfully';
                    break;
                case 'Unit_type':
                    unset($validated['type']);
                    $createdItem = UnitType::create($validated);
                    $message = 'Unit type created successfully';
                    break;
                case 'Property_service':
                    unset($validated['type']);
                    $createdItem = PropertyService::create($validated);
                    $message = 'Property service created successfully';
                    break;
                case 'Rent_type':
                    unset($validated['type']);
                    $createdItem = RentType::create($validated);
                    $message = 'Rent type created successfully';
                    break;
                case 'Lease_type':
                    unset($validated['type']);
                    $createdItem = LeaseType::create($validated);
                    $message = 'Lease type created successfully';
                    break;
                case 'Payment_type':
                    unset($validated['type']);
                    $createdItem = PaymentType::create($validated);
                    $message = 'Payment type created successfully';
                    break;
                case 'Payment_status':
                    unset($validated['type']);
                    $createdItem = PaymentStatus::create($validated);
                    $message = 'Payment status created successfully';
                    break;
                case 'Payment_method':
                    unset($validated['type']);
                    $createdItem = PaymentMethod::create($validated);
                    $message = 'Payment method created successfully';
                    break;
                case 'Vendor_type':
                    unset($validated['type']);
                    $createdItem = VendorType::create($validated);
                    $message = 'Vendor type created successfully';
                    break;
                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Unsupported type: ' . $validated['type']
                    ], 400);
            }

            return response()->json([
                'success' => true,
                'data' => $createdItem,
                'message' => $message
            ], 201);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to create type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'type' => 'required|string',
            'is_active' => 'nullable|in:0,1'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $validated = $validator->validated();

        // Generate slug if not provided
        if (empty($validated['slug'])) {
            $validated['slug'] = Str::slug($validated['name']);
        }

        try {
            $updatedItem = null;
            $message = '';

            switch ($validated['type']) {
                case 'Property_type':
                    $updatedItem = PropertyType::findOrFail($id);
                    break;
                case 'Property_status':
                    $updatedItem = PropertyStatus::findOrFail($id);
                    break;
                case 'Property_stage':
                    $updatedItem = PropertyStage::findOrFail($id);
                    break;
                case 'Unit_type':
                    $updatedItem = UnitType::findOrFail($id);
                    break;
                case 'Property_service':
                    $updatedItem = PropertyService::findOrFail($id);
                    break;
                case 'Rent_type':
                    $updatedItem = RentType::findOrFail($id);
                    break;
                case 'Lease_type':
                    $updatedItem = LeaseType::findOrFail($id);
                    break;
                case 'Payment_type':
                    $updatedItem = PaymentType::findOrFail($id);
                    break;
                case 'Payment_status':
                    $updatedItem = PaymentStatus::findOrFail($id);
                    break;
                case 'Payment_method':
                    $updatedItem = PaymentMethod::findOrFail($id);
                    break;
                case 'Vendor_type':
                    $updatedItem = VendorType::findOrFail($id);
                    break;
                default:
                    return response()->json([
                        'success' => false,
                        'message' => 'Unsupported type: ' . $validated['type']
                    ], 400);
            }

            // Remove 'type' as it's not fillable in models
            unset($validated['type']);

            $updatedItem->update($validated);

            $message = ucfirst(str_replace('_', ' ', $validated['type'] ?? 'Type')) . ' updated successfully';

            return response()->json([
                'success' => true,
                'data' => $updatedItem,
                'message' => $message
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update type: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id, string $type)
    {
        try {
            // Map type to model class
            $models = [
                'Property_type' => PropertyType::class,
                'Property_status' => PropertyStatus::class,
                'Property_stage' => PropertyStage::class,
                'Unit_type' => UnitType::class,
                'Property_service' => PropertyService::class,
                'Rent_type' => RentType::class,
                'Lease_type' => LeaseType::class,
                'Payment_type' => PaymentType::class,
                'Payment_status' => PaymentStatus::class,
                'Payment_method' => PaymentMethod::class,
                'Vendor_type' => VendorType::class,
            ];


            if (!isset($models[$type])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unsupported type: ' . $type
                ], 400);
            }

            $modelClass = $models[$type];
            $item = $modelClass::find($id);

            if (!$item) {
                return response()->json([
                    'success' => false,
                    'message' => 'Item not found'
                ], 404);
            }

            $item->delete();
            $message = ucfirst(str_replace('_', ' ', $type)) . ' deleted successfully';

            return response()->json([
                'success' => true,
                'data' => $item,
                'message' => $message
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete type: ' . $e->getMessage()
            ], 500);
        }
    }

    public function PropertyTypeDropdown()
    {
        try {
            $propertyTypes = PropertyType::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $propertyTypes,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active property types: ' . $e->getMessage()
            ], 500);
        }
    }

    public function PropertyStatusDropdown()
    {
        try {
            $propertyStatuses = PropertyStatus::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $propertyStatuses,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active property statuses: ' . $e->getMessage()
            ], 500);
        }
    }

    public function PropertyStageDropdown()
    {
        try {
            $propertyStages = PropertyStage::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $propertyStages,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active property stages: ' . $e->getMessage()
            ], 500);
        }
    }

    public function UnitTypeDropdown()
    {
        try {
            $unitTypes = UnitType::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $unitTypes,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active unit types: ' . $e->getMessage()
            ], 500);
        }
    }

      public function PropertyServiceDropdown()
    {
        try {
            $propertyService = PropertyService::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $propertyService,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active unit types: ' . $e->getMessage()
            ], 500);
        }
    }


    public function RentTypeDropdown()
    {
        try {
            $rentTypes = RentType::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $rentTypes,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active rent types: ' . $e->getMessage()
            ], 500);
        }
    }


    public function LeaseTypeDropdown()
    {
        try {
            $leaseTypes = LeaseType::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $leaseTypes,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active lease types: ' . $e->getMessage()
            ], 500);
        }
    }

    public function PaymentTypeDropdown()
    {
        try {
            $paymentTypes = PaymentType::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $paymentTypes,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active payment types: ' . $e->getMessage()
            ], 500);
        }
    }

    public function PaymentMethodDropdown()
    {
        try {
            $paymentMethods = PaymentMethod::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $paymentMethods,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active payment methods: ' . $e->getMessage()
            ], 500);
        }
    }


    public function PaymentStatusDropdown()
    {
        try {
            $paymentStatuses = PaymentStatus::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $paymentStatuses,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active payment statuses: ' . $e->getMessage()
            ], 500);
        }
    }

   public function ToggleStatus(string $id, string $type)
    {
    
        try {
            // Map type to model class
            $models = [
                'Property_type' => PropertyType::class,
                'Property_status' => PropertyStatus::class,
                'Property_stage' => PropertyStage::class,
                'Unit_type' => UnitType::class,
                'Property_service' => PropertyService::class,
                'Rent_type' => RentType::class,
                'Lease_type' => LeaseType::class,
                'Payment_type' => PaymentType::class,
                'Payment_status' => PaymentStatus::class,
                'Payment_method' => PaymentMethod::class,
                'Vendor_type' => VendorType::class,
            ];

            
            if (!isset($models[$type])) {
                return response()->json([
                    'success' => false,
                    'message' => 'Unsupported type: ' . $type
                ], 400);
            }

            $modelClass = $models[$type];
            $item = $modelClass::find($id);

            if (!$item) {
                return response()->json([
                    'success' => false,
                    'message' => 'Item not found'
                ], 404);
            }

            $item->is_active = !$item->is_active;
           
            $item->save();
            $message = ucfirst(str_replace('_', ' ', $type)) . ' status toggled successfully';

            return response()->json([
                'success' => true,
                'data' => $item,
                'message' => $message
            ], 200);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to toggle status: ' . $e->getMessage()
            ], 500);
        }
    }

    public function VendorTypeDropdown()
    {
        try {
            $vendorTypes = VendorType::where('is_active', 1)->get();
            return response()->json([
                'success' => true,
                'data' => $vendorTypes,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve active vendor types: ' . $e->getMessage()
            ], 500);
        }
    }
    
}
