import React, { useState, useEffect } from "react";


const Slider = () => {
  const [sliders, setSliders] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchSliderData = async () => {
      try {
        setLoading(true);
        
        // Fetch page content data with page_id=1 and widget_id=1
        const response = await fetch('/api/frontend/page-contents?page_id=1&widget_id=1&paginate=false');
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        const result = await response.json();
        
        if (result.success && result.data && result.data.length > 0) {
        
          // Find the widget with widget_id = 1 (slider widget)
          const sliderWidget = result.data.find(item => item.widget_id === 1);
          
          if (sliderWidget && sliderWidget.pageContent) {
            const config = typeof sliderWidget.pageContent === 'string' 
              ? JSON.parse(sliderWidget.pageContent) 
              : sliderWidget.pageContent;
              
            // Extract sliders from config
            if (config && config.sliders && Array.isArray(config.sliders)) {
              setSliders(config.sliders);
            
            }
          }
        } else {
          console.log('No data found'); // Debug log
        }
      } catch (err) {
        console.error('Error fetching slider data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchSliderData();
  }, []);



  // Use fetched data if available, otherwise use default data
  const slidersToRender = sliders;

  if (loading) {
    return (
      <div className="banner-section2">
        <div className="d-flex justify-content-center align-items-center" style={{ minHeight: '400px' }}>
          <div className="spinner-border" role="status">
            <span className="sr-only">Loading slider data...</span>
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    console.warn('Slider API error, using default data:', error);
  }

  
  return (
    <>
      {/* Start Banner section */}
      <div className="banner-section2">
        <div className="banner2-swiper-pagination"></div>
        <div className="swiper home2-banner-slider">
          <div className="swiper-wrapper">
            {slidersToRender.map((slide, index) => (
              <div key={index} className="swiper-slide">
                <div
                  className="banner-wrapper"
                  style={{
                    backgroundImage: `linear-gradient(180deg, rgba(0, 0, 0, 0.5) 0%, rgba(0, 0, 0, 0.15) 100%), url(${slide.image })`
                  }}
                >
                  <div className="row">
                    <div className="col-lg-8">
                      <div className="banner-content">
                        <h1>{slide.title }</h1>
                        <p>
                          {slide.description}
                        </p>
                        <div className="banner-content-bottom">
                          <div className="view-dt-btn">
                            <a
                              href={slide.button_link }
                              className="primary-btn3"
                            >
                              {/* Search Icon */}
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="18"
                                height="18"
                                viewBox="0 0 18 18"
                                fill="currentColor"
                              >
                                <path d="M12.5 11h-.79l-.28-.27C12.41 9.59 13 8.11 13 6.5 13 2.91 10.09 0 6.5 0S0 2.91 0 6.5 2.91 13 6.5 13c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L17.49 16l-4.99-5zm-6 0C4.01 11 2 8.99 2 6.5S4.01 2 6.5 2 11 4.01 11 6.5 8.99 11 6.5 11z"/>
                              </svg>
                              {slide.button_text || "Find Property"}
                            </a>
                          </div>
                          <div className="rating">
                            <a href="#">
                              <div className="review-top">
                                <div className="logo">
                                  <img
                                    src={slide.ratingLogo }
                                    alt="Trustpilot Logo"
                                  />
                                </div>
                                <div className="star">
                                  <img
                                    src="/Frontend/assets/img/home1/icon/trustpilot-star.svg"
                                    alt="Star"
                                  />
                                </div>
                              </div>
                              <div className="content">
                                <ul>
                                  <li>
                                    Trust Rating <span>{slide.rating || "5.0"}</span>
                                  </li>
                                  <li>
                                    <span>{slide.reviews_count || "2348"}</span> Reviews
                                  </li>
                                </ul>
                              </div>
                            </a>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </>
  );
};

export default Slider;
