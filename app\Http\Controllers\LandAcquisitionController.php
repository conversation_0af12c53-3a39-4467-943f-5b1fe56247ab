<?php

namespace App\Http\Controllers;

use App\Models\LandAcquisition;
use Modules\LandOwners\Models\LandOwner;
use App\Models\LandAddress;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class LandAcquisitionController extends Controller
{
    /**
     * Display a listing of land acquisitions
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $query = LandAcquisition::with(['landOwner', 'landAddress']);

            // Search functionality
            if ($request->has('search') && !empty($request->search)) {
                $search = $request->search;
                $query->where(function($q) use ($search) {
                    $q->where('dag_number', 'like', "%{$search}%")
                      ->orWhere('khatian_number', 'like', "%{$search}%")
                      ->orWhere('mauza', 'like', "%{$search}%")
                      ->orWhereHas('landOwner', function($landOwnerQuery) use ($search) {
                          $landOwnerQuery->where('first_name', 'like', "%{$search}%")
                                        ->orWhere('last_name', 'like', "%{$search}%");
                      });
                });
            }

            // Pagination
            $perPage = $request->get('per_page', 10);
            $landAcquisitions = $query->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $landAcquisitions
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching land acquisitions: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created land acquisition
     */
    public function store(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Validate request
            $validated = $request->validate([
                'dag_number' => 'required|string|max:255|unique:land_acquisitions',
                'khatian_number' => 'required|string|max:255',
                'mauza' => 'nullable|string|max:255',
                'land_size' => 'required|numeric|min:0',
                'acquisition_price' => 'required|numeric|min:0',
                'cs_khatian' => 'nullable|string|max:255',
                'rs_khatian' => 'nullable|string|max:255',
                'bs_khatian' => 'nullable|string|max:255',
                'sa_khatian' => 'nullable|string|max:255',
                
                // Land owner fields
                'land_owner_id' => 'nullable|exists:land_owners,id',
                'first_name' => 'required_without:land_owner_id|string|max:255',
                'last_name' => 'required_without:land_owner_id|string|max:255',
                'father_name' => 'required_without:land_owner_id|string|max:255',
                'mother_name' => 'nullable|string|max:255',
                'phone' => 'nullable|string|max:20',
                'nid_number' => 'nullable|string|max:20',
                'email' => 'nullable|email|max:255',
                'address' => 'required_without:land_owner_id|string',
                'document_type' => 'nullable|in:nid,passport',
                
                // Files
                'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:10240',
                'nid_front' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:10240',
                'nid_back' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:10240',
                'passport_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:10240',
                
                // Land address
                'land_address.country_id' => 'nullable|exists:countries,id',
                'land_address.state_id' => 'nullable|exists:states,id',
                'land_address.city' => 'nullable|string|max:255',
                'land_address.specific_address' => 'nullable|string',
            ]);

            // Handle land owner
            if (!empty($validated['land_owner_id'])) {
                $landOwnerId = $validated['land_owner_id'];
            } else {
                // Create new land owner
                $landOwnerData = [
                    'first_name' => $validated['first_name'],
                    'last_name' => $validated['last_name'],
                    'father_name' => $validated['father_name'],
                    'mother_name' => $validated['mother_name'] ?? null,
                    'phone' => $validated['phone'] ?? null,
                    'nid_number' => $validated['nid_number'] ?? null,
                    'email' => $validated['email'] ?? null,
                    'address' => $validated['address'],
                    'document_type' => $validated['document_type'] ?? null,
                ];

                // Handle file uploads
                if ($request->hasFile('photo')) {
                    $landOwnerData['photo'] = $this->uploadFile($request->file('photo'), 'photos');
                }
                if ($request->hasFile('nid_front')) {
                    $landOwnerData['nid_front'] = $this->uploadFile($request->file('nid_front'), 'documents');
                }
                if ($request->hasFile('nid_back')) {
                    $landOwnerData['nid_back'] = $this->uploadFile($request->file('nid_back'), 'documents');
                }
                if ($request->hasFile('passport_photo')) {
                    $landOwnerData['passport_photo'] = $this->uploadFile($request->file('passport_photo'), 'documents');
                }

                $landOwner = LandOwner::create($landOwnerData);
                $landOwnerId = $landOwner->id;
            }

            // Create land acquisition
            $landAcquisition = LandAcquisition::create([
                'dag_number' => $validated['dag_number'],
                'khatian_number' => $validated['khatian_number'],
                'mauza' => $validated['mauza'],
                'land_size' => $validated['land_size'],
                'acquisition_price' => $validated['acquisition_price'],
                'cs_khatian' => $validated['cs_khatian'] ?? null,
                'rs_khatian' => $validated['rs_khatian'] ?? null,
                'bs_khatian' => $validated['bs_khatian'] ?? null,
                'sa_khatian' => $validated['sa_khatian'] ?? null,
                'land_owner_id' => $landOwnerId,
            ]);

            // Create land address if provided
            if (!empty($validated['land_address'])) {
                $landAddressData = array_filter($validated['land_address']);
                if (!empty($landAddressData)) {
                    $landAddressData['land_acquisition_id'] = $landAcquisition->id;
                    LandAddress::create($landAddressData);
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Land acquisition created successfully',
                'data' => $landAcquisition->load(['landOwner', 'landAddress'])
            ], 201);

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Error creating land acquisition: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified land acquisition
     */
    public function show(LandAcquisition $landAcquisition): JsonResponse
    {
        try {
            $landAcquisition->load(['landOwner', 'landAddress']);
            
            return response()->json([
                'success' => true,
                'data' => $landAcquisition
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching land acquisition: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified land acquisition
     */
    public function update(Request $request, LandAcquisition $landAcquisition): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Validate request
            $validated = $request->validate([
                'dag_number' => 'sometimes|required|string|max:255|unique:land_acquisitions,dag_number,' . $landAcquisition->id,
                'khatian_number' => 'sometimes|required|string|max:255',
                'mauza' => 'nullable|string|max:255',
                'land_size' => 'sometimes|required|numeric|min:0',
                'acquisition_price' => 'sometimes|required|numeric|min:0',
                'cs_khatian' => 'nullable|string|max:255',
                'rs_khatian' => 'nullable|string|max:255',
                'bs_khatian' => 'nullable|string|max:255',
                'sa_khatian' => 'nullable|string|max:255',
                
                // Land owner fields
                'first_name' => 'sometimes|required|string|max:255',
                'last_name' => 'sometimes|required|string|max:255',
                'father_name' => 'sometimes|required|string|max:255',
                'mother_name' => 'nullable|string|max:255',
                'phone' => 'nullable|string|max:20',
                'nid_number' => 'nullable|string|max:20',
                'email' => 'nullable|email|max:255',
                'address' => 'sometimes|required|string',
                'document_type' => 'nullable|in:nid,passport',
                
                // Files
                'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:10240',
                'nid_front' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:10240',
                'nid_back' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:10240',
                'passport_photo' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp|max:10240',
                
                // Land address
                'land_address.country_id' => 'nullable|exists:countries,id',
                'land_address.state_id' => 'nullable|exists:states,id',
                'land_address.city' => 'nullable|string|max:255',
                'land_address.specific_address' => 'nullable|string',
            ]);

            // Update land acquisition
            $landAcquisitionData = array_intersect_key($validated, array_flip([
                'dag_number', 'khatian_number', 'mauza', 'land_size', 'acquisition_price',
                'cs_khatian', 'rs_khatian', 'bs_khatian', 'sa_khatian'
            ]));
            
            if (!empty($landAcquisitionData)) {
                $landAcquisition->update($landAcquisitionData);
            }

            // Update land owner if fields are provided
            $landOwnerData = array_intersect_key($validated, array_flip([
                'first_name', 'last_name', 'father_name', 'mother_name', 'phone',
                'nid_number', 'email', 'address', 'document_type'
            ]));

            // Handle file uploads for land owner
            if ($request->hasFile('photo')) {
                if ($landAcquisition->landOwner->photo) {
                    $this->deleteFile($landAcquisition->landOwner->photo);
                }
                $landOwnerData['photo'] = $this->uploadFile($request->file('photo'), 'photos');
            }

            if (!empty($landOwnerData)) {
                $landAcquisition->landOwner->update($landOwnerData);
            }

            // Update land address
            if (!empty($validated['land_address'])) {
                $landAddressData = array_filter($validated['land_address']);
                if (!empty($landAddressData)) {
                    $landAddressData['land_acquisition_id'] = $landAcquisition->id;
                    $landAcquisition->landAddress()->updateOrCreate(
                        ['land_acquisition_id' => $landAcquisition->id],
                        $landAddressData
                    );
                }
            }

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Land acquisition updated successfully',
                'data' => $landAcquisition->load(['landOwner', 'landAddress'])
            ]);

        } catch (\Illuminate\Validation\ValidationException $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $e->errors()
            ], 422);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Error updating land acquisition: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Remove the specified land acquisition
     */
    public function destroy(LandAcquisition $landAcquisition): JsonResponse
    {
        try {
            DB::beginTransaction();

            // Delete related land address
            if ($landAcquisition->landAddress) {
                $landAcquisition->landAddress->delete();
            }

            // Delete the land acquisition
            $landAcquisition->delete();

            DB::commit();

            return response()->json([
                'success' => true,
                'message' => 'Land acquisition deleted successfully'
            ]);

        } catch (\Exception $e) {
            DB::rollback();
            return response()->json([
                'success' => false,
                'message' => 'Error deleting land acquisition: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get statistics for land acquisitions
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = LandAcquisition::getStatistics();

            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error fetching statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload file helper
     */
    private function uploadFile($file, $folder): string
    {
        $filename = time() . '_' . Str::random(10) . '.' . $file->getClientOriginalExtension();
        $path = "landowners/{$folder}/" . $filename;

        // Store in public disk
        Storage::disk('public')->put($path, file_get_contents($file));

        return "/storage/{$path}";
    }

    /**
     * Delete file helper
     */
    private function deleteFile($filePath): void
    {
        if ($filePath && Storage::disk('public')->exists(str_replace('/storage/', '', $filePath))) {
            Storage::disk('public')->delete(str_replace('/storage/', '', $filePath));
        }
    }
}
