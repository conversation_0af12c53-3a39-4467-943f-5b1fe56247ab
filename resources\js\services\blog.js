import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

// create axios instance with default config
const instance = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// add request interceptor to add token to headers
api.interceptors.request.use(
    (config) => {
        const token =locaStorage.getItem('auth_token');
        if(token){
            config.headers.Authorization=`Bearer${token}`;
        }
        return config
    },

    (error)=>{
        return Promise.reject(error);
    });

    class BlogAPI{
        constructor(){
            this.baseUrl='/blogs';
        }
        async getBlogs(){
            try{
                const response=await instance.get(this.baseUrl);
                return response.data;
            }catch(error){
                throw error;
            }
        }

        async getBlog(id){
            try{
                const response=await instance.get(`${this.baseUrl}/${id}`);
                return response.data;
            }catch(error){
                throw error;
            }
        }

        async createBlog(blog){
            try{
                const response=await instance.post(this.baseUrl,blog);
                return response.data;
            }catch(error){
                throw error;
            }
        }

        async updateBlog(id,blog){
            try{
                const response=await instance.put(`${this.baseUrl}/${id}`,blog);
                return response.data;
            }catch(error){
                throw error;
            }
        }
        async deleteBlog(id){
            try{
                const response=await instance.delete(`${this.baseUrl}/${id}`);
                return response.data;
            }catch(error){
                throw error;
            }
        }
    }
