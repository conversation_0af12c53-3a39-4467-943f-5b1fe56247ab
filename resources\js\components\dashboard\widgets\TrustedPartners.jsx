import React, { useState, useEffect } from "react";

const TrustedPartners = ({ widget, handleConfigUpdate, handleSubmit }) => {
  const items = widget.config?.items || [
    { partnersLogo: "" },
  ];

  const addItem = () => {
    const newItems = [
      ...items,
      { partnersLogo: "" },
    ];
    handleConfigUpdate(widget.id, "items", newItems);
  };

  const removeItem = (index) => {
    const newItems = items.filter((_, i) => i !== index);
    handleConfigUpdate(widget.id, "items", newItems);
  };

  const updateItem = (index, field, value) => {
    const newItems = [...items];
    newItems[index][field] = value;
    handleConfigUpdate(widget.id, "items", newItems);
  };

  const handlePartnersLogoChange = async (e, index) => {
    const file = e.target.files[0];
    if (file) {
      const formData = new FormData();
      formData.append("image", file);

      try {
        const response = await fetch("/api/page-contents/upload-image", {
          method: "POST",
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          updateItem(index, "partnersLogo", data.data.url);
        } else {
          alert("Partner logo upload failed: " + data.message);
        }
      } catch (error) {
        alert("Error uploading Partner logo: " + error.message);
      }
    }
  };

  const handleRemoveLogo = (index) => {
    updateItem(index, "partnersLogo", "");
  };
    
  return (
    <form onSubmit={handleSubmit} className="space-y-4 w-full">
      {/* Items */}
      <div className="space-y-6">
        {items.map((item, index) => (
          <div key={index} className="border p-4">
            <div className="flex items-center gap-4 w-full">
              <div className="flex-1">
                <label className="block text-sm font-medium text-gray-700">
                  Partners Logo
                </label>
                <input
                  type="file"
                  accept="image/*"
                  className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                  onChange={(e) => handlePartnersLogoChange(e, index)}
                />
              </div>
              {item.partnersLogo && (
                <div className="flex items-center gap-2">
                  <img
                    src={item.partnersLogo}
                    alt="Partners Logo Preview"
                    className="w-16 h-16 object-cover rounded-md border"
                  />
                  <button
                    type="button"
                    onClick={() => handleRemoveLogo(index)}
                    className="px-2 py-1 bg-red-500 text-white rounded"
                  >
                    Remove
                  </button>
                </div>
              )}
            </div>
            
          </div>
        ))}

        {/* Add Item */}
        <button
          type="button"
          className="px-4 py-2 bg-blue-500 text-white rounded"
          onClick={addItem}
        >
          Add Item
        </button>
      </div>
    </form>
  );
};

export default TrustedPartners;
