<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class PropertyTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $propertyTypes = [
            [
                'name' => 'Residential',
                'slug' => 'residential',
                'description' => 'Residential property type',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name' => 'Commercial',
                'slug' => 'commercial',
                'description' => 'Commercial property type',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name' => 'Industrial',
                'slug' => 'industrial',
                'description' => 'Industrial property type',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name' => 'Land',
                'slug' => 'land',
                'description' => 'Land property type',
                'is_active' => true,
                'sort_order' => 4,
            ],
        ];

        foreach ($propertyTypes as $type) {
            \App\Models\PropertyType::create($type);
        }
    }
}
