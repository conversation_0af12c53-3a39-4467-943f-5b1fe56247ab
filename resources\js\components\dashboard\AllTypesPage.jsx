import React, { useState, useEffect } from 'react';
import PropertyTypeTable from "./PropertyTypeTable";
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Plus,
  Settings,
  Clock,
  AlertCircle,
  CheckCircle,
  DollarSign,
  Key,
  Wrench,
  Package,
  FileText,
} from "lucide-react";
import propertyTypeAPI from '../../services/propertyTypeAPI';
import { showAlert } from '../../utils/sweetAlert';
import PropertyTypeModal from './PropertyTypeModal';
import PropertyStatusTable from './PropertyStatusTable';

export default function AllTypes() {
  const [propertyTypes, setPropertyTypes] = useState([]);
  const [loading, setLoading] = useState(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState('create');
  const [selectedPropertyType, setSelectedPropertyType] = useState(null);

  // Property Statuses
  const [propertyStatuses, setPropertyStatuses] = useState([]);
  const [loadingStatuses, setLoadingStatuses] = useState(true);
  const [isStatusModalOpen, setIsStatusModalOpen] = useState(false);
  const [selectedPropertyStatus, setSelectedPropertyStatus] = useState(null);
  const [modalModeStatus, setModalModeStatus] = useState('create');
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');

  const getIconComponent = (iconName) => {
    const iconMap = {
      ClipboardList: Clock,
      Hammer: AlertCircle,
      CheckCircle: CheckCircle,
      DollarSign: DollarSign,
      Key: Key,
      Wrench: Wrench,
      Package: Package,
      FileText: FileText,
    };
    return iconMap[iconName] || Package;
  };

  // Open Property Status Modal
const handleStatusModalOpen = (mode = 'create', status = null) => {
  setSelectedPropertyStatus(status);
  setModalModeStatus(mode);
  setIsStatusModalOpen(true);
};

  
 

  // Property Types Handle create
  const handleCreate = () => {
    setSelectedPropertyType(null);
    setModalMode('create');
    setIsModalOpen(true);
  };

  //Property Types Handle view
  const handleView = (propertyType) => {
    setSelectedPropertyType(propertyType);
    setModalMode('view');
    setIsModalOpen(true);
  };

  //Property Types Handle edit
  const handleEdit = (propertyType) => {
    setSelectedPropertyType(propertyType);
    setModalMode('edit');
    setIsModalOpen(true);
  };

  //Property Types Handle toggle status
  const handleToggleStatus = async (propertyType) => {
    try {
      await propertyTypeAPI.toggleStatus(propertyType.id);
      showAlert('success', 'Success', 'Property type status updated successfully');
      fetchPropertyTypes(); // Refresh data
    } catch (error) {
      showAlert('error', 'Error', error.message || 'Failed to update property type status');
    }
  };

  //Property Types Handle delete
  const handleDelete = async (propertyType) => {
    if (window.confirm(`Are you sure you want to delete "${propertyType.name}"?`)) {
      try {
        await propertyTypeAPI.delete(propertyType.id);
        showAlert('success', 'Success', 'Property type deleted successfully');
        fetchPropertyTypes(); // Refresh data
      } catch (error) {
        showAlert('error', 'Error', error.message || 'Failed to delete property type');
      }
    }
  };

  //Property Types Handle modal success
  const handleModalSuccess = () => {
    setIsModalOpen(false);
    fetchPropertyTypes();
  };

  return (
    <>
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">All Property Types</h1>
          <p className="text-muted-foreground">
            Browse through all available property categories
          </p>
        </div>
        <Button onClick={handleCreate} className="gap-2">
          <Plus className="h-4 w-4" />
          Add Property Type
        </Button>
      </div>

      <div className="space-y-6 mb-5">
        <PropertyTypeTable
          propertyTypes={propertyTypes}
          handleView={handleView}
          handleEdit={handleEdit}
          handleToggleStatus={handleToggleStatus}
          handleDelete={handleDelete}
        />
      </div>

      {/* Modal */}
      <PropertyTypeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSuccess={handleModalSuccess}
        propertyType={selectedPropertyType}
        mode={modalMode}
      />




        {/* Property Statuses Table */}
      <PropertyStatusTable
        searchTerm={searchTerm}
        statusFilter={statusFilter}
        getIconComponent={getIconComponent}
        openModal={handleStatusModalOpen}
        handleToggleStatus={handleToggleStatus}
        handleDelete={handleDelete}
      />

    </>
  );
}
