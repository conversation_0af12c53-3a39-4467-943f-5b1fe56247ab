<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('tanants', function (Blueprint $table) {
            $table->string('first_name')->nullable()->after('name');
            $table->string('last_name')->nullable()->after('first_name');
            $table->string('photo')->nullable()->after('last_name');
            $table->text('previous_address')->nullable()->after('address');
            $table->text('permanent_address')->nullable()->after('previous_address');
            $table->text('note')->nullable()->after('permanent_address');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('tanants', function (Blueprint $table) {
            $table->dropColumn(['first_name', 'last_name', 'photo', 'previous_address', 'permanent_address', 'note']);
        });
    }
};
