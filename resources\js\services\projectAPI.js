import axios from 'axios';
import { API_BASE_URL } from '../config/api.js';

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

class ProjectAPI {
  constructor() {
    this.baseUrl = '/projects';
  }

  // Get all projects with filtering and pagination
  async getAll(params = {}) {
    try {
      const response = await api.get(this.baseUrl, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw error;
    }
  }

  // Get project statistics
  async getStatistics() {
    try {
      const response = await api.get(`${this.baseUrl}-statistics`);
      return response.data;
    } catch (error) {
      console.error('Error fetching project statistics:', error);
      throw error;
    }
  }

  // Get dropdown data (for filters)
  async getDropdowns() {
    try {
      // Use the correct endpoint: '/projects-dropdown' (protected route)
      const response = await api.get('/projects-dropdown');
      return response.data;
    } catch (error) {
      console.error('Error fetching dropdown data:', error);
      throw error;
    }
  }

  // Get single project by ID
  async getById(id) {
    try {
      const response = await api.get(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching project ${id}:`, error);
      throw error;
    }
  }

  // Create new project
  async create(projectData) {
    try {
      const response = await api.post(this.baseUrl, projectData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error creating project:', error);
      throw error;
    }
  }

  // Update existing project
  async update(id, projectData) {
    try {
      // Laravel workaround for PUT with multipart/form-data
      if (projectData instanceof FormData) {
        projectData.append('_method', 'PUT');
        const response = await api.post(`${this.baseUrl}/${id}`, projectData, {
          headers: {
            'Content-Type': 'multipart/form-data',
          },
        });
        return response.data;
      } else {
        const response = await api.put(`${this.baseUrl}/${id}`, projectData);
        return response.data;
      }
    } catch (error) {
      console.error(`Error updating project ${id}:`, error);
      throw error;
    }
  }

  // Delete project
  async delete(id) {
    try {
      const response = await api.delete(`${this.baseUrl}/${id}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting project ${id}:`, error);
      throw error;
    }
  }

  // Bulk delete projects
  async bulkDelete(ids) {
    try {
      const response = await apiService.delete(`${this.baseUrl}/bulk`, {
        data: { ids }
      });
      return response.data;
    } catch (error) {
      console.error('Error bulk deleting projects:', error);
      throw error;
    }
  }

  // Toggle featured status
  async toggleFeatured(id) {
    try {
      const response = await apiService.patch(`${this.baseUrl}/${id}/toggle-featured`);
      return response.data;
    } catch (error) {
      console.error(`Error toggling featured status for project ${id}:`, error);
      throw error;
    }
  }

  // Toggle availability status
  async toggleAvailability(id) {
    try {
      const response = await apiService.patch(`${this.baseUrl}/${id}/toggle-availability`);
      return response.data;
    } catch (error) {
      console.error(`Error toggling availability for project ${id}:`, error);
      throw error;
    }
  }

  // Update project status
  async updateStatus(id, status) {
    try {
      const response = await apiService.patch(`${this.baseUrl}/${id}/status`, { status });
      return response.data;
    } catch (error) {
      console.error(`Error updating status for project ${id}:`, error);
      throw error;
    }
  }

  // Upload single image
  async uploadImage(id, imageData) {
    try {
      const formData = new FormData();
      formData.append('image', imageData.file);
      formData.append('title', imageData.title || '');
      formData.append('description', imageData.description || '');
      formData.append('image_type', imageData.image_type || 'gallery');
      formData.append('sort_order', imageData.sort_order || 0);
      formData.append('is_featured', imageData.is_featured || false);

      const response = await apiService.post(`${this.baseUrl}/${id}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error(`Error uploading image for project ${id}:`, error);
      throw error;
    }
  }

  // Delete project image
  async deleteImage(projectId, imageId) {
    try {
      const response = await api.delete(`${this.baseUrl}/${projectId}/images/${imageId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting image ${imageId} for project ${projectId}:`, error);
      throw error;
    }
  }

  // Upload single video
  async uploadVideo(id, videoData) {
    try {
      const formData = new FormData();
      if (videoData.file) {
        formData.append('video', videoData.file);
      }
      formData.append('title', videoData.title || '');
      formData.append('description', videoData.description || '');
      formData.append('video_type', videoData.video_type || 'upload');
      formData.append('youtube_url', videoData.youtube_url || '');
      formData.append('video_url', videoData.video_url || '');
      formData.append('is_featured', videoData.is_featured || false);

      const response = await apiService.post(`${this.baseUrl}/${id}/videos`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error(`Error uploading video for project ${id}:`, error);
      throw error;
    }
  }

  // Delete project video
  async deleteVideo(projectId, videoId) {
    try {
      const response = await apiService.delete(`${this.baseUrl}/${projectId}/videos/${videoId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting video ${videoId} for project ${projectId}:`, error);
      throw error;
    }
  }

  // Get project units
  async getUnits(id) {
    try {
      const response = await apiService.get(`${this.baseUrl}/${id}/units`);
      return response.data;
    } catch (error) {
      console.error(`Error fetching units for project ${id}:`, error);
      throw error;
    }
  }

  // Create project unit
  async createUnit(projectId, unitData) {
    try {
      const response = await apiService.post(`${this.baseUrl}/${projectId}/units`, unitData);
      return response.data;
    } catch (error) {
      console.error(`Error creating unit for project ${projectId}:`, error);
      throw error;
    }
  }

  // Update project unit
  async updateUnit(projectId, unitId, unitData) {
    try {
      const response = await apiService.put(`${this.baseUrl}/${projectId}/units/${unitId}`, unitData);
      return response.data;
    } catch (error) {
      console.error(`Error updating unit ${unitId} for project ${projectId}:`, error);
      throw error;
    }
  }

  // Delete project unit
  async deleteUnit(projectId, unitId) {
    try {
      const response = await apiService.delete(`${this.baseUrl}/${projectId}/units/${unitId}`);
      return response.data;
    } catch (error) {
      console.error(`Error deleting unit ${unitId} for project ${projectId}:`, error);
      throw error;
    }
  }

  async unitDropdown(projectId) {
    try {
      // Fetch only units for the selected project
      const response = await api.get(`/unit-dropdown`, { params: { project_id: projectId } });
      return response.data;
    } catch (error) {
      console.error(`Error fetching unit dropdown:`, error);
      throw error;
    }
  }

  // Search projects
  async search(query, filters = {}) {
    try {
      const params = {
        search: query,
        ...filters
      };
      const response = await apiService.get(`${this.baseUrl}/search`, { params });
      return response.data;
    } catch (error) {
      console.error('Error searching projects:', error);
      throw error;
    }
  }

  // Export projects
  async export(format = 'excel', filters = {}) {
    try {
      const params = {
        format,
        ...filters
      };
      const response = await apiService.get(`${this.baseUrl}/export`, { 
        params,
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      console.error('Error exporting projects:', error);
      throw error;
    }
  }

  // Import projects
  async import(file, options = {}) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      Object.keys(options).forEach(key => {
        formData.append(key, options[key]);
      });

      const response = await apiService.post(`${this.baseUrl}/import`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data;
    } catch (error) {
      console.error('Error importing projects:', error);
      throw error;
    }
  }
}

// Create and export a singleton instance
const projectAPI = new ProjectAPI();
export default projectAPI;
