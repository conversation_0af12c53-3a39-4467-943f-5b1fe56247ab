/**
 * Utility functions for making API calls
 */

const API_BASE_URL = '/api';

/**
 * Generic fetch function with error handling
 */
export const apiCall = async (endpoint, options = {}) => {
  try {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(url, config);
    
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('API call failed:', error);
    throw error;
  }
};

/**
 * Fetch page content by page_id and optionally widget_id
 */
export const fetchPageContent = async (pageId, widgetId = null) => {
  let endpoint = `/frontend/page-contents?page_id=${pageId}&paginate=false`;
  
  if (widgetId) {
    endpoint += `&widget_id=${widgetId}`;
  }
  
  return await apiCall(endpoint);
};

/**
 * Fetch slider data specifically (page_id=1, widget_id=1)
 */
export const fetchSliderData = async () => {
  const result = await fetchPageContent(1, 1);
  
  if (result.success && result.data && result.data.length > 0) {
    const sliderWidget = result.data.find(item => item.widget_id === 1);
    
    if (sliderWidget && sliderWidget.pageContent) {
      const config = typeof sliderWidget.pageContent === 'string' 
        ? JSON.parse(sliderWidget.pageContent) 
        : sliderWidget.pageContent;
        
      return config.sliders || [];
    }
  }
  
  return [];
};