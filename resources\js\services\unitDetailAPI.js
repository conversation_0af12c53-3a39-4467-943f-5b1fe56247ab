import axios from 'axios';

const api = axios.create({
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

const unitDetailAPI = {
  // Get all units for a project
  getByProject: async (project_id) => {
    try {
      const response = await api.get(`/api/unit-details`, { params: { project_id } });
      return response.data;
    } catch (error) {
      console.error('Error fetching units for project:', error);
      throw error;
    }
  },
};

export default unitDetailAPI;
