import React, { useState, useEffect } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Package, X, Loader2 } from "lucide-react";
import menuAPI from '@/services/menuAPI';

const MenuNewModal = ({ isOpen, onClose, menu, onSuccess }) => {
    const [formData, setFormData] = useState({
        name: '',
        is_active: true
    });
    const [loading, setLoading] = useState(false);
    const [formErrors, setFormErrors] = useState({});

    useEffect(() => {
        if (menu) {
            setFormData({
                name: menu.name || '',
                is_active: menu.is_active || false
            });
        } else {
            setFormData({
                name: '',
                is_active: true
            });
        }
        setFormErrors({});
    }, [menu, isOpen]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        setLoading(true);
        setFormErrors({});

        try {
            const data = {
                name: formData.name,
                is_active: formData.is_active
            };

            if (menu) {
                await menuAPI.update(menu.id, data);
            } else {
                await menuAPI.create(data);
            }

            onSuccess();
            onClose();
        } catch (error) {
            console.error('Error saving menu:', error);
            if (error.response?.data?.errors) {
                setFormErrors(error.response.data.errors);
            }
        } finally {
            setLoading(false);
        }
    };

    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));
        // Clear error when user starts typing
        if (formErrors[field]) {
            setFormErrors(prev => ({
                ...prev,
                [field]: null
            }));
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-center justify-center p-4">
            <div className="bg-white rounded-lg shadow-2xl w-full max-w-2xl max-h-[90vh] flex flex-col overflow-hidden">
                {/* Modal Header */}
                <div className="bg-gradient-to-r from-blue-600 to-blue-700 px-6 py-4 flex items-center justify-between text-white">
                    <div className="flex items-center space-x-3">
                        <Package className="h-6 w-6" />
                        <div>
                            <h3 className="text-lg font-semibold">
                                {menu ? 'Edit Menu' : 'Create New Menu'}
                            </h3>
                            <p className="text-blue-100 text-sm">
                                {menu
                                    ? 'Update the menu information below.'
                                    : 'Fill in the details to create a new menu.'
                                }
                            </p>
                        </div>
                    </div>
                    <button
                        onClick={onClose}
                        className="text-white hover:text-blue-200 transition-colors p-1 rounded-full hover:bg-white/10"
                    >
                        <X className="h-5 w-5" />
                    </button>
                </div>

                {/* Modal Content */}
                <div className="flex-1 overflow-y-auto p-6">
                    <form onSubmit={handleSubmit} className="space-y-6">
                        <div className="space-y-4">
                            <div>
                                <Label htmlFor="name" className="text-sm font-medium">
                                    Menu Name *
                                </Label>
                                <Input
                                    id="name"
                                    value={formData.name}
                                    onChange={(e) => handleInputChange('name', e.target.value)}
                                    placeholder="Enter menu name"
                                    className={formErrors.name ? 'border-red-500' : ''}
                                    required
                                />
                                {formErrors.name && (
                                    <p className="text-sm text-red-500 mt-1">{formErrors.name[0]}</p>
                                )}
                            </div>
                            <div className="flex items-center space-x-2">
                                <Switch
                                    id="is_active"
                                    checked={formData.is_active}
                                    onCheckedChange={(checked) => handleInputChange('is_active', checked)}
                                />
                                <Label htmlFor="is_active" className="text-sm font-medium">
                                    Active
                                </Label>
                            </div>
                        </div>

                        {/* Modal Footer */}
                        <div className="flex justify-end space-x-3 pt-6 border-t">
                            <Button
                                type="button"
                                variant="outline"
                                onClick={onClose}
                                disabled={loading}
                                className="hover:bg-gray-50 transition-colors"
                            >
                                Cancel
                            </Button>
                            <Button
                                type="submit"
                                disabled={loading}
                                className="bg-blue-600 hover:bg-blue-700 transition-colors"
                            >
                                {loading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                                {menu ? 'Update Menu' : 'Create Menu'}
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default MenuNewModal;
    
