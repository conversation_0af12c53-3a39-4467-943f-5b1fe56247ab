<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Carbon\Carbon;

class InvoiceSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $now = Carbon::now();

        DB::table('invoices')->insert([
            [
                'invoice_number'   => 'INV-' . Str::upper(Str::random(6)),
                'tenant_id'        => 1, // Make sure this tenant exists
                'property_id'      => 1, // Make sure this property exists
                'issue_date'       => $now->toDateString(),
                'due_date'         => $now->copy()->addDays(15)->toDateString(),
                'billing_date'     => $now->copy()->startOfMonth()->toDateString(),
                'billing_end'      => $now->copy()->endOfMonth()->toDateString(),
                'ammount'          => 1000.00,
                'tax_amount'       => 100.00,
                'discount_amount'  => 50.00,
                'total_amount'     => 1050.00,
                'status'           => 'unpaid',
                'payment_date'     => null,
                'payment_method'   => null,
                'notes'            => 'First month rent invoice',
                'created_by'       => 1,
                'updated_by'       => 1,
                'soft_deleted'     => 0,
                'created_at'       => $now,
                'updated_at'       => $now,
            ],
            [
                'invoice_number'   => 'INV-' . Str::upper(Str::random(6)),
                'tenant_id'        => 2, // Must exist in tenants table
                'property_id'      => 2,
                'issue_date'       => $now->toDateString(),
                'due_date'         => $now->copy()->addDays(30)->toDateString(),
                'billing_date'     => $now->copy()->startOfMonth()->toDateString(),
                'billing_end'      => $now->copy()->endOfMonth()->toDateString(),
                'ammount'          => 1500.00,
                'tax_amount'       => 150.00,
                'discount_amount'  => 0.00,
                'total_amount'     => 1650.00,
                'status'           => 'paid',
                'payment_date'     => $now->copy()->addDays(5)->toDateString(),
                'payment_method'   => 'bank_transfer',
                'notes'            => 'Advance rent payment',
                'created_by'       => 1,
                'updated_by'       => 1,
                'soft_deleted'     => 0,
                'created_at'       => $now,
                'updated_at'       => $now,
            ]
        ]);
    }
}
