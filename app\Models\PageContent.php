<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PageContent extends Model
{
    protected $fillable = [
        'page_id',
        'widget_id',
        'pageContent',
        'order_index',
    ];

    protected $casts = [
        'pageContent' => 'array',
        'order_index' => 'integer',
    ];

    /**
     * Default ordering by order_index.
     */
    protected static function boot()
    {
        parent::boot();

        static::addGlobalScope('ordered', function ($builder) {
            $builder->orderBy('order_index', 'asc');
        });
    }

    /**
     * Relationship with widgets (if you have a Widget model).
     */
    public function widget()
    {
        return $this->belongsTo(\App\Models\Widget::class);
    }

    /**
     * Relationship with pages/menus (if you have a Menu model).
     */
    public function page()
    {
        return $this->belongsTo(\App\Models\Menu::class, 'page_id');
    }
}
