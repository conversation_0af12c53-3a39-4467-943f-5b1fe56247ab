import React from "react";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { TabsContent } from "@/components/ui/tabs";

const Whatsapp = ({ formData, handleInputChange, handleSubmit, loading }) => {
  return (
      <TabsContent value="whatsapp">
        <div className="grid grid-cols-2 md:grid-cols-2 gap-6">
            <div className="flex items-center space-x-2">
                <label htmlFor="enableRecaptcha" className="text-sm font-medium text-gray-700">
                Enable/Disable Whatsapp Chat
                </label>
                <input
                type="checkbox"
                id="whatsappChat"
                className="relative inline-flex h-6 w-11 items-center rounded-full bg-gray-200 transition-colors focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 checked:bg-indigo-600"
                checked={parseInt(formData.whatsappChat) === 1}
                onChange={(e) =>
                    handleInputChange("whatsappChat", e.target.checked ? 1 : 0)
                }
                />
            </div>
             <div>
                <Label htmlFor="recaptchaKey">Whatsapp Number</Label>
                <Input
                    id="whatsappNumber"
                    value={formData.whatsappNumber || ""}
                    onChange={(e) => handleInputChange("whatsappNumber", e.target.value)}
                    placeholder=""
                />
            </div>
        </div>
            
        <div className="flex justify-end mt-4">
            <button
                onClick={handleSubmit}
                className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
                disabled={loading}
            >
                {loading ? "Saving..." : "Save Settings"}
            </button>
        </div>
           
      </TabsContent>
  )};
  export default Whatsapp;
