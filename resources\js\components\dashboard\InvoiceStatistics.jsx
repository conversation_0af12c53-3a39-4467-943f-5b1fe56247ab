import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { FileText, DollarSign, Clock, AlertTriangle } from "lucide-react";

const InvoiceStatistics = ({ statistics }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-blue-800">Total Invoices</CardTitle>
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <FileText className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-900">{statistics.counts.total}</div>
          <p className="text-xs text-blue-600 mt-1">Total Amount: ${statistics.amounts.total.toFixed(2)}</p>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-green-800">Paid Invoices</CardTitle>
          <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
            <DollarSign className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-900">{statistics.counts.paid}</div>
          <p className="text-xs text-green-600 mt-1">Paid Amount: ${statistics.amounts.paid.toFixed(2)}</p>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-r from-yellow-50 to-yellow-100 border-yellow-200">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-yellow-800">Pending Invoices</CardTitle>
          <div className="w-8 h-8 bg-yellow-500 rounded-lg flex items-center justify-center">
            <Clock className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-yellow-900">{statistics.counts.pending}</div>
          <p className="text-xs text-yellow-600 mt-1">Pending Amount: ${statistics.amounts.pending.toFixed(2)}</p>
        </CardContent>
      </Card>

      <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-red-800">Overdue Invoices</CardTitle>
          <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
            <AlertTriangle className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-900">{statistics.counts.overdue}</div>
          <p className="text-xs text-red-600 mt-1">Overdue Amount: ${statistics.amounts.overdue.toFixed(2)}</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default InvoiceStatistics;
