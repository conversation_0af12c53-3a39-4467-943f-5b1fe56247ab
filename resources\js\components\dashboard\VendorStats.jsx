import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from '@/components/ui/card';
import { Truck, UserCheck, UserX } from 'lucide-react';

const VendorStats = ({ statistics }) => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Total Vendors */}
      <Card className="bg-gradient-to-r from-blue-50 to-blue-100 border-blue-200">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-blue-800">Total Vendors</CardTitle>
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <Truck className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-900">{statistics.total}</div>
          <p className="text-xs text-blue-600 mt-1">All registered vendors</p>
        </CardContent>
      </Card>

      {/* Active Vendors */}
      <Card className="bg-gradient-to-r from-green-50 to-green-100 border-green-200">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-green-800">Active Vendors</CardTitle>
          <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
            <UserCheck className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-900">{statistics.active}</div>
          <p className="text-xs text-green-600 mt-1">Currently active vendors</p>
        </CardContent>
      </Card>

      {/* Inactive Vendors */}
      <Card className="bg-gradient-to-r from-red-50 to-red-100 border-red-200">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-red-800">Inactive Vendors</CardTitle>
          <div className="w-8 h-8 bg-red-500 rounded-lg flex items-center justify-center">
            <UserX className="h-4 w-4 text-white" />
          </div>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-900">{statistics.inactive}</div>
          <p className="text-xs text-red-600 mt-1">Not currently active</p>
        </CardContent>
      </Card>
    </div>
  );
};

export default VendorStats;
