<?php

namespace App\Http\Controllers;
use Illuminate\Http\JsonResponse;
use App\Models\BackendSettings;
use Illuminate\Http\Request;

class BackendSettingsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        //
        try {
            $settings = BackendSettings::first();
           
            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        
        try {
            $data = [];
            // Handle logo files if present
            if ($request->hasFile('adminLogo')) {
                $file = $request->file('adminLogo');
                if ($file->isValid()) {
                    $filename = 'adminLogo_' . time() . '.' . $file->getClientOriginalExtension();
                    $destination = public_path('settings/image');
                    if (!file_exists($destination)) {
                        mkdir($destination, 0775, true);
                    }
                    $file->move($destination, $filename);
                    $data['adminLogo'] = '/settings/image/' . $filename;
                }
            }

            if ($request->hasFile('invoiceLogo')) {
                $file = $request->file('invoiceLogo');
                if ($file->isValid()) {
                    $filename = 'invoiceLogo_' . time() . '.' . $file->getClientOriginalExtension();
                    $destination = public_path('settings/image');
                    if (!file_exists($destination)) {
                        mkdir($destination, 0775, true);
                    }
                    $file->move($destination, $filename);
                    $data['invoiceLogo'] = '/settings/image/' . $filename;
                }
            }

            // Other fields
            $data['applicationName'] = $request->input('applicationName', null);
            $data['vatTaxRateForCustomers'] = $request->input('vatTaxRateForCustomers', null);
            $data['vatTaxRateForMerchants'] = $request->input('vatTaxRateForMerchants', null);
            $data['systemTimeZone'] = $request->input('systemTimeZone', null);
            $data['dateFormat'] = $request->input('dateFormat', null);
            $data['commissionFromMerchant'] = $request->input('commissionFromMerchant', null);
            $data['driver'] = $request->input('driver', null);
            $data['host'] = $request->input('host', null);
            $data['port'] = $request->input('port', null);
            $data['username'] = $request->input('username', null);
            $data['password'] = $request->input('password', null);
            $data['fromAdress'] = $request->input('fromAdress', null);
            $data['fromName'] = $request->input('fromName', null);
            $data['encryption'] = $request->input('encryption', null);
            $data['enableRecaptcha'] = $request->input('enableRecaptcha', null);
            $data['recaptchaKey'] = $request->input('recaptchaKey', null);
            $data['recaptchaSecret'] = $request->input('recaptchaSecret', null);
            $data['whatsappChat'] = $request->input('whatsappChat', null);
            $data['whatsappNumber'] = $request->input('whatsappNumber', null);
            $settings = BackendSettings::first();
            if ($settings) {
                $settings->update($data);
            } else {
                $settings = BackendSettings::create($data);
            }

            return response()->json([
                'success' => true,
                'data' => $settings,
                'message' => 'Backend settings saved successfully'
            ], 201);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to save backend settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(BackendSettings $backendSettings): JsonResponse
    {
        //
        try {
            return response()->json([
                'success' => true,
                'data' => $backendSettings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch settings: ' . $e->getMessage()
            ], 500);
        }
    }

  

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(BackendSettings $backendSettings)
    {
        //
    }
}
