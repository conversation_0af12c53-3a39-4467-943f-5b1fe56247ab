<?php

namespace App\Http\Controllers;
use App\Models\Tanants;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class TanantController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request): JsonResponse
    {
        //
        try{
            $query=Tanants::query();
            if ($request->has('search') && $request->search) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('first_name', 'LIKE', "%{$search}%")
                      ->orWhere('last_name', 'LIKE', "%{$search}%")
                      ->orWhere('email', 'LIKE', "%{$search}%")
                      ->orWhere('phone', 'LIKE', "%{$search}%");
                });
            }
            $perPage = $request->get('per_page', 10);
            $tanants = $query->with(['documents', 'emergencyContact'])->paginate($perPage);

            return response()->json([
                'success' => true,
                'data' => $tanants
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch tanants: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): JsonResponse
    {
        //
        try{
            $validated = $request->validate([
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'previous_address' => 'nullable|string|max:500',
                'permanent_address' => 'nullable|string|max:500',
                'note' => 'nullable|string|max:500',
                'email' => 'required|email|unique:tanants,email',
                'phone' => 'nullable|string|max:20',
                'status' => 'in:active,inactive',
                'documents' => 'array',
                'documents.*.title' => 'required_with:documents|string|max:255',
                'emergency_contact.name' => 'nullable|string|max:255',
                'emergency_contact.relation' => 'nullable|string|max:255',
                'emergency_contact.phone' => 'nullable|string|max:20',
            ]);

       

            // Handle photo upload
            if ($request->hasFile('photo')) {
                $validated['photo'] = $request->file('photo')->store('tenant_photos', 'public');
               
            }
             
            $tanant = Tanants::create($validated);

            // Handle documents (support FormData and JSON)
            if ($request->has('documents')) {
                foreach ($request->documents as $i => $doc) {
                    $filePath = null;
                    // If file is uploaded via FormData, it will be in files array
                    if ($request->hasFile("documents.$i.file")) {
                        $filePath = $request->file("documents.$i.file")->store('tenant_documents', 'public');
                    } elseif (isset($doc['file']) && $doc['file']) {
                        $filePath = $doc['file']; // fallback for API/JSON
                    }
                    $tanant->documents()->create([
                        'title' => $doc['title'],
                        'file' => $filePath,
                    ]);
                }
            }

            // Handle emergency contact
            if ($request->has('emergency_contact')) {
                $ec = $request->emergency_contact;
                $tanant->emergencyContact()->create([
                    'name' => $ec['name'] ?? '',
                    'relation' => $ec['relation'] ?? '',
                    'phone' => $ec['phone'] ?? '',
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => $tanant->load(['documents', 'emergencyContact'])
            ], 201);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch tanants: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(string $id): JsonResponse
    {
        try{
            $tanant = Tanants::with(['documents', 'emergencyContact'])->findOrFail($id);
            return response()->json([
                'success' => true,
                'data' => $tanant
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch tanant: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, string $id): JsonResponse
    {
        //
        try{
            $tanant = Tanants::findOrFail($id);
            $validated = $request->validate([
                'first_name' => 'required|string|max:255',
                'last_name' => 'required|string|max:255',
                'photo' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
                'previous_address' => 'nullable|string|max:500',
                'permanent_address' => 'nullable|string|max:500',
                'note' => 'nullable|string|max:500',
                'email' => 'required|email|unique:tanants,email,' . $id,
                'phone' => 'nullable|string|max:20',
                'status' => 'in:active,inactive',
                'documents' => 'array',
                'documents.*.title' => 'required_with:documents|string|max:255',
                'emergency_contact.name' => 'nullable|string|max:255',
                'emergency_contact.relation' => 'nullable|string|max:255',
                'emergency_contact.phone' => 'nullable|string|max:20',
            ]);

            // Handle photo upload
            if ($request->hasFile('photo')) {
                $validated['photo'] = $request->file('photo')->store('tenant_photos', 'public');
            }

            $tanant->update($validated);

            // Handle documents (delete old and add new)
            $tanant->documents()->delete();
            if ($request->has('documents')) {
                foreach ($request->documents as $i => $doc) {
                    $filePath = null;
                    if ($request->hasFile("documents.$i.file")) {
                        $filePath = $request->file("documents.$i.file")->store('tenant_documents', 'public');
                    } elseif (isset($doc['file']) && $doc['file']) {
                        $filePath = $doc['file'];
                    }
                    $tanant->documents()->create([
                        'title' => $doc['title'],
                        'file' => $filePath,
                    ]);
                }
            }

            // Handle emergency contact (update or create)
            if ($request->has('emergency_contact')) {
                $ec = $request->emergency_contact;
                $tanant->emergencyContact()->updateOrCreate([], [
                    'name' => $ec['name'] ?? '',
                    'relation' => $ec['relation'] ?? '',
                    'phone' => $ec['phone'] ?? '',
                ]);
            }

            return response()->json([
                'success' => true,
                'data' => $tanant->load(['documents', 'emergencyContact'])
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to update tanant: ' . $e->getMessage()
            ], 500);
        }
    }



    /**
     * Remove the specified resource from storage.
     */
    public function destroy(string $id): JsonResponse
    {
        try{
            $tanant = Tanants::findOrFail($id);
            $tanant->delete();
            return response()->json([
                'success' => true,
                'message' => 'Tanant deleted successfully'
            ]);
        }catch(\Exception $e){
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete tanant: ' . $e->getMessage()
            ], 500);
        }
    }

    public function dropdown(): JsonResponse
    {
        try {
            $tanants = Tanants::select('id', 'first_name', 'last_name', 'email','phone','permanent_address','previous_address')
                ->where('status', 'active')
                ->orderBy('first_name')
                ->get();

            return response()->json([
                'success' => true,
                'data' => $tanants
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch tanants: ' . $e->getMessage()
            ], 500);
        }
    }

    public function getStatistics(): JsonResponse
    {
        try {
            $totalTanants = Tanants::count();
            $activeTanants = Tanants::where('status', 'active')->count();
            $inactiveTanants = Tanants::where('status', 'inactive')->count();

            return response()->json([
                'success' => true,
                'data' => [
                    'total_tanants' => $totalTanants,
                    'active_tanants' => $activeTanants,
                    'inactive_tanants' => $inactiveTanants
                ]
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch statistics: ' . $e->getMessage()
            ], 500);
        }
    }

    public function bulkStatusUpdate(Request $request): JsonResponse
    {
        try {
            $validated = $request->validate([
                'ids' => 'required|array',
                'ids.*' => 'integer|exists:tanants,id',
                'status' => 'required|in:active,inactive'
            ]);

            $updated = Tanants::whereIn('id', $validated['ids'])
                ->update(['status' => $validated['status']]);

            return response()->json([
                'success' => true,
                'message' => "Successfully updated {$updated} tenant(s) status",
                'updated_count' => $updated
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to update tenants status',
                'error' => $e->getMessage()
            ], 500);
        }
    }


}
