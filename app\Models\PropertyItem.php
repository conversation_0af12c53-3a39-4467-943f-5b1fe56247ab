<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class PropertyItem extends Model
{
    protected $fillable = [
        'invoice_id',
        'property_id',
        'unit_id',
        'amount',
        'tax_amount',
        'total_amount',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'tax_amount' => 'decimal:2',
        'total_amount' => 'decimal:2',
    ];

    // Accessor to provide ammount field for frontend compatibility
    public function getAmmountAttribute()
    {
        return $this->amount;
    }

    // Mutator to handle ammount field from frontend
    public function setAmmountAttribute($value)
    {
        $this->attributes['amount'] = $value;
    }

    // Relationships
    public function invoice()
    {
        return $this->belongsTo(Invoice::class);
    }

    public function property()
    {
        return $this->belongsTo(Project::class, 'property_id');
    }
 
    public function unit()
    {
        return $this->belongsTo(ProjectUnit::class, 'unit_id');
    }
}
