
import React, { useState, useEffect } from "react";
import RichTextEditor from "../../ui/RichTextEditor";

const AboutUs = ({ widget, handleConfigUpdate, handleSubmit }) => {
const [imagePreview, setImagePreview] = useState(widget.config?.image || "");
const [imagetwoPreview, setImagetwoPreview] = useState(widget.config?.imagetwo || "");
const [ceoSignaturePreview, setceoSignaturePreview] = useState(widget.config?.ceoSignature || "");

useEffect(() => {
  setImagePreview(widget.config?.image || "");
}, [widget.config?.image]);

useEffect(() => {
  setImagetwoPreview(widget.config?.imagetwo || "");
}, [widget.config?.imagetwo]);

useEffect(() => {
  setceoSignaturePreview(widget.config?.ceoSignature || "");
}, [widget.config?.ceoSignature]);
const handleImageChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // Create a preview for UI
      setImagePreview(URL.createObjectURL(file));
      // Upload file to server
      const formData = new FormData();
      formData.append('image', file);

      try {
        const response = await fetch('/api/page-contents/upload-image', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          handleConfigUpdate(widget.id, "image", data.data.url); // Store image URL
        } else {
          alert('Image upload failed: ' + data.message);
        }
      } catch (error) {
        alert('Error uploading image: ' + error.message);
      }
    }
  };

  const handleImagetwoChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      setImagetwoPreview(URL.createObjectURL(file));
      // Upload file to server
      const formData = new FormData();
      formData.append('image', file);

      try {
        const response = await fetch('/api/page-contents/upload-image', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          handleConfigUpdate(widget.id, "imagetwo", data.data.url); // Store image URL
        } else {
          alert('Image upload failed: ' + data.message);
        }
      } catch (error) {
        alert('Error uploading image: ' + error.message);
      }
    }
  };

  const handleCeoSignatureChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      setceoSignaturePreview(URL.createObjectURL(file));
      // Upload file to server
      const formData = new FormData();
      formData.append('image', file);

      try {
        const response = await fetch('/api/page-contents/upload-image', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          handleConfigUpdate(widget.id, "ceoSignature", data.data.url); // Store image URL
        } else {
          alert('CEO signature upload failed: ' + data.message);
        }
      } catch (error) {
        alert('Error uploading CEO signature: ' + error.message);
      }
    }
  };

  return (
    <form
      onSubmit={handleSubmit}
      encType="multipart/form-data"
      className="space-y-4"
    >
      <div className="flex gap-4">
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">
            Service From
          </label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={widget.config?.serviceFrom || ""}
            onChange={(e) =>
              handleConfigUpdate(widget.id, "serviceFrom", e.target.value)
            }
            placeholder="Enter the year the Service Started"
          />
        </div>
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">
            Title
          </label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={widget.config?.title || ""}
            onChange={(e) =>
              handleConfigUpdate(widget.id, "title", e.target.value)
            }
            placeholder="Enter the Title"
          />
        </div>

      </div>

      <div className="flex gap-4">
            <div className="flex-1">
                 <label className="block text-sm font-medium text-gray-700">
                    Project Completed
                  </label>
                   <input
                      type="number"
                      className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                      value={widget.config?.projectCompleted || ""}
                      onChange={(e) =>
                        handleConfigUpdate(widget.id, "projectCompleted", e.target.value)
                      }
                      placeholder="Enter the Number of Completed Projects"
                    />

            </div>

             <div className="flex-1">
                 <label className="block text-sm font-medium text-gray-700">
                    Project Sold
                  </label>
                  <input
                    type="number"
                    className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                    value={widget.config?.projectSold || ""}   
                    onChange={(e) =>
                      handleConfigUpdate(widget.id, "projectSold", e.target.value)
                    }
                    placeholder="Enter the Number of sold Projects"
                  />

            </div>

            <div className="flex-1">
                 <label className="block text-sm font-medium text-gray-700">
                    Year of Experience
                  </label>
                   <input
                      type="number"
                      className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
                      value={widget.config?.yearOfExperience || ""}
                      onChange={(e) =>
                        handleConfigUpdate(widget.id, "yearOfExperience", e.target.value)
                      }
                      placeholder="Enter the Number of sold Projects"
                    />

            </div>
      </div>

      <div className="flex gap-4">
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">
            About Text
          </label>
          <RichTextEditor
            value={widget.config?.aboutText || ""}
            onChange={(value) =>
              handleConfigUpdate(widget.id, "aboutText", value)
            }
            placeholder="Enter the about text..."
            height="150px"
          />
        </div>

        {/* <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">
            Message from CEO/Founder
          </label>
          <RichTextEditor
            value={widget.config?.ceoMessage || ""}
            onChange={(value) =>
              handleConfigUpdate(widget.id, "ceoMessage", value)
            }
            placeholder="Enter the message from CEO/Founder..."
            height="150px"
          />
        </div> */}
      </div>

      <div className="flex gap-4 pt-6">
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">
            Upload  Image One
          </label>
          <input
            type="file"
            accept="image/*"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            onChange={handleImageChange}
          />
        </div>
        {imagePreview && (
          <div className="flex items-center">
            <img
              src={imagePreview}
              alt="Preview"
              className="w-16 h-16 object-cover rounded-md border"
            />
          </div>
        )}

        <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700">
              Upload Two Image
            </label>
            <input
              type="file"
              accept="image/*"   // <-- fixed here
              className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
              onChange={handleImagetwoChange}  // <-- use new handler
            />
          </div>

         {imagetwoPreview && (
           <div className="flex items-center">
              <img
                src={imagetwoPreview}
                alt="Preview"
                className="w-16 h-16 object-cover rounded-md border"
              />
            </div>
        )}

         <div className="flex-1">
            <label className="block text-sm font-medium text-gray-700">
             CEO digital Signature 
            </label>
          <input
              type="file"
              accept="image/*"   // <-- fixed here
              className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
              onChange={handleCeoSignatureChange}  // <-- use new handler
            />
          </div>

           {ceoSignaturePreview && (
           <div className="flex items-center">
              <img
                src={ceoSignaturePreview}
                alt="Preview"
                className="w-16 h-16 object-cover rounded-md border"
              />
            </div>
        )}

      </div>

      {/* <div className="space-y-4 pt-4">
        <label className="block text-sm font-medium text-gray-700">
          Options
        </label>
        {(widget.config?.options || []).map((option, index) => (
          <div key={index} className="flex gap-4 mb-2">
            <input
              type="text"
              className="w-[150px] flex-1 rounded-md border border-gray-300 shadow-sm p-2"
              value={option.title}
              onChange={(e) => updateOption(index, "title", e.target.value)}
              placeholder="Enter the Option Title"
            />
            <input
              type="text"
              className="flex-1 rounded-md border border-gray-300 shadow-sm p-2"
              value={option.subText}
              onChange={(e) => updateOption(index, "subText", e.target.value)}
              placeholder="Enter the Option Sub Text"
            />

            <input
              type="text"
              className="w-[100px] rounded-md border border-gray-300 shadow-sm p-2"
              value={option.link}
              onChange={(e) => updateOption(index, "link", e.target.value)}
              placeholder="Redirection Link"
            />

            <input
              type="file"
              accept="image/*"
              className="w-[120px] rounded-md border border-gray-300 shadow-sm p-2"
              onChange={(e) => handleOptionImageChange(index, e)}
            />

             {option.image && (
              <img
                src={option.image}
                alt="Option Preview"
                className="w-16 h-16 object-cover rounded-md border"
              />
            )} 

            <button
              type="button"
              className="text-red-600 hover:text-red-800 font-semibold px-2"
              onClick={() => removeOption(index)}
              aria-label={`Remove option ${index + 1}`}
            >
              &times;
            </button>
          </div>
        ))}
        <button
          type="button"
          className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          onClick={addOption}
        >
          Add Option
        </button>
      </div> */}
    </form>
  );
};

export default AboutUs;
