import React, { useState, useEffect } from "react";
const SliderItem = ({ slider, sliderIndex, updateSlider, removeSlider }) => {
  const [imagePreview, setImagePreview] = useState(slider.image || "");
  const [ratingLogoPreview, setRatingLogoPreview] = useState(slider.ratingLogo || "");

  useEffect(() => {
    setImagePreview(slider.image || "");
  }, [slider.image]);

  useEffect(() => {
    setRatingLogoPreview(slider.ratingLogo || "");
  }, [slider.ratingLogo]);

  const handleImageChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // Create FormData to send file
      const formData = new FormData();
      formData.append('image', file);

      try {
        const response = await fetch('/api/page-contents/upload-image', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          setImagePreview(data.data.url);
          updateSlider(sliderIndex, "image", data.data.url); // Store image URL
        } else {
          alert('Image upload failed: ' + data.message);
        }
      } catch (error) {
        alert('Error uploading image: ' + error.message);
      }
    }
  };

  const handleRatingLogoChange = async (e) => {
    const file = e.target.files[0];
    if (file) {
      // Create FormData to send file
      const formData = new FormData();
      formData.append('image', file);

      try {
        const response = await fetch('/api/page-contents/upload-image', {
          method: 'POST',
          body: formData,
        });

        const data = await response.json();

        if (data.success) {
          setRatingLogoPreview(data.data.url);
          updateSlider(sliderIndex, "ratingLogo", data.data.url); // Store image URL
        } else {
          alert('Rating logo upload failed: ' + data.message);
        }
      } catch (error) {
        alert('Error uploading rating logo: ' + error.message);
      }
    }
  };

  return ( 
    <div key={sliderIndex} className="border p-4 rounded-md space-y-4">
      <div className="flex gap-4">
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">Title</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={slider.title || ""}
            onChange={(e) => updateSlider(sliderIndex, "title", e.target.value)}
            placeholder="Enter title..."
          />
        </div>

        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">Button Text</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={slider.buttonText || ""}
            onChange={(e) => updateSlider(sliderIndex, "buttonText", e.target.value)}
            placeholder="Enter Button Text..."
          />
        </div>

        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">Button Url</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={slider.buttonUrl || ""}
            onChange={(e) => updateSlider(sliderIndex, "buttonUrl", e.target.value)}
            placeholder="Enter Button Url..."
          />
        </div>
        <div className="flex items-center">
          <button
            type="button"
            className="px-3 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
            onClick={() => removeSlider(sliderIndex)}
          >
            Remove Slider
          </button>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Subtitle</label>
        <input
          type="text"
          className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
          value={slider.subtitle || ""}
          onChange={(e) => updateSlider(sliderIndex, "subtitle", e.target.value)}
          placeholder="Enter subtitle..."
        />
      </div>

      <div className="flex gap-4">
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">Image Upload</label>
          <input
            type="file"
            accept="image/*"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            onChange={handleImageChange}
          />
        </div>
        {imagePreview && (
          <div className="flex items-center">
            <img
              src={imagePreview}
              alt="Preview"
              className="w-16 h-16 object-cover rounded-md border"
            />
          </div>
        )}
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">Location</label>
          <input
            type="text"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            value={slider.location || ""}
            onChange={(e) => updateSlider(sliderIndex, "location", e.target.value)}
            placeholder="Enter location..."
          />
        </div>
      </div>

      <div className="flex gap-4">
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">Rating Logo Upload</label>
          <input
            type="file"
            accept="image/*"
            className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
            onChange={handleRatingLogoChange}
          />
        </div>
        {ratingLogoPreview && (
          <div className="flex items-center">
            <img
              src={ratingLogoPreview}
              alt="Rating Logo Preview"
              className="w-16 h-16 object-cover rounded-md border"
            />
          </div>
        )}
        <div className="flex-1">
          <label className="block text-sm font-medium text-gray-700">Rating</label>
          <div className="flex items-center gap-2">
           
            <input
              type="number"
              min="0"
              max="5"
              step="0.1"
              className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
              value={slider.rating || ""}
              onChange={(e) => updateSlider(sliderIndex, "rating", e.target.value)}
              placeholder="4.5"
            />
          </div>
        </div>
      </div>

      <div>
        <label className="block text-sm font-medium text-gray-700">Description</label>
        <textarea
          rows="1"
          className="mt-1 block w-full rounded-md border border-gray-300 shadow-sm p-2"
          value={slider.description || ""}
          onChange={(e) => updateSlider(sliderIndex, "description", e.target.value)}
          placeholder="Enter description..."
        />
      </div>

  

    </div>
    
  )
};

export default SliderItem;
