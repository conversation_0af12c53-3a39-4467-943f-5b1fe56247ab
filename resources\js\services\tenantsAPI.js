import axios from 'axios';
const API_URL = '/api/tanants'; // Fixed to match backend route

// Create axios instance with default config
const api = axios.create({
    headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
    },
});

// Add request interceptor to include auth token
api.interceptors.request.use(
    (config) => {
        const token = localStorage.getItem('auth_token');
        if (token) {
            config.headers.Authorization = `Bearer ${token}`;
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

// Add response interceptor for error handling
api.interceptors.response.use(
    (response) => response,
    (error) => {
        if (error.response?.status === 401) {
            // Handle unauthorized access
            localStorage.removeItem('auth_token');
            window.location.href = '/login';
        }
        return Promise.reject(error);
    }
);

const tenantsAPI = {
    // Get all tenants with pagination and filters
    getAll: async (params = {}) => {
        try {
            const response = await api.get(API_URL, { params });
            return response.data;
        } catch (error) {
            console.error('Error fetching tenants:', error);
            throw error.response?.data || error;
        }
    },

    // Get tenant by ID
    getById: async (id) => {
        try {
            const response = await api.get(`${API_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching tenant:', error);
            throw error.response?.data || error;
        }
    },

    // Create new tenant
    create: async (data, isFormData = false) => {
        try {
            const config = isFormData ? { headers: { 'Content-Type': 'multipart/form-data' } } : {};
            const response = await api.post(API_URL, data, config);
            return response.data;
        } catch (error) {
            console.error('Error creating tenant:', error);
            throw error.response?.data || error;
        }
    },

    // Update tenant
    update: async (id, data, isFormData = false) => {
        try {
            const config = isFormData ? { headers: { 'Content-Type': 'multipart/form-data' } } : {};
            const response = await api.post(`${API_URL}/${id}?_method=PUT`, data, config);
            return response.data;
        } catch (error) {
            console.error('Error updating tenant:', error);
            throw error.response?.data || error;
        }
    },

    // Delete tenant
    delete: async (id) => {
        try {
            const response = await api.delete(`${API_URL}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting tenant:', error);
            throw error.response?.data || error;
        }
    },

    // Get tenants dropdown
    getDropdown: async () => {
        try {
            const response = await api.get('/api/tanants/dropdown');
            return response.data;
        } catch (error) {
            console.error('Error fetching tenants dropdown:', error);
            throw error.response?.data || error;
        }
    },

    // Get tenants statistics
    getStatistics: async () => {
        try {
            const response = await api.get('/api/tanants-statistics');
            return response.data;
        } catch (error) {
            console.error('Error fetching tenants statistics:', error);
            throw error.response?.data || error;
        }
    },

    // Bulk status update
    bulkStatusUpdate: async (data) => {
        try {
            const response = await api.post('/api/tanants/bulk-status-update', data);
            return response.data;
        } catch (error) {
            console.error('Error updating tenants status:', error);
            throw error.response?.data || error;
        }
    },
};

export default tenantsAPI;
