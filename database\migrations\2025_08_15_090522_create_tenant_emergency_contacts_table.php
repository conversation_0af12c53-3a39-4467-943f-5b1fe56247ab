<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('tenant_emergency_contacts', function (Blueprint $table) {
            $table->id();
          $table->unsignedBigInteger('tenant_id'); // must match tenants.id
            $table->string('name');
            $table->string('relation');
            $table->string('phone');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('tenant_emergency_contacts');
    }
};
