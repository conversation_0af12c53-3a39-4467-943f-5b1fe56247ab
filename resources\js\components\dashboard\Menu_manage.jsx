import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { FileText } from "lucide-react";
import menuPositionAPI from "../../services/menuPosition";
import MenuPositionSelector from './menu-manage/MenuPositionSelector';
import AddMenuItemsPanel from './menu-manage/AddMenuItemsPanel';
import MenuStructureDisplay from './menu-manage/MenuStructureDisplay';

const MenuManage = () => {
  const [pages, setPages] = useState([]);
  const [loadingPages, setLoadingPages] = useState(false);
  const [menuPositions, setMenuPositions] = useState([]);
  const [selectedMenuPosition, setSelectedMenuPosition] = useState("");
  const [loadingMenuPositions, setLoadingMenuPositions] = useState(false);

  // Handle menu position selection (exclusive selection with checkboxes)
  const handleMenuPositionChange = (positionId) => {
    console.log('Menu position changed to:', positionId);
    setSelectedMenuPosition(positionId);
  };

  const [customLink, setCustomLink] = useState({
    title: "",
    url: "",
    description: "",
    target: "_self"
  });

  const [selectedPages, setSelectedPages] = useState([]);
  const [isSelectAll, setIsSelectAll] = useState(false);

  const [currentMenuItems, setCurrentMenuItems] = useState([]);
  const [loadingCurrentMenuItems, setLoadingCurrentMenuItems] = useState(false);
  const [loadingCustomLink, setLoadingCustomLink] = useState(false);

  // Fetch menu positions from API
  const fetchMenuPositions = async () => {
    try {
      setLoadingMenuPositions(true);
      const response = await menuPositionAPI.getAll();
      setMenuPositions(response.data || []);
    } catch (error) {
      console.error('Error fetching menu positions:', error);
    } finally {
      setLoadingMenuPositions(false);
    }
  };

  // Fetch pages from API
  const fetchPages = async () => {
    try {
      setLoadingPages(true);
      const response = await fetch('/api/menus', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Accept': 'application/json',
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPages(data.data || []);
      } else {
        console.error('Failed to fetch menus');
      }
    } catch (error) {
      console.error('Error fetching menus:', error);
    } finally {
      setLoadingPages(false);
    }
  };

  // Fetch current menu items for selected position
  const fetchCurrentMenuItems = async (positionId) => {
    if (!positionId) return;
    try {
      setLoadingCurrentMenuItems(true);
      const response = await fetch(`/api/menu-manages?menu_position_id=${positionId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Accept': 'application/json',
        },
      });
      if (response.ok) {
        const data = await response.json();
        setCurrentMenuItems(data.data || []);
      } else {
        console.error('Failed to fetch menu manages');
      }
    } catch (error) {
      console.error('Error fetching menu manages:', error);
    } finally {
      setLoadingCurrentMenuItems(false);
    }
  };

  useEffect(() => {
    fetchMenuPositions();
    fetchPages();
  }, []);

  useEffect(() => {
    if (selectedMenuPosition) {
      fetchCurrentMenuItems(selectedMenuPosition);
    } else {
      setCurrentMenuItems([]);
    }
  }, [selectedMenuPosition]);

  const handlePageSelection = (pageId) => {
    console.log('handlePageSelection called with pageId:', pageId);
    console.log('Current selectedPages:', selectedPages);
    setSelectedPages(prev => {
      const newSelected = prev.includes(pageId)
        ? prev.filter(id => id !== pageId)
        : [...prev, pageId];
      console.log('New selectedPages:', newSelected);
      setIsSelectAll(newSelected.length === pages.length);
      return newSelected;
    });
  };

  const handleSelectAll = () => {
    if (isSelectAll) {
      setSelectedPages([]);
      setIsSelectAll(false);
    } else {
      setSelectedPages(pages.map(page => page.id));
      setIsSelectAll(true);
    }
  };

  const handleCustomLinkChange = (field, value) => {
    setCustomLink(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleAddSelectedPages = async () => {
    if (!selectedMenuPosition) {
      alert("Please select a menu position first");
      return;
    }

    const selected = pages.filter(page => selectedPages.includes(page.id));

    for (const page of selected) {
      const slug = page.name.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
      const order = currentMenuItems.length + 1;

      const data = {
        menu_position_id: parseInt(selectedMenuPosition),
        title: page.name,
        slug: slug,
        type: 'page',
        target: '_self',
        parent_id: null,
        order: order,
        new_tap: 0
      };

      try {
        const res = await fetch('/api/menu-manages', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
            'Accept': 'application/json',
          },
          body: JSON.stringify(data)
        });

        if (res.ok) {
          const result = await res.json();
          setCurrentMenuItems(prev => [...prev, result.data]);
        } else {
          console.error('Failed to add page:', page.name);
        }
      } catch (e) {
        console.error('Error adding page:', e);
      }
    }

    setSelectedPages([]);
    setIsSelectAll(false);
  };

  const handleAddCustomLink = async () => {
    if (!selectedMenuPosition) {
      alert("Please select a menu position first");
      return;
    }

    if (!customLink.title || !customLink.url) {
      alert("Please fill in both title and URL for the custom link");
      return;
    }

    setLoadingCustomLink(true);

    const slug = customLink.title.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
    const order = currentMenuItems.length + 1;

    const data = {
      menu_position_id: parseInt(selectedMenuPosition),
      title: customLink.title,
      slug: slug,
      type: 'custom_link',
      target: customLink.target,
      parent_id: null,
      order: order,
      new_tap: 0
    };

    try {
      const res = await fetch('/api/menu-manages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Accept': 'application/json',
        },
        body: JSON.stringify(data)
      });

      if (res.ok) {
        const result = await res.json();
        setCurrentMenuItems(prev => [...prev, result.data]);
        // Reset form
        setCustomLink({
          title: "",
          url: "",
          description: "",
          target: "_self"
        });
      } else {
        console.error('Failed to add custom link');
      }
    } catch (e) {
      console.error('Error adding custom link:', e);
    } finally {
      setLoadingCustomLink(false);
    }
  };

  const handleDelete = async (id) => {
    try {
      const res = await fetch(`/api/menu-manages/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Accept': 'application/json',
        },
      });

      if (res.ok) {
        setCurrentMenuItems(prev => prev.filter(item => item.id !== id));
      } else {
        console.error('Failed to delete menu item');
      }
    } catch (e) {
      console.error('Error deleting menu item:', e);
    }
  };

  const handleReorder = async (reorderedItems) => {
    try {
      // Update the local state immediately for smooth UX
      setCurrentMenuItems(reorderedItems);

      // Send the reorder request to the backend
      const updatePromises = reorderedItems.map((item, index) => {
        return fetch(`/api/menu-manages/${item.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
            'Accept': 'application/json',
          },
          body: JSON.stringify({
            order: index + 1
          })
        });
      });

      // Wait for all updates to complete
      const results = await Promise.all(updatePromises);
      
      // Check if any updates failed
      const failedUpdates = results.filter(res => !res.ok);
      if (failedUpdates.length > 0) {
        console.error('Some order updates failed');
        // Refresh the current menu items to get the correct state
        fetchCurrentMenuItems(selectedMenuPosition);
      } else {
        console.log('All menu items reordered successfully');
      }
    } catch (error) {
      console.error('Error reordering menu items:', error);
      // Refresh the current menu items to get the correct state
      fetchCurrentMenuItems(selectedMenuPosition);
    }
  };

  const handleNesting = async (itemId, parentId) => {
    try {
      // Update the local state immediately for smooth UX
      setCurrentMenuItems(prev => 
        prev.map(item => 
          item.id === itemId 
            ? { ...item, parent_id: parentId }
            : item
        )
      );

      // Send the nesting request to the backend
      const res = await fetch(`/api/menu-manages/${itemId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Accept': 'application/json',
        },
        body: JSON.stringify({
          parent_id: parentId
        })
      });

      if (!res.ok) {
        console.error('Failed to update parent relationship');
        // Refresh the current menu items to get the correct state
        fetchCurrentMenuItems(selectedMenuPosition);
      } else {
        console.log('Menu item nested successfully');
      }
    } catch (error) {
      console.error('Error nesting menu item:', error);
      // Refresh the current menu items to get the correct state
      fetchCurrentMenuItems(selectedMenuPosition);
    }
  };

  return (
    <div className="space-y-6">
      {/* Menu Position Selection */}
      <MenuPositionSelector
        menuPositions={menuPositions}
        selectedMenuPosition={selectedMenuPosition}
        onMenuPositionChange={handleMenuPositionChange}
        loadingMenuPositions={loadingMenuPositions}
        onRefresh={fetchMenuPositions}
      />

      {/* Menu Management */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Menu Management
            {selectedMenuPosition && (
              <span className="text-sm font-normal text-muted-foreground">
                - {menuPositions.find(p => p.id.toString() === selectedMenuPosition)?.name}
              </span>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Left Column - Menu Items (1/3 width) */}
            <div className="lg:col-span-1 border border-gray-300 rounded-lg p-4">
              <AddMenuItemsPanel
                // Menu items props
                availableMenus={pages}
                selectedMenus={selectedPages}
                onMenuSelection={handlePageSelection}
                onSelectAllMenus={handleSelectAll}
                onDeselectAllMenus={handleSelectAll}
                onAddSelectedPages={handleAddSelectedPages}
                selectedMenuPosition={selectedMenuPosition}
                loadingPages={loadingPages}
                
                // Custom link props
                customLink={customLink}
                onCustomLinkChange={handleCustomLinkChange}
                onCustomLinkSubmit={handleAddCustomLink}
                loadingCustomLink={loadingCustomLink}
              />
            </div>

            {/* Right Column - Menu Structure Preview (2/3 width) */}
            <MenuStructureDisplay
              selectedMenuPosition={selectedMenuPosition}
              menuPositions={menuPositions}
              currentMenuItems={currentMenuItems}
              loadingCurrentMenuItems={loadingCurrentMenuItems}
              onDelete={handleDelete}
              onReorder={handleReorder}
              onNest={handleNesting}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default MenuManage;
