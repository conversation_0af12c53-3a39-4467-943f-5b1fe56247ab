import React from "react";
import { TabsContent } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";

const Header = ({ formData, handleInputChange , handleSubmit, loading}) => {
  return (
    <TabsContent value="header" className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <Label htmlFor="facebook">Facebook Link</Label>
          <Input
            id="facebook"
            value={formData.facebook || ""}
            onChange={(e) => handleInputChange('facebook', e.target.value)}
            placeholder="https://www.facebook.com/yourpage"
          />
        </div>

        <div>
          <Label htmlFor="twitter">Twitter Link</Label>
          <Input
            id="twitter"
            value={formData.twitter || ""}
            onChange={(e) => handleInputChange('twitter', e.target.value)}
            placeholder="https://www.twitter.com/yourpage"
          />
        </div>

        <div>
          <Label htmlFor="linkedin">LinkedIn Link</Label>
          <Input
            id="linkedin"
            value={formData.linkedin || ""}
            onChange={(e) => handleInputChange('linkedin', e.target.value)}
            placeholder="https://www.linkedin.com/yourpage"
          />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div>
          <Label htmlFor="youtube">YouTube Link</Label>
          <Input
            id="youtube"
            value={formData.youtube || ""}
            onChange={(e) => handleInputChange('youtube', e.target.value)}
            placeholder="https://www.youtube.com/yourpage"
          />
        </div>

        <div>
          <Label htmlFor="instagram">Instagram Link</Label>
          <Input
            id="instagram"
            value={formData.instagram || ""}
            onChange={(e) => handleInputChange('instagram', e.target.value)}
            placeholder="https://www.instagram.com/yourpage"
          />
        </div>

        <div>
          <Label htmlFor="phone">Phone</Label>
          <Input
            id="phone"
            value={formData.phone || ""}
            onChange={(e) => handleInputChange('phone', e.target.value)}
            placeholder="+990-737 621 432"
          />
        </div>
      </div>

      <div className="flex justify-end">
        <button
          onClick={handleSubmit}
          className="px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm hover:bg-blue-700 disabled:opacity-50"
          disabled={loading}
        >
          {loading ? "Saving..." : "Save Settings"}
        </button>
      </div>
    </TabsContent>
  )};
export default Header;
