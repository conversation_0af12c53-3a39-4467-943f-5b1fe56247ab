import React, { useState, useCallback } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { FileText, Save, Trash } from "lucide-react";
import DraggableMenuItem from './DraggableMenuItem';
import DropZone from './DropZone';
import HorizontalDropZone from './HorizontalDropZone';

const MenuStructureDisplay = ({
  selectedMenuPosition,
  menuPositions,
  currentMenuItems,
  loadingCurrentMenuItems,
  onDelete,
  onReorder,
  onNest
}) => {
  const [items, setItems] = useState(currentMenuItems);
  const [isDragging, setIsDragging] = useState(false);

  // Update local state when currentMenuItems changes
  React.useEffect(() => {
    setItems(currentMenuItems);
  }, [currentMenuItems]);

  // Organize items into hierarchy (parent -> children)
  const organizeItems = (items) => {
    const organized = [];
    const itemMap = {};
    
    // Create a map of all items
    items.forEach(item => {
      itemMap[item.id] = { ...item, children: [] };
    });
    
    // Organize into hierarchy
    items.forEach(item => {
      if (item.parent_id && itemMap[item.parent_id]) {
        itemMap[item.parent_id].children.push(itemMap[item.id]);
      } else {
        organized.push(itemMap[item.id]);
      }
    });
    
    return organized;
  };

  // Flatten hierarchy back to array with proper order
  const flattenItems = (organizedItems) => {
    const flattened = [];
    
    const addItem = (item, level = 0) => {
      flattened.push({ ...item, level });
      if (item.children && item.children.length > 0) {
        item.children.forEach(child => addItem(child, level + 1));
      }
    };
    
    organizedItems.forEach(item => addItem(item));
    return flattened;
  };

  const organizedItems = organizeItems(items);
  const flattenedItems = flattenItems(organizedItems);

  const moveItem = useCallback((dragIndex, hoverIndex, unnest = false) => {
    setItems((prevItems) => {
      const newItems = [...prevItems];
      const draggedItem = { ...newItems[dragIndex] };
      // Remove the dragged item from its current position
      newItems.splice(dragIndex, 1);
      // Insert it at the new position
      newItems.splice(hoverIndex, 0, draggedItem);
      // If unnesting, set parent_id to null and update backend
      if (unnest) {
        draggedItem.parent_id = null;
        // Update backend (try both null and empty string for compatibility)
        fetch(`/api/menu-manages/${draggedItem.id}`, {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
          },
          body: JSON.stringify({ parent_id: null })
        }).then(res => {
          if (!res.ok) {
            // Try with empty string if null fails
            fetch(`/api/menu-manages/${draggedItem.id}`, {
              method: 'PUT',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
              },
              body: JSON.stringify({ parent_id: "" })
            });
          }
        });
      }
      // Update the order values based on new positions
      const reorderedItems = newItems.map((item, index) => ({
        ...item,
        order: index + 1,
        parent_id: item.parent_id
      }));
      // Call the parent's reorder function to update the database
      if (onReorder) {
        onReorder(reorderedItems);
      }
      return reorderedItems;
    });
  }, [onReorder]);

  const handleNesting = useCallback((itemId, parentId) => {
    setItems((prevItems) => {
      const newItems = [...prevItems];
      const draggedItemIndex = newItems.findIndex(item => item.id === itemId);

      if (draggedItemIndex === -1) return prevItems;

      const draggedItem = { ...newItems[draggedItemIndex] };

      // Update the dragged item's parent_id
      draggedItem.parent_id = parentId;

      // Remove from current position
      newItems.splice(draggedItemIndex, 1);

      // Find parent item and insert after it
      const parentIndex = newItems.findIndex(item => item.id === parentId);
      if (parentIndex !== -1) {
        newItems.splice(parentIndex + 1, 0, draggedItem);
      } else {
        newItems.push(draggedItem);
      }

      // Update order values
      const updatedItems = newItems.map((item, index) => ({
        ...item,
        order: index + 1
      }));

      // Call the parent's nest function to update the database
      if (onNest) {
        onNest(draggedItem.id, parentId);
      }

      return updatedItems;
    });
  }, [onNest]);

  const handleDropZoneDrop = useCallback((dragIndex, dropPosition) => {
    setItems((prevItems) => {
      const newItems = [...prevItems];
      const draggedItem = newItems[dragIndex];
      
      // Remove the dragged item from its current position
      newItems.splice(dragIndex, 1);
      // Insert it at the drop position
      newItems.splice(dropPosition, 0, draggedItem);
      
      // Update the order values based on new positions
      const reorderedItems = newItems.map((item, index) => ({
        ...item,
        order: index + 1
      }));
      
      // Call the parent's reorder function to update the database
      if (onReorder) {
        onReorder(reorderedItems);
      }
      
      return reorderedItems;
    });
  }, [onReorder]);

  const selectedPosition = menuPositions.find(p => p.id.toString() === selectedMenuPosition);

  return (
    <div className="space-y-4 lg:col-span-2">
      <h3 className="text-lg font-semibold">Menu Structure</h3>
      
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Current Menu Items</CardTitle>
        </CardHeader>
        <CardContent>
          {!selectedMenuPosition ? (
            <div className="text-center text-muted-foreground py-8">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Please select a menu position first.</p>
              <p className="text-sm">Choose a menu position from the dropdown above to start building your menu.</p>
            </div>
          ) : loadingCurrentMenuItems ? (
            <div className="text-center text-muted-foreground py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 mx-auto mb-4"></div>
              <p>Loading menu items...</p>
            </div>
          ) : items.length === 0 ? (
            <div className="text-center text-muted-foreground py-8">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No menu items added yet.</p>
              <p className="text-sm">Use the accordions on the left to add pages or custom links.</p>
            </div>
          ) : (
            <div className="space-y-1">
              <div className="text-sm text-muted-foreground mb-3 flex items-center gap-2 p-2 bg-blue-50 rounded-lg border border-blue-200">
                <span>🔄</span>
                <span>Drag vertically to reorder • Drag to the green drop zones on the right to create child menus</span>
              </div>
              
              {/* Drop zone at the top */}
              <DropZone 
                onDrop={handleDropZoneDrop}
                position={0}
              />
              
              {flattenedItems.map((item, index) => (
                <React.Fragment key={item.id}>
                  <DraggableMenuItem
                    item={item}
                    index={index}
                    moveItem={moveItem}
                    onDelete={onDelete}
                    level={item.level || 0}
                    allItems={items}
                    onDragStart={() => setIsDragging(true)}
                    onDragEnd={() => setIsDragging(false)}
                  />
                  
                  {/* Horizontal drop zone for nesting - show for items that can be parents */}
                  <HorizontalDropZone
                    onNest={handleNesting}
                    parentItem={item}
                    isVisible={true}
                  />
                  
                  {/* Vertical drop zone after each item */}
                  <DropZone 
                    onDrop={handleDropZoneDrop}
                    position={index + 1}
                    isLast={index === flattenedItems.length - 1}
                  />
                </React.Fragment>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
      
      <div className="flex justify-end">
        <Button
          size="sm"
          disabled={!selectedMenuPosition}
        >
          <Save className="h-4 w-4 mr-2" />
          Save Menu
          {selectedPosition && (
            <span className="ml-2 text-sm">
              ({selectedPosition.name})
            </span>
          )}
        </Button>
      </div>
    </div>
  );
};

export default MenuStructureDisplay;