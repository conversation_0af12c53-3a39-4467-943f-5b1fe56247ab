import axios from 'axios';

const API_URL = '/api/menus';

// Create axios instance with default config
const api = axios.create({
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
});

// Add request interceptor to include auth token 
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

// Menu API functions
const menuAPI = {
  // Get all menus with pagination and filters
  getAll: async (params = {}) => {
    try {
      const response = await api.get(API_URL, { params });
      return response.data;
    } catch (error) {
      console.error('Error fetching menus:', error);
      throw error.response?.data || error;
    }
  },

  // Get menu by ID
  getById: async (id) => {
    try {
      const response = await api.get(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error fetching menu:', error);
      throw error.response?.data || error;
    }
  },

  // Create new menu
  create: async (data) => {
    try {
      const response = await api.post(API_URL, data);
      return response.data;
    } catch (error) {
      console.error('Error creating menu:', error);
      throw error.response?.data || error;
    }
  },

  // Update menu
  update: async (id, data) => {
    try {
      const response = await api.put(`${API_URL}/${id}`, data);
      return response.data;
    } catch (error) {
      console.error('Error updating menu:', error);
      throw error.response?.data || error;
    }
  },

  // Delete menu
  delete: async (id) => {
    try {
      const response = await api.delete(`${API_URL}/${id}`);
      return response.data;
    } catch (error) {
      console.error('Error deleting menu:', error);
      throw error.response?.data || error;
    }
  },

  
  reorder: async (menus) => {
    try {
      // menus should be an array of objects { id, position }
      const response = await api.post(`${API_URL}/reorder`, { menus });
      return response.data;
    } catch (error) {
      console.error('Error reordering menus:', error);
      throw error.response?.data || error;
    }
  }
};

export default menuAPI;
