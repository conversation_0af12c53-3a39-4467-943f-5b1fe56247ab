<?php

namespace App\Models;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Support\Str;

use Illuminate\Database\Eloquent\Model;

class PaymentType extends Model
{
    //
       use HasFactory;
    protected $fillable = [
        'name',
        'description',
        'is_active',
       
      
    ];

    protected $casts = [
        'is_active' => 'boolean',
        
    ];
    protected $attributes = [
        'is_active' => true,
       
    ];
    // Scopes
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order')->orderBy('name');
    }
    
  
    // Removed unnecessary attribute accessors to prevent recursion errors

}
