const Timezone = [
  { value: "(GMT-12:00) Etc/GMT+12", label: "(GMT-12:00) Etc/GMT+12" },
  { value: "(GMT-11:00) Etc/GMT+11", label: "(GMT-11:00) Etc/GMT+11" },
  { value: "(GMT-10:00) Etc/GMT+10", label: "(GMT-10:00) Etc/GMT+10" },
  { value: "(GMT-09:00) Etc/GMT+9", label: "(GMT-09:00) Etc/GMT+9" },
  { value: "(GMT-08:00) Etc/GMT+8", label: "(GMT-08:00) Etc/GMT+8" },
  { value: "(GMT-07:00) Etc/GMT+7", label: "(GMT-07:00) Etc/GMT+7" },
  { value: "(GMT-06:00) Etc/GMT+6", label: "(GMT-06:00) Etc/GMT+6" },
  { value: "(GMT-05:00) Etc/GMT+5", label: "(GMT-05:00) Etc/GMT+5" },
  { value: "(GMT-04:00) Etc/GMT+4", label: "(GMT-04:00) Etc/GMT+4" },
  { value: "(GMT-03:00) Etc/GMT+3", label: "(GMT-03:00) Etc/GMT+3" },
  { value: "(GMT-02:00) Etc/GMT+2", label: "(GMT-02:00) Etc/GMT+2" },
  { value: "(GMT-01:00) Etc/GMT+1", label: "(GMT-01:00) Etc/GMT+1" },
  { value: "(GMT+00:00) Etc/GMT", label: "(GMT+00:00) Etc/GMT" },
  { value: "(GMT+01:00) Etc/GMT-1", label: "(GMT+01:00) Etc/GMT-1" },
  { value: "(GMT+02:00) Etc/GMT-2", label: "(GMT+02:00) Etc/GMT-2" },
  { value: "(GMT+03:00) Etc/GMT-3", label: "(GMT+03:00) Etc/GMT-3" },
  { value: "(GMT+04:00) Etc/GMT-4", label: "(GMT+04:00) Etc/GMT-4" },
  { value: "(GMT+05:00) Etc/GMT-5", label: "(GMT+05:00) Etc/GMT-5" },
  { value: "(GMT+06:00) Etc/GMT-6", label: "(GMT+06:00) Etc/GMT-6" },
  { value: "(GMT+07:00) Etc/GMT-7", label: "(GMT+07:00) Etc/GMT-7" },
  { value: "(GMT+08:00) Etc/GMT-8", label: "(GMT+08:00) Etc/GMT-8" },
  { value: "(GMT+09:00) Etc/GMT-9", label: "(GMT+09:00) Etc/GMT-9" },
  { value: "(GMT+10:00) Etc/GMT-10", label: "(GMT+10:00) Etc/GMT-10" },
  { value: "(GMT+11:00) Etc/GMT-11", label: "(GMT+11:00) Etc/GMT-11" },
  { value: "(GMT+12:00) Etc/GMT-12", label: "(GMT+12:00) Etc/GMT-12" },
  { value: "(GMT+13:00) Etc/GMT-13", label: "(GMT+13:00) Etc/GMT-13" },
  { value: "(GMT+14:00) Etc/GMT-14", label: "(GMT+14:00) Etc/GMT-14" }
];

export default Timezone;
