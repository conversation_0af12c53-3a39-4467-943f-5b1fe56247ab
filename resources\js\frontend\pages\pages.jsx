import React, { useEffect, useState, useMemo } from "react";
import { useParams } from "react-router-dom";
import menuAPI from "../../services/MenuAPI";
import pageContentAPI from "../../services/pageContentAPI";
import HomeAbout from "../components/pages/Home/home-about";
import Slider from "../components/pages/Home/slider";
import PropertyByCities from "../components/pages/Home/propertyByCities";

const Pages = () => {
  const { slug } = useParams();
  const [pageId, setPageId] = useState(null);
  const [error, setError] = useState(null);
  const [pageContents, setPageContents] = useState([]);

  // Fetch menu by slug
  useEffect(() => {
    if (!slug) return;
    const fetchMenu = async () => {
      try {
        const data = await menuAPI.getBySlug(slug);
        setPageId(data.data?.id || null);
      } catch (err) {
        setError(err.error || "Menu not found");
      }
    };
    fetchMenu();
  }, [slug]);

  // Fetch page contents by pageId
  useEffect(() => {
    if (!pageId) return;
    const fetchPageContents = async () => {
      try {
        const data = await pageContentAPI.getByPageId(pageId);
        setPageContents(Array.isArray(data.data) ? data.data : []);
      } catch (err) {
        setError("Failed to fetch page contents");
      }
    };
    fetchPageContents();
  }, [pageId]);

 
  const parseWidgetProps = (widgetId) => {
    const widget = pageContents.find((item) => item.widget_id === widgetId);
    if (widget?.pageContent) {
      try {
        return typeof widget.pageContent === "string"
          ? JSON.parse(widget.pageContent)
          : widget.pageContent;
      } catch {
        return {};
      }
    }
    return {};
  };

  // Extract widget props with useMemo for performance
  const sliderProps = useMemo(() => parseWidgetProps(1), [pageContents]);
  const aboutProps = useMemo(() => parseWidgetProps(4), [pageContents]);
  
  const propertyByCitiesProps = useMemo(() => parseWidgetProps(6), [pageContents]);

  return (
    <div className="page-container">
      {/* Show error message */}
      {error && (
        <div className="alert alert-danger text-center my-3">{error}</div>
      )}

      {/* Slider Section */}
      {Object.keys(sliderProps).length > 0 && (
        <Slider 
         pageContents={pageContents} />
      )}
      {/* PropertyByCities Section */}
      {Object.keys(propertyByCitiesProps).length > 0 && (
        <PropertyByCities
         pageContents={pageContents} />
      )}

      {/* About Section */}
      {Object.keys(aboutProps).length > 0 && (
        <HomeAbout 
         pageContents={pageContents} />
      )}

    

     
    </div>
  );
};

export default Pages;
