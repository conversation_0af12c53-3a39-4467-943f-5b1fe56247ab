import React, { useEffect, useState, useMemo } from "react";
import { useParams } from "react-router-dom";
import menuAPI from "../../services/MenuAPI";
import pageContentAPI from "../../services/pageContentAPI";
import HomeAbout from "../components/pages/Home/home-about";
import Slider from "../components/pages/Home/slider";
import PropertyByCities from "../components/pages/Home/propertyByCities";
import FeaturedProperties from "../components/pages/Home/featuredProperties";
import WhyChooseUs from "../components/pages/Home/whyChooseUs";
import HowItWorks from "../components/pages/Home/howItWorks";
import RecentProperties from "../components/pages/Home/recentProperties";
import Advertise from "../components/pages/Home/advertise";
import Testimonial from "../components/pages/Home/testimonial";
import Partners from "../components/pages/Home/partners";
import Blog from "../components/pages/Home/blog";

const Pages = () => {
  const { slug } = useParams();
  const [pageId, setPageId] = useState(null);
  const [error, setError] = useState(null);
  const [pageContents, setPageContents] = useState([]);

  // Memoized sorted pageContents by order_index ascending
  const sortedPageContents = useMemo(() => {
    return [...pageContents].sort((a, b) => (a.order_index ?? 0) - (b.order_index ?? 0));
  }, [pageContents]);

  // Fetch menu by slug
  useEffect(() => {
    if (!slug) return;
    const fetchMenu = async () => {
      try {
        const data = await menuAPI.getBySlug(slug);
        setPageId(data.data?.id || null);
      } catch (err) {
        setError(err.error || "Menu not found");
      }
    };
    fetchMenu();
  }, [slug]);

  // Fetch page contents by pageId
  useEffect(() => {
    if (!pageId) return;
    const fetchPageContents = async () => {
      try {
        const data = await pageContentAPI.getByPageId(pageId);
        setPageContents(Array.isArray(data.data) ? data.data : []);
      } catch (err) {
        setError("Failed to fetch page contents");
      }
    };
    fetchPageContents();
  }, [pageId]);

 
  const parseWidgetProps = (widgetId) => {
    const widget = pageContents.find((item) => item.widget_id === widgetId);
    if (widget?.pageContent) {
      try {
        return typeof widget.pageContent === "string"
          ? JSON.parse(widget.pageContent)
          : widget.pageContent;
      } catch {
        return {};
      }
    }
    return {};
  };

  // Extract widget props with useMemo for performance
  // Widget component map
  const widgetComponentMap = {
    1: Slider,
    4: HomeAbout,
    8: FeaturedProperties,
    6: PropertyByCities,
    7: WhyChooseUs,
    9: HowItWorks,
    10: RecentProperties,
    11: Advertise,
    12: Testimonial,
    13: Partners,
    14: Blog,
  };
  return (
    <div className="page-container">
      {/* Show error message */}
      {error && (
        <div className="alert alert-danger text-center my-3">{error}</div>
      )}

      {/* Render widgets in order_index ascending */}
      {sortedPageContents.map((item, idx) => {
        const WidgetComponent = widgetComponentMap[item.widget_id];
        if (!WidgetComponent) return null;
        return (
          <WidgetComponent key={item.widget_id + '-' + idx} pageContents={pageContents} />
        );
      })}
    </div>
  );
};

export default Pages;
