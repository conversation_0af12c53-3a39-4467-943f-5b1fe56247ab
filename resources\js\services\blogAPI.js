import apiClient from './apiClient';

const blogAPI = {
  // Get all blogs with pagination and filters
  getBlogs: (params = {}) => {
    const queryParams = new URLSearchParams();
    
    // Default pagination
    queryParams.append('page', params.page || 1);
    queryParams.append('per_page', params.per_page || 10);
    
    // Filters
    if (params.search) queryParams.append('search', params.search);
    if (params.status) queryParams.append('status', params.status);
    if (params.category_id) queryParams.append('category_id', params.category_id);
    if (params.is_featured !== undefined) queryParams.append('is_featured', params.is_featured);
    if (params.author) queryParams.append('author', params.author);
    
    // Sorting
    if (params.sort_by) queryParams.append('sort_by', params.sort_by);
    if (params.sort_order) queryParams.append('sort_order', params.sort_order);
    
    return apiClient.get(`/api/blogs?${queryParams.toString()}`);
  },

  // Get single blog by ID
  getBlog: (id) => {
    return apiClient.get(`/api/blogs/${id}`);
  },

  // Get blog by slug
  getBlogBySlug: (slug) => {
    return apiClient.get(`/api/blogs/slug/${slug}`);
  },

  // Create new blog
  createBlog: (blogData) => {
    const formData = new FormData();
    
    // Add text fields
    Object.keys(blogData).forEach(key => {
      if (key === 'featured_image' && blogData[key] instanceof File) {
        formData.append(key, blogData[key]);
      } else if (key === 'tags') {
        // Send tags as individual array items
        if (Array.isArray(blogData[key])) {
          blogData[key].forEach((tag, index) => {
            formData.append(`tags[${index}]`, tag);
          });
        }
      } else if (key === 'seo_keywords') {
        // Send seo_keywords as individual array items
        if (Array.isArray(blogData[key])) {
          blogData[key].forEach((keyword, index) => {
            formData.append(`seo_keywords[${index}]`, keyword);
          });
        }
      } else if (key === 'is_featured') {
        // Convert boolean to string for FormData
        formData.append(key, blogData[key] ? '1' : '0');
      } else if (blogData[key] !== null && blogData[key] !== undefined) {
        formData.append(key, blogData[key]);
      }
    });

    return apiClient.post('/api/blogs', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Update blog
  updateBlog: (id, blogData) => {
    const formData = new FormData();
    
    // Add _method for Laravel to handle PUT via POST
    formData.append('_method', 'PUT');
    
    // Add text fields
    Object.keys(blogData).forEach(key => {
      if (key === 'featured_image' && blogData[key] instanceof File) {
        formData.append(key, blogData[key]);
      } else if (key === 'tags') {
        // Send tags as individual array items
        if (Array.isArray(blogData[key])) {
          blogData[key].forEach((tag, index) => {
            formData.append(`tags[${index}]`, tag);
          });
        }
      } else if (key === 'seo_keywords') {
        // Send seo_keywords as individual array items
        if (Array.isArray(blogData[key])) {
          blogData[key].forEach((keyword, index) => {
            formData.append(`seo_keywords[${index}]`, keyword);
          });
        }
      } else if (key === 'is_featured') {
        // Convert boolean to string for FormData
        formData.append(key, blogData[key] ? '1' : '0');
      } else if (blogData[key] !== null && blogData[key] !== undefined) {
        formData.append(key, blogData[key]);
      }
    });

    return apiClient.post(`/api/blogs/${id}`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Delete blog
  deleteBlog: (id) => {
    return apiClient.delete(`/api/blogs/${id}`);
  },

  // Bulk delete blogs
  bulkDeleteBlogs: (ids) => {
    return apiClient.post('/api/blogs/bulk-delete', { ids });
  },

  // Update blog status
  updateStatus: (id, status) => {
    return apiClient.patch(`/api/blogs/${id}/status`, { status });
  },

  // Toggle featured status
  toggleFeatured: (id) => {
    return apiClient.patch(`/api/blogs/${id}/toggle-featured`);
  },

  // Get blog statistics
  getStatistics: () => {
    return apiClient.get('/api/blogs/statistics');
  },

  // Get published blogs for frontend
  getPublishedBlogs: (params = {}) => {
    const queryParams = new URLSearchParams();
    
    queryParams.append('page', params.page || 1);
    queryParams.append('per_page', params.per_page || 10);
    
    if (params.search) queryParams.append('search', params.search);
    if (params.category_id) queryParams.append('category_id', params.category_id);
    if (params.featured_only) queryParams.append('featured_only', true);
    
    return apiClient.get(`/api/frontend/blogs?${queryParams.toString()}`);
  },

  // Get related blogs
  getRelatedBlogs: (blogId, limit = 3) => {
    return apiClient.get(`/api/blogs/${blogId}/related?limit=${limit}`);
  },

  // Increment blog views
  incrementViews: (id) => {
    return apiClient.post(`/api/blogs/${id}/views`);
  },

  // Get blog categories (using property types)
  getCategories: () => {
    return apiClient.get('/api/property-types-dropdown');
  }
};

export default blogAPI;
