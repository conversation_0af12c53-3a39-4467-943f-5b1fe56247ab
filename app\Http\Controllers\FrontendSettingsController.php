<?php

namespace App\Http\Controllers;

use Illuminate\Http\JsonResponse;
use App\Models\FrontendSettings;
use Illuminate\Http\Request;

class FrontendSettingsController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(): JsonResponse
    {
        try {
            $settings = FrontendSettings::first();
            return response()->json([
                'success' => true,
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created resource in storage.
     */
   public function store(Request $request): JsonResponse
        {
          
 
            try {
                $data = [];
                // Handle logo file if present
                if ($request->hasFile('logo')) {
                    $file = $request->file('logo');
                    if ($file->isValid()) {
                        $filename = 'logo_' . time() . '.' . $file->getClientOriginalExtension();
                        $destination = public_path('settings/image');
                        if (!file_exists($destination)) {
                            mkdir($destination, 0775, true);
                        }
                        $file->move($destination, $filename);
                        $data['logo'] = '/settings/image/' . $filename;
                    }
                }

                if ($request->hasFile('footerLogo')) {
                    $file = $request->file('footerLogo');
                    if ($file->isValid()) {
                        $filename = 'footerLogo_' . time() . '.' . $file->getClientOriginalExtension();
                        $destination = public_path('settings/image');
                        if (!file_exists($destination)) {
                            mkdir($destination, 0775, true);
                        }
                        $file->move($destination, $filename);
                        $data['footerLogo'] = '/settings/image/' . $filename;
                    }
                }
                 if ($request->hasFile('BreadcumImage')) {
                    $file = $request->file('BreadcumImage');
                    if ($file->isValid()) {
                        $filename = 'BreadcumImage_' . time() . '.' . $file->getClientOriginalExtension();
                        $destination = public_path('settings/image');
                        if (!file_exists($destination)) {
                            mkdir($destination, 0775, true);
                        }
                        $file->move($destination, $filename);
                        $data['BreadcumImage'] = '/settings/image/' . $filename;
                    }
                }

                // Fix: Use lowerdcase 'metaImage' for consistency
                if ($request->hasFile('metaImage')) {

                    $file = $request->file('metaImage');
                    if ($file->isValid()) {
                        $filename = 'metaImage_' . time() . '.' . $file->getClientOriginalExtension();
                        $destination = public_path('settings/image');
                        if (!file_exists($destination)) {
                            mkdir($destination, 0775, true);
                        }
                        $file->move($destination, $filename);
                        $data['metaImage'] = '/settings/image/' . $filename;
                    }
                }


                // Other fields
                $data['phone'] = $request->input('phone', null);
                $data['addPropertyButton'] = $request->input('addPropertyButton', 0);
                $data['btnUrl'] = $request->input('btnUrl', null);
                $data['showPreloader'] = $request->input('showPreloader', 0);
                $data['showTopBar'] = $request->input('showTopBar', 0);
                $data['primaryColor'] = $request->input('primaryColor', null);
                $data['secondaryColor'] = $request->input('secondaryColor', null);
                $data['facebook'] = $request->input('facebook', null);
                $data['twitter'] = $request->input('twitter', null);
                $data['youtube'] = $request->input('youtube', null);
                $data['instagram'] = $request->input('instagram', null);
                $data['linkedin'] = $request->input('linkedin', null);
                $data['footerPhone'] = $request->input('footerPhone', null);
                $data['footerEmail'] = $request->input('footerEmail', null);
                $data['copyRightText'] = $request->input('copyRightText', null);
                $data['breadcumColor'] = $request->input('breadcumColor', null);
                $data['MetaTitle'] = $request->input('MetaTitle', null);
                $data['meta_key_word'] = $request->input('meta_key_word', null);
                $data['metaDescription'] = $request->input('metaDescription', null);
                $data['googleAnalytics'] = $request->input('googleAnalytics', null);
                $data['googleClientID'] = $request->input('googleClientID', null);
                $data['googleClientSecret'] = $request->input('googleClientSecret', null);
                $data['googleRedirectionUrl'] = $request->input('googleRedirectionUrl', null);
                $data['googleLoginClientId'] = $request->input('googleLoginClientId', null);
                $data['googleLoginClientSecret'] = $request->input('googleLoginClientSecret', null);
                $data['googleLoginRedirectionUrl'] = $request->input('googleLoginRedirectionUrl', null);
                // If settings exist, update; otherwise, create
                $settings = FrontendSettings::first();
                if ($settings) {
                    $settings->update($data);
                } else {
                    $settings = FrontendSettings::create($data);
                }

                return response()->json([
                    'success' => true,
                    'data' => $settings
                ], 201);
            } catch (\Exception $e) {
                \Log::error('FrontendSettingsController@store error', ['error' => $e->getMessage()]);
                return response()->json([
                    'success' => false,
                    'message' => 'Failed to save settings: ' . $e->getMessage()
                ], 500);
            }
        }
    /**
     * Display the specified resource.
     */
    public function show(FrontendSettings $frontendSettings): JsonResponse
    {
        try {
            return response()->json([
                'success' => true,
                'data' => $frontendSettings
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch settings: ' . $e->getMessage()
            ], 500);
        }
    }

   
    /**
     * Remove the specified resource from storage.
     */
    public function destroy(FrontendSettings $frontendSettings): JsonResponse
    {
        try {
            $frontendSettings->delete();
            return response()->json([
                'success' => true,
                'message' => 'Settings deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to delete settings: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Upload logo for frontend settings.
     */
    public function uploadLogo(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'logo' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048'
            ]);

            $settings = FrontendSettings::first();
            if (!$settings) {
                $settings = new FrontendSettings();
            }

            if ($request->hasFile('logo')) {
                $file = $request->file('logo');
                $filename = 'logo_' . time() . '.' . $file->getClientOriginalExtension();
                $file->move(public_path('settings/image'), $filename);
                $settings->logo = '/settings/image/' . $filename;
                $settings->save();
            }

            return response()->json([
                'success' => true,
                'message' => 'Logo uploaded successfully',
                'data' => $settings
            ]);
        } catch (\Exception $e) {
            \Log::error('FrontendSettingsController@uploadLogo error', ['error' => $e->getMessage()]);
            return response()->json([
                'success' => false,
                'message' => 'Failed to upload logo: ' . $e->getMessage()
            ], 500);
        }
    }
}
